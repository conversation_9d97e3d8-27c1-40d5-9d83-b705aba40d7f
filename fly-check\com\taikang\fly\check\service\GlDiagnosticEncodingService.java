package com.taikang.fly.check.service.GlDiagnosticEncodingService;
import java.lang.Object;
import com.taikang.fly.check.dto.diagnosticencoding.DiagnosticSearchDto;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.util.ObjectUtils;
import java.lang.String;
import java.lang.CharSequence;
import org.apache.commons.lang3.StringUtils;
import java.lang.Integer;
import com.taikang.fly.check.comm.NativePage;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import java.util.List;
import com.taikang.fly.check.mybatis.dao.GlDiagnosticEncodingDao;
import com.taikang.fly.check.dto.mapstruct.DiagnosticEncodingMapping;

public class GlDiagnosticEncodingService	// class@0002e3 from classes.dex
{
    private DiagnosticEncodingMapping diagnosticEncodingMapping;
    private GlDiagnosticEncodingDao glDiagnosticEncodingDao;

    public void GlDiagnosticEncodingService(){
       super();
    }
    private QueryWrapper getQueryWrapper(DiagnosticSearchDto diagnosticSearchDto){
       QueryWrapper queryWrapper = new QueryWrapper();
       if (!ObjectUtils.isEmpty(diagnosticSearchDto)) {
          if (StringUtils.isNotBlank(diagnosticSearchDto.getDiagnosisCode())) {
             queryWrapper.eq("diagnosis_code", diagnosticSearchDto.getDiagnosisCode());
          }
          if (StringUtils.isNotBlank(diagnosticSearchDto.getDiagnosisName())) {
             queryWrapper.like("diagnosis_name", diagnosticSearchDto.getDiagnosisName());
          }
       }
       return queryWrapper;
    }
    public NativePage queryList(Integer page,Integer size,DiagnosticSearchDto diagnosticSearchDto){
       QueryWrapper queryWrapper = this.getQueryWrapper(diagnosticSearchDto);
       Page records = PageHelper.startPage(page.intValue(), size.intValue(), true);
       List glDiagnosticEncodings = this.glDiagnosticEncodingDao.selectList(queryWrapper);
       List diagnosticEncodingDtos = this.diagnosticEncodingMapping.toGlDtoList(glDiagnosticEncodings);
       NativePage pageDto = new NativePage(diagnosticEncodingDtos, Integer.valueOf((int)records.getTotal()), Integer.valueOf(records.getPageSize()), Integer.valueOf(records.getPageNum()), records.getStartRow());
       return pageDto;
    }
}
