package com.taikang.fly.check.dto.match.MatchingData$OffStandard;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class MatchingData$OffStandard	// class@000189 from classes.dex
{
    private String columnType;
    private String stanColumn;
    private String tabColumn;
    private String tabName;

    public void MatchingData$OffStandard(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MatchingData$OffStandard;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof MatchingData$OffStandard) {
             b = false;
          }else {
             MatchingData$OffStandard offStandard = o;
             if (!offStandard.canEqual(this)) {
                b = false;
             }else {
                String tabName = this.getTabName();
                String tabName1 = offStandard.getTabName();
                if (tabName == null) {
                   if (tabName1 != null) {
                      b = false;
                   }
                }else if(tabName.equals(tabName1)){
                }
                String tabColumn = this.getTabColumn();
                String tabColumn1 = offStandard.getTabColumn();
                if (tabColumn == null) {
                   if (tabColumn1 != null) {
                      b = false;
                   }
                }else if(tabColumn.equals(tabColumn1)){
                }
                String columnType = this.getColumnType();
                String columnType1 = offStandard.getColumnType();
                if (columnType == null) {
                   if (columnType1 != null) {
                      b = false;
                   }
                }else if(columnType.equals(columnType1)){
                }
                String stanColumn = this.getStanColumn();
                String stanColumn1 = offStandard.getStanColumn();
                if (stanColumn == null) {
                   if (stanColumn1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!stanColumn.equals(stanColumn1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getColumnType(){
       return this.columnType;
    }
    public String getStanColumn(){
       return this.stanColumn;
    }
    public String getTabColumn(){
       return this.tabColumn;
    }
    public String getTabName(){
       return this.tabName;
    }
    public int hashCode(){
       String $tabName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($tabName = this.getTabName()) == null)? i: $tabName.hashCode();
       result = i1 + 59;
       String $tabColumn = this.getTabColumn();
       int i2 = result * 59;
       i1 = ($tabColumn == null)? i: $tabColumn.hashCode();
       result = i2 + i1;
       String $columnType = this.getColumnType();
       i2 = result * 59;
       i1 = ($columnType == null)? i: $columnType.hashCode();
       result = i2 + i1;
       String $stanColumn = this.getStanColumn();
       i1 = result * 59;
       if ($stanColumn != null) {
          i = $stanColumn.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setColumnType(String columnType){
       this.columnType = columnType;
    }
    public void setStanColumn(String stanColumn){
       this.stanColumn = stanColumn;
    }
    public void setTabColumn(String tabColumn){
       this.tabColumn = tabColumn;
    }
    public void setTabName(String tabName){
       this.tabName = tabName;
    }
    public String toString(){
       return "MatchingData.OffStandard\(tabName="+this.getTabName()+", tabColumn="+this.getTabColumn()+", columnType="+this.getColumnType()+", stanColumn="+this.getStanColumn()+"\)";
    }
}
