package com.taikang.fly.check.vo.MdcsFundSetlListOprnVo;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class MdcsFundSetlListOprnVo implements Serializable	// class@00035a from classes.dex
{
    private String oprnOprtCode;
    private String oprnOprtName;

    public void MdcsFundSetlListOprnVo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MdcsFundSetlListOprnVo;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof MdcsFundSetlListOprnVo) {
             b = false;
          }else {
             MdcsFundSetlListOprnVo mdcsFundSetl = o;
             if (!mdcsFundSetl.canEqual(this)) {
                b = false;
             }else {
                String oprnOprtCode = this.getOprnOprtCode();
                String oprnOprtCode1 = mdcsFundSetl.getOprnOprtCode();
                if (oprnOprtCode == null) {
                   if (oprnOprtCode1 != null) {
                      b = false;
                   }
                }else if(oprnOprtCode.equals(oprnOprtCode1)){
                }
                String oprnOprtName = this.getOprnOprtName();
                String oprnOprtName1 = mdcsFundSetl.getOprnOprtName();
                if (oprnOprtName == null) {
                   if (oprnOprtName1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!oprnOprtName.equals(oprnOprtName1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getOprnOprtCode(){
       return this.oprnOprtCode;
    }
    public String getOprnOprtName(){
       return this.oprnOprtName;
    }
    public int hashCode(){
       String $oprnOprtCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($oprnOprtCode = this.getOprnOprtCode()) == null)? i: $oprnOprtCode.hashCode();
       result = i1 + 59;
       String $oprnOprtName = this.getOprnOprtName();
       i1 = result * 59;
       if ($oprnOprtName != null) {
          i = $oprnOprtName.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setOprnOprtCode(String oprnOprtCode){
       this.oprnOprtCode = oprnOprtCode;
    }
    public void setOprnOprtName(String oprnOprtName){
       this.oprnOprtName = oprnOprtName;
    }
    public String toString(){
       return "MdcsFundSetlListOprnVo\(oprnOprtCode="+this.getOprnOprtCode()+", oprnOprtName="+this.getOprnOprtName()+"\)";
    }
}
