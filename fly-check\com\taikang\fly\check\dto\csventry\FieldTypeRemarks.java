package com.taikang.fly.check.dto.csventry.FieldTypeRemarks;
import java.lang.Object;
import java.lang.Integer;
import java.lang.String;
import java.lang.StringBuilder;

public class FieldTypeRemarks	// class@0000db from classes.dex
{
    private Integer dataLength;
    private String dataType;
    private String dateFormat;
    private Integer decimalLength;
    private Integer id;
    private String notes;

    public void FieldTypeRemarks(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FieldTypeRemarks;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof FieldTypeRemarks){
          b = false;
       }else {
          FieldTypeRemarks uFieldTypeRe = o;
          if (!uFieldTypeRe.canEqual(this)) {
             b = false;
          }else {
             Integer id = this.getId();
             Integer id1 = uFieldTypeRe.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String dataType = this.getDataType();
             String dataType1 = uFieldTypeRe.getDataType();
             if (dataType == null) {
                if (dataType1 != null) {
                   b = false;
                }
             }else if(dataType.equals(dataType1)){
             }
             Integer dataLength = this.getDataLength();
             Integer dataLength1 = uFieldTypeRe.getDataLength();
             if (dataLength == null) {
                if (dataLength1 != null) {
                   b = false;
                }
             }else if(dataLength.equals(dataLength1)){
             }
             Integer decimalLengt = this.getDecimalLength();
             Integer decimalLengt1 = uFieldTypeRe.getDecimalLength();
             if (decimalLengt == null) {
                if (decimalLengt1 != null) {
                   b = false;
                }
             }else if(decimalLengt.equals(decimalLengt1)){
             }
             String dateFormat = this.getDateFormat();
             String dateFormat1 = uFieldTypeRe.getDateFormat();
             if (dateFormat == null) {
                if (dateFormat1 != null) {
                   b = false;
                }
             }else if(dateFormat.equals(dateFormat1)){
             }
             String notes = this.getNotes();
             String notes1 = uFieldTypeRe.getNotes();
             if (notes == null) {
                if (notes1 != null) {
                   b = false;
                }
             }else if(notes.equals(notes1)){
             }
             b = true;
          }
       }
       return b;
    }
    public Integer getDataLength(){
       return this.dataLength;
    }
    public String getDataType(){
       return this.dataType;
    }
    public String getDateFormat(){
       return this.dateFormat;
    }
    public Integer getDecimalLength(){
       return this.decimalLength;
    }
    public Integer getId(){
       return this.id;
    }
    public String getNotes(){
       return this.notes;
    }
    public int hashCode(){
       Integer $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $dataType = this.getDataType();
       int i2 = result * 59;
       i1 = ($dataType == null)? i: $dataType.hashCode();
       result = i2 + i1;
       Integer $dataLength = this.getDataLength();
       i2 = result * 59;
       i1 = ($dataLength == null)? i: $dataLength.hashCode();
       result = i2 + i1;
       Integer $decimalLength = this.getDecimalLength();
       i2 = result * 59;
       i1 = ($decimalLength == null)? i: $decimalLength.hashCode();
       result = i2 + i1;
       String $dateFormat = this.getDateFormat();
       i2 = result * 59;
       i1 = ($dateFormat == null)? i: $dateFormat.hashCode();
       result = i2 + i1;
       String notes = this.getNotes();
       i1 = result * 59;
       if (notes != null) {
          i = notes.hashCode();
       }
       return (i1 + i);
    }
    public void setDataLength(Integer dataLength){
       this.dataLength = dataLength;
    }
    public void setDataType(String dataType){
       this.dataType = dataType;
    }
    public void setDateFormat(String dateFormat){
       this.dateFormat = dateFormat;
    }
    public void setDecimalLength(Integer decimalLength){
       this.decimalLength = decimalLength;
    }
    public void setId(Integer id){
       this.id = id;
    }
    public void setNotes(String notes){
       this.notes = notes;
    }
    public String toString(){
       return "FieldTypeRemarks\(id="+this.getId()+", dataType="+this.getDataType()+", dataLength="+this.getDataLength()+", decimalLength="+this.getDecimalLength()+", dateFormat="+this.getDateFormat()+", notes="+this.getNotes()+"\)";
    }
}
