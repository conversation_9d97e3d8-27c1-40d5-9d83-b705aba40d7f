package com.taikang.fly.check.dto.menu.MenuDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.lang.StringBuilder;

public class MenuDto implements Serializable	// class@00018e from classes.dex
{
    private String aclass;
    private List children;
    private String faArrow;
    private String iclass;
    private String id;
    private boolean isLeaf;
    private boolean isOpen;
    private int level;
    private String menuDesc;
    private String menuName;
    private String menuOrder;
    private String navabel;
    private String target;
    private String url;
    private static final long serialVersionUID = 0x21e31acb44c4c6a7;

    public void MenuDto(){
       super();
       this.isOpen = false;
       this.isLeaf = false;
    }
    protected boolean canEqual(Object other){
       return other instanceof MenuDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof MenuDto){
          b = false;
       }else {
          MenuDto menuDto = o;
          if (!menuDto.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = menuDto.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String menuName = this.getMenuName();
             String menuName1 = menuDto.getMenuName();
             if (menuName == null) {
                if (menuName1 != null) {
                   b = false;
                }
             }else if(menuName.equals(menuName1)){
             }
             String menuDesc = this.getMenuDesc();
             String menuDesc1 = menuDto.getMenuDesc();
             if (menuDesc == null) {
                if (menuDesc1 != null) {
                   b = false;
                }
             }else if(menuDesc.equals(menuDesc1)){
             }
             String url = this.getUrl();
             String url1 = menuDto.getUrl();
             if (url == null) {
                if (url1 != null) {
                   b = false;
                }
             }else if(url.equals(url1)){
             }
             String iclass = this.getIclass();
             String iclass1 = menuDto.getIclass();
             if (iclass == null) {
                if (iclass1 != null) {
                   b = false;
                }
             }else if(iclass.equals(iclass1)){
             }
             String aclass = this.getAclass();
             String aclass1 = menuDto.getAclass();
             if (aclass == null) {
                if (aclass1 != null) {
                label_00a3 :
                   b = false;
                }
             }else if(aclass.equals(aclass1)){
             }
             String navabel = this.getNavabel();
             String navabel1 = menuDto.getNavabel();
             if (navabel == null) {
                if (navabel1 != null) {
                   b = false;
                }
             }else if(navabel.equals(navabel1)){
             }
             String faArrow = this.getFaArrow();
             String faArrow1 = menuDto.getFaArrow();
             if (faArrow == null) {
                if (faArrow1 != null) {
                label_00d1 :
                   b = false;
                }
             }else if(faArrow.equals(faArrow1)){
             }
             if (this.isOpen() != menuDto.isOpen()) {
                b = false;
             }else if(this.isLeaf() != menuDto.isLeaf()){
                b = false;
             }else if(this.getLevel() != menuDto.getLevel()){
                b = false;
             }else {
                String tar = this.getTarget();
                String tar1 = menuDto.getTarget();
                if (tar == null) {
                   if (tar1 != null) {
                      b = false;
                   }
                }else if(tar.equals(tar1)){
                }
                List children = this.getChildren();
                List children1 = menuDto.getChildren();
                if (children == null) {
                   if (children1 != null) {
                   label_0137 :
                      b = false;
                   }
                }else if(children.equals(children1)){
                }
                String menuOrder = this.getMenuOrder();
                String menuOrder1 = menuDto.getMenuOrder();
                if (menuOrder == null) {
                   if (menuOrder1 != null) {
                      b = false;
                   }
                }else if(menuOrder.equals(menuOrder1)){
                }
                b = true;
             }
          }
       }
       return b;
    }
    public String getAclass(){
       return this.aclass;
    }
    public List getChildren(){
       return this.children;
    }
    public String getFaArrow(){
       return this.faArrow;
    }
    public String getIclass(){
       return this.iclass;
    }
    public String getId(){
       return this.id;
    }
    public int getLevel(){
       return this.level;
    }
    public String getMenuDesc(){
       return this.menuDesc;
    }
    public String getMenuName(){
       return this.menuName;
    }
    public String getMenuOrder(){
       return this.menuOrder;
    }
    public String getNavabel(){
       return this.navabel;
    }
    public String getTarget(){
       return this.target;
    }
    public String getUrl(){
       return this.url;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $menuName = this.getMenuName();
       int i1 = result * 59;
       i = ($menuName == null)? 43: $menuName.hashCode();
       result = i1 + i;
       String $menuDesc = this.getMenuDesc();
       i1 = result * 59;
       i = ($menuDesc == null)? 43: $menuDesc.hashCode();
       result = i1 + i;
       String $url = this.getUrl();
       i1 = result * 59;
       i = ($url == null)? 43: $url.hashCode();
       result = i1 + i;
       String $iclass = this.getIclass();
       i1 = result * 59;
       i = ($iclass == null)? 43: $iclass.hashCode();
       result = i1 + i;
       String aclass = this.getAclass();
       i1 = result * 59;
       i = (aclass == null)? 43: aclass.hashCode();
       String navabel = this.getNavabel();
       i1 = (i1 + i) * 59;
       i = (navabel == null)? 43: navabel.hashCode();
       String faArrow = this.getFaArrow();
       i1 = (i1 + i) * 59;
       i = (faArrow == null)? 43: faArrow.hashCode();
       i1 = (i1 + i) * 59;
       i = (this.isOpen())? 79: 97;
       i1 = (i1 + i) * 59;
       i = (this.isLeaf())? 79: 97;
       String tar = this.getTarget();
       i1 = (((i1 + i) * 59) + this.getLevel()) * 59;
       i = (tar == null)? 43: tar.hashCode();
       List children = this.getChildren();
       i1 = (i1 + i) * 59;
       i = (children == null)? 43: children.hashCode();
       String menuOrder = this.getMenuOrder();
       i1 = (i1 + i) * 59;
       i = (menuOrder == null)? 43: menuOrder.hashCode();
       return (i1 + i);
    }
    public boolean isLeaf(){
       return this.isLeaf;
    }
    public boolean isOpen(){
       return this.isOpen;
    }
    public void setAclass(String aclass){
       this.aclass = aclass;
    }
    public void setChildren(List children){
       this.children = children;
    }
    public void setFaArrow(String faArrow){
       this.faArrow = faArrow;
    }
    public void setIclass(String iclass){
       this.iclass = iclass;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setLeaf(boolean isLeaf){
       this.isLeaf = isLeaf;
    }
    public void setLevel(int level){
       this.level = level;
    }
    public void setMenuDesc(String menuDesc){
       this.menuDesc = menuDesc;
    }
    public void setMenuName(String menuName){
       this.menuName = menuName;
    }
    public void setMenuOrder(String menuOrder){
       this.menuOrder = menuOrder;
    }
    public void setNavabel(String navabel){
       this.navabel = navabel;
    }
    public void setOpen(boolean isOpen){
       this.isOpen = isOpen;
    }
    public void setTarget(String target){
       this.target = target;
    }
    public void setUrl(String url){
       this.url = url;
    }
    public String toString(){
       return "MenuDto\(id="+this.getId()+", menuName="+this.getMenuName()+", menuDesc="+this.getMenuDesc()+", url="+this.getUrl()+", iclass="+this.getIclass()+", aclass="+this.getAclass()+", navabel="+this.getNavabel()+", faArrow="+this.getFaArrow()+", isOpen="+this.isOpen()+", isLeaf="+this.isLeaf()+", level="+this.getLevel()+", target="+this.getTarget()+", children="+this.getChildren()+", menuOrder="+this.getMenuOrder()+"\)";
    }
}
