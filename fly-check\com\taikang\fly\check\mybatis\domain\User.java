package com.taikang.fly.check.mybatis.domain.User;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.time.LocalDateTime;
import java.lang.Integer;
import java.lang.StringBuilder;

public class User implements Serializable	// class@000271 from classes.dex
{
    private LocalDateTime createTime;
    private String creator;
    private Integer failureTimes;
    private String id;
    private LocalDateTime lockTime;
    private String modby;
    private LocalDateTime modifyTime;
    private String name;
    private String password;
    private String region;
    private String status;
    private String userCode;
    private static final long serialVersionUID = 0x1;

    public void User(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof User;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof User){
          b = false;
       }else {
          User user = o;
          if (!user.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = user.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String userCode = this.getUserCode();
             String userCode1 = user.getUserCode();
             if (userCode == null) {
                if (userCode1 != null) {
                   b = false;
                }
             }else if(userCode.equals(userCode1)){
             }
             String name = this.getName();
             String name1 = user.getName();
             if (name == null) {
                if (name1 != null) {
                   b = false;
                }
             }else if(name.equals(name1)){
             }
             String password = this.getPassword();
             String password1 = user.getPassword();
             if (password == null) {
                if (password1 != null) {
                   b = false;
                }
             }else if(password.equals(password1)){
             }
             String region = this.getRegion();
             String region1 = user.getRegion();
             if (region == null) {
                if (region1 != null) {
                label_008b :
                   b = false;
                }
             }else if(region.equals(region1)){
             }
             String status = this.getStatus();
             String status1 = user.getStatus();
             if (status == null) {
                if (status1 != null) {
                   b = false;
                }
             }else if(status.equals(status1)){
             }
             LocalDateTime lockTime = this.getLockTime();
             LocalDateTime lockTime1 = user.getLockTime();
             if (lockTime == null) {
                if (lockTime1 != null) {
                   b = false;
                }
             }else if(lockTime.equals(lockTime1)){
             }
             Integer failureTimes = this.getFailureTimes();
             Integer failureTimes1 = user.getFailureTimes();
             if (failureTimes == null) {
                if (failureTimes1 != null) {
                   b = false;
                }
             }else if(failureTimes.equals(failureTimes1)){
             }
             String creator = this.getCreator();
             String creator1 = user.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                label_00eb :
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             LocalDateTime createTime = this.getCreateTime();
             LocalDateTime createTime1 = user.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modby = this.getModby();
             String modby1 = user.getModby();
             if (modby == null) {
                if (modby1 != null) {
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             LocalDateTime modifyTime = this.getModifyTime();
             LocalDateTime modifyTime1 = user.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             b = true;
          }
       }
       return b;
    }
    public LocalDateTime getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public Integer getFailureTimes(){
       return this.failureTimes;
    }
    public String getId(){
       return this.id;
    }
    public LocalDateTime getLockTime(){
       return this.lockTime;
    }
    public String getModby(){
       return this.modby;
    }
    public LocalDateTime getModifyTime(){
       return this.modifyTime;
    }
    public String getName(){
       return this.name;
    }
    public String getPassword(){
       return this.password;
    }
    public String getRegion(){
       return this.region;
    }
    public String getStatus(){
       return this.status;
    }
    public String getUserCode(){
       return this.userCode;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $userCode = this.getUserCode();
       int i1 = result * 59;
       i = ($userCode == null)? 43: $userCode.hashCode();
       result = i1 + i;
       String $name = this.getName();
       i1 = result * 59;
       i = ($name == null)? 43: $name.hashCode();
       result = i1 + i;
       String $password = this.getPassword();
       i1 = result * 59;
       i = ($password == null)? 43: $password.hashCode();
       result = i1 + i;
       String $region = this.getRegion();
       i1 = result * 59;
       i = ($region == null)? 43: $region.hashCode();
       result = i1 + i;
       String status = this.getStatus();
       i1 = result * 59;
       i = (status == null)? 43: status.hashCode();
       LocalDateTime lockTime = this.getLockTime();
       i1 = (i1 + i) * 59;
       i = (lockTime == null)? 43: lockTime.hashCode();
       Integer failureTimes = this.getFailureTimes();
       i1 = (i1 + i) * 59;
       i = (failureTimes == null)? 43: failureTimes.hashCode();
       String creator = this.getCreator();
       i1 = (i1 + i) * 59;
       i = (creator == null)? 43: creator.hashCode();
       LocalDateTime createTime = this.getCreateTime();
       i1 = (i1 + i) * 59;
       i = (createTime == null)? 43: createTime.hashCode();
       String modby = this.getModby();
       i1 = (i1 + i) * 59;
       i = (modby == null)? 43: modby.hashCode();
       LocalDateTime modifyTime = this.getModifyTime();
       i1 = (i1 + i) * 59;
       i = (modifyTime == null)? 43: modifyTime.hashCode();
       return (i1 + i);
    }
    public User setCreateTime(LocalDateTime createTime){
       this.createTime = createTime;
       return this;
    }
    public User setCreator(String creator){
       this.creator = creator;
       return this;
    }
    public User setFailureTimes(Integer failureTimes){
       this.failureTimes = failureTimes;
       return this;
    }
    public User setId(String id){
       this.id = id;
       return this;
    }
    public User setLockTime(LocalDateTime lockTime){
       this.lockTime = lockTime;
       return this;
    }
    public User setModby(String modby){
       this.modby = modby;
       return this;
    }
    public User setModifyTime(LocalDateTime modifyTime){
       this.modifyTime = modifyTime;
       return this;
    }
    public User setName(String name){
       this.name = name;
       return this;
    }
    public User setPassword(String password){
       this.password = password;
       return this;
    }
    public User setRegion(String region){
       this.region = region;
       return this;
    }
    public User setStatus(String status){
       this.status = status;
       return this;
    }
    public User setUserCode(String userCode){
       this.userCode = userCode;
       return this;
    }
    public String toString(){
       return "User\(id="+this.getId()+", userCode="+this.getUserCode()+", name="+this.getName()+", password="+this.getPassword()+", region="+this.getRegion()+", status="+this.getStatus()+", lockTime="+this.getLockTime()+", failureTimes="+this.getFailureTimes()+", creator="+this.getCreator()+", createTime="+this.getCreateTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+"\)";
    }
}
