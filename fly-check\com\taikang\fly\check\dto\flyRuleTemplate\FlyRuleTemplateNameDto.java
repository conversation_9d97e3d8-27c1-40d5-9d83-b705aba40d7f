package com.taikang.fly.check.dto.flyRuleTemplate.FlyRuleTemplateNameDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class FlyRuleTemplateNameDto implements Serializable	// class@000119 from classes.dex
{
    private String count;
    private String ruleCategory2;
    private static final long serialVersionUID = 0x1;

    public void FlyRuleTemplateNameDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRuleTemplateNameDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof FlyRuleTemplateNameDto) {
             b = false;
          }else {
             FlyRuleTemplateNameDto uFlyRuleTemp = o;
             if (!uFlyRuleTemp.canEqual(this)) {
                b = false;
             }else {
                String ruleCategory = this.getRuleCategory2();
                String ruleCategory1 = uFlyRuleTemp.getRuleCategory2();
                if (ruleCategory == null) {
                   if (ruleCategory1 != null) {
                      b = false;
                   }
                }else if(ruleCategory.equals(ruleCategory1)){
                }
                String count = this.getCount();
                String count1 = uFlyRuleTemp.getCount();
                if (count == null) {
                   if (count1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!count.equals(count1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getCount(){
       return this.count;
    }
    public String getRuleCategory2(){
       return this.ruleCategory2;
    }
    public int hashCode(){
       String $ruleCategory2;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($ruleCategory2 = this.getRuleCategory2()) == null)? i: $ruleCategory2.hashCode();
       result = i1 + 59;
       String $count = this.getCount();
       i1 = result * 59;
       if ($count != null) {
          i = $count.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setCount(String count){
       this.count = count;
    }
    public void setRuleCategory2(String ruleCategory2){
       this.ruleCategory2 = ruleCategory2;
    }
    public String toString(){
       return "FlyRuleTemplateNameDto\(ruleCategory2="+this.getRuleCategory2()+", count="+this.getCount()+"\)";
    }
}
