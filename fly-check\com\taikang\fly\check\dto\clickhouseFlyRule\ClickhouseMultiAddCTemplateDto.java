package com.taikang.fly.check.dto.clickhouseFlyRule.ClickhouseMultiAddCTemplateDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.lang.StringBuilder;

public class ClickhouseMultiAddCTemplateDto implements Serializable	// class@0000c8 from classes.dex
{
    private List args;
    private String diagnosisType;
    private List multiArgs;
    private String ps;
    private String ruleCategory1;
    private String ruleCategory2;
    private String ruleDescribe;
    private String ruleLevel;
    private String ruleName;
    private String ruleType;
    private String sqlName;
    private String sqlTemplate;
    private static final long serialVersionUID = 0x1;

    public void ClickhouseMultiAddCTemplateDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ClickhouseMultiAddCTemplateDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ClickhouseMultiAddCTemplateDto){
          b = false;
       }else {
          ClickhouseMultiAddCTemplateDto uClickhouseM = o;
          if (!uClickhouseM.canEqual(this)) {
             b = false;
          }else {
             String ruleName = this.getRuleName();
             String ruleName1 = uClickhouseM.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String sqlTemplate = this.getSqlTemplate();
             String sqlTemplate1 = uClickhouseM.getSqlTemplate();
             if (sqlTemplate == null) {
                if (sqlTemplate1 != null) {
                   b = false;
                }
             }else if(sqlTemplate.equals(sqlTemplate1)){
             }
             String sqlName = this.getSqlName();
             String sqlName1 = uClickhouseM.getSqlName();
             if (sqlName == null) {
                if (sqlName1 != null) {
                   b = false;
                }
             }else if(sqlName.equals(sqlName1)){
             }
             List args = this.getArgs();
             List args1 = uClickhouseM.getArgs();
             if (args == null) {
                if (args1 != null) {
                   b = false;
                }
             }else if(args.equals(args1)){
             }
             List multiArgs = this.getMultiArgs();
             List multiArgs1 = uClickhouseM.getMultiArgs();
             if (multiArgs == null) {
                if (multiArgs1 != null) {
                   b = false;
                }
             }else if(multiArgs.equals(multiArgs1)){
             }
             String ruleType = this.getRuleType();
             String ruleType1 = uClickhouseM.getRuleType();
             if (ruleType == null) {
                if (ruleType1 != null) {
                   b = false;
                }
             }else if(ruleType.equals(ruleType1)){
             }
             String ps = this.getPs();
             String ps1 = uClickhouseM.getPs();
             if (ps == null) {
                if (ps1 != null) {
                label_00b9 :
                   b = false;
                }
             }else if(ps.equals(ps1)){
             }
             String ruleCategory = this.getRuleCategory1();
             String ruleCategory1 = uClickhouseM.getRuleCategory1();
             if (ruleCategory == null) {
                if (ruleCategory1 != null) {
                   b = false;
                }
             }else if(ruleCategory.equals(ruleCategory1)){
             }
             String ruleCategory2 = this.getRuleCategory2();
             String ruleCategory3 = uClickhouseM.getRuleCategory2();
             if (ruleCategory2 == null) {
                if (ruleCategory3 != null) {
                label_00e9 :
                   b = false;
                }
             }else if(ruleCategory2.equals(ruleCategory3)){
             }
             String diagnosisTyp = this.getDiagnosisType();
             String diagnosisTyp1 = uClickhouseM.getDiagnosisType();
             if (diagnosisTyp == null) {
                if (diagnosisTyp1 != null) {
                   b = false;
                }
             }else if(diagnosisTyp.equals(diagnosisTyp1)){
             }
             String ruleDescribe = this.getRuleDescribe();
             String ruleDescribe1 = uClickhouseM.getRuleDescribe();
             if (ruleDescribe == null) {
                if (ruleDescribe1 != null) {
                label_0119 :
                   b = false;
                }
             }else if(ruleDescribe.equals(ruleDescribe1)){
             }
             String ruleLevel = this.getRuleLevel();
             String ruleLevel1 = uClickhouseM.getRuleLevel();
             if (ruleLevel == null) {
                if (ruleLevel1 != null) {
                   b = false;
                }
             }else if(ruleLevel.equals(ruleLevel1)){
             }
             b = true;
          }
       }
       return b;
    }
    public List getArgs(){
       return this.args;
    }
    public String getDiagnosisType(){
       return this.diagnosisType;
    }
    public List getMultiArgs(){
       return this.multiArgs;
    }
    public String getPs(){
       return this.ps;
    }
    public String getRuleCategory1(){
       return this.ruleCategory1;
    }
    public String getRuleCategory2(){
       return this.ruleCategory2;
    }
    public String getRuleDescribe(){
       return this.ruleDescribe;
    }
    public String getRuleLevel(){
       return this.ruleLevel;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleType(){
       return this.ruleType;
    }
    public String getSqlName(){
       return this.sqlName;
    }
    public String getSqlTemplate(){
       return this.sqlTemplate;
    }
    public int hashCode(){
       String $ruleName;
       int PRIME = 59;
       int result = 1;
       int i = (($ruleName = this.getRuleName()) == null)? 43: $ruleName.hashCode();
       result = i + 59;
       String $sqlTemplate = this.getSqlTemplate();
       int i1 = result * 59;
       i = ($sqlTemplate == null)? 43: $sqlTemplate.hashCode();
       result = i1 + i;
       String $sqlName = this.getSqlName();
       i1 = result * 59;
       i = ($sqlName == null)? 43: $sqlName.hashCode();
       result = i1 + i;
       List $args = this.getArgs();
       i1 = result * 59;
       i = ($args == null)? 43: $args.hashCode();
       result = i1 + i;
       List $multiArgs = this.getMultiArgs();
       i1 = result * 59;
       i = ($multiArgs == null)? 43: $multiArgs.hashCode();
       result = i1 + i;
       String ruleType = this.getRuleType();
       i1 = result * 59;
       i = (ruleType == null)? 43: ruleType.hashCode();
       String ps = this.getPs();
       i1 = (i1 + i) * 59;
       i = (ps == null)? 43: ps.hashCode();
       String ruleCategory = this.getRuleCategory1();
       i1 = (i1 + i) * 59;
       i = (ruleCategory == null)? 43: ruleCategory.hashCode();
       String ruleCategory1 = this.getRuleCategory2();
       i1 = (i1 + i) * 59;
       i = (ruleCategory1 == null)? 43: ruleCategory1.hashCode();
       String diagnosisTyp = this.getDiagnosisType();
       i1 = (i1 + i) * 59;
       i = (diagnosisTyp == null)? 43: diagnosisTyp.hashCode();
       String ruleDescribe = this.getRuleDescribe();
       i1 = (i1 + i) * 59;
       i = (ruleDescribe == null)? 43: ruleDescribe.hashCode();
       String ruleLevel = this.getRuleLevel();
       i1 = (i1 + i) * 59;
       i = (ruleLevel == null)? 43: ruleLevel.hashCode();
       return (i1 + i);
    }
    public void setArgs(List args){
       this.args = args;
    }
    public void setDiagnosisType(String diagnosisType){
       this.diagnosisType = diagnosisType;
    }
    public void setMultiArgs(List multiArgs){
       this.multiArgs = multiArgs;
    }
    public void setPs(String ps){
       this.ps = ps;
    }
    public void setRuleCategory1(String ruleCategory1){
       this.ruleCategory1 = ruleCategory1;
    }
    public void setRuleCategory2(String ruleCategory2){
       this.ruleCategory2 = ruleCategory2;
    }
    public void setRuleDescribe(String ruleDescribe){
       this.ruleDescribe = ruleDescribe;
    }
    public void setRuleLevel(String ruleLevel){
       this.ruleLevel = ruleLevel;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleType(String ruleType){
       this.ruleType = ruleType;
    }
    public void setSqlName(String sqlName){
       this.sqlName = sqlName;
    }
    public void setSqlTemplate(String sqlTemplate){
       this.sqlTemplate = sqlTemplate;
    }
    public String toString(){
       return "ClickhouseMultiAddCTemplateDto\(ruleName="+this.getRuleName()+", sqlTemplate="+this.getSqlTemplate()+", sqlName="+this.getSqlName()+", args="+this.getArgs()+", multiArgs="+this.getMultiArgs()+", ruleType="+this.getRuleType()+", ps="+this.getPs()+", ruleCategory1="+this.getRuleCategory1()+", ruleCategory2="+this.getRuleCategory2()+", diagnosisType="+this.getDiagnosisType()+", ruleDescribe="+this.getRuleDescribe()+", ruleLevel="+this.getRuleLevel()+"\)";
    }
}
