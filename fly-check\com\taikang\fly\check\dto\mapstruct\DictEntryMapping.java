package com.taikang.fly.check.dto.mapstruct.DictEntryMapping;
import com.taikang.fly.check.dto.csventry.CSVHeaderEntry;
import com.taikang.fly.check.dto.csventry.CsvConfigEntry;
import com.taikang.fly.check.mybatis.domain.DictEntry;
import java.util.List;
import com.taikang.fly.check.dto.dictEntry.DictEntryDto;

public interface abstract DictEntryMapping	// class@00014a from classes.dex
{

    CsvConfigEntry entryToConfig(CSVHeaderEntry p0);
    DictEntry toDto(DictEntry p0);
    List toDtoList(List p0);
    DictEntry toEntity(DictEntryDto p0);
    List toEntityList(List p0);
}
