package com.taikang.fly.check.mybatis.domain.Module;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import java.time.LocalDateTime;
import java.lang.StringBuilder;

public class Module implements Serializable	// class@000260 from classes.dex
{
    private LocalDateTime createdTime;
    private String creator;
    private String description;
    private String icon;
    private String isValid;
    private String modifier;
    private LocalDateTime modifyTime;
    private String moduleCode;
    private String moduleName;
    private Integer moduleOrder;
    private String moduleType;
    private String url;
    private static final long serialVersionUID = 0x1;

    public void Module(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof Module;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof Module){
          b = false;
       }else {
          Module module = o;
          if (!module.canEqual(this)) {
             b = false;
          }else {
             String moduleCode = this.getModuleCode();
             String moduleCode1 = module.getModuleCode();
             if (moduleCode == null) {
                if (moduleCode1 != null) {
                   b = false;
                }
             }else if(moduleCode.equals(moduleCode1)){
             }
             String moduleName = this.getModuleName();
             String moduleName1 = module.getModuleName();
             if (moduleName == null) {
                if (moduleName1 != null) {
                   b = false;
                }
             }else if(moduleName.equals(moduleName1)){
             }
             Integer moduleOrder = this.getModuleOrder();
             Integer moduleOrder1 = module.getModuleOrder();
             if (moduleOrder == null) {
                if (moduleOrder1 != null) {
                   b = false;
                }
             }else if(moduleOrder.equals(moduleOrder1)){
             }
             String moduleType = this.getModuleType();
             String moduleType1 = module.getModuleType();
             if (moduleType == null) {
                if (moduleType1 != null) {
                   b = false;
                }
             }else if(moduleType.equals(moduleType1)){
             }
             String description = this.getDescription();
             String description1 = module.getDescription();
             if (description == null) {
                if (description1 != null) {
                   b = false;
                }
             }else if(description.equals(description1)){
             }
             String icon = this.getIcon();
             String icon1 = module.getIcon();
             if (icon == null) {
                if (icon1 != null) {
                label_00a3 :
                   b = false;
                }
             }else if(icon.equals(icon1)){
             }
             String url = this.getUrl();
             String url1 = module.getUrl();
             if (url == null) {
                if (url1 != null) {
                   b = false;
                }
             }else if(url.equals(url1)){
             }
             String creator = this.getCreator();
             String creator1 = module.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                label_00d3 :
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             LocalDateTime createdTime = this.getCreatedTime();
             LocalDateTime createdTime1 = module.getCreatedTime();
             if (createdTime == null) {
                if (createdTime1 != null) {
                   b = false;
                }
             }else if(createdTime.equals(createdTime1)){
             }
             String modifier = this.getModifier();
             String modifier1 = module.getModifier();
             if (modifier == null) {
                if (modifier1 != null) {
                   b = false;
                }
             }else if(modifier.equals(modifier1)){
             }
             LocalDateTime modifyTime = this.getModifyTime();
             LocalDateTime modifyTime1 = module.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String isValid = this.getIsValid();
             String isValid1 = module.getIsValid();
             if (isValid == null) {
                if (isValid1 != null) {
                label_0131 :
                   b = false;
                }
             }else if(isValid.equals(isValid1)){
             }
             b = true;
          }
       }
       return b;
    }
    public LocalDateTime getCreatedTime(){
       return this.createdTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getDescription(){
       return this.description;
    }
    public String getIcon(){
       return this.icon;
    }
    public String getIsValid(){
       return this.isValid;
    }
    public String getModifier(){
       return this.modifier;
    }
    public LocalDateTime getModifyTime(){
       return this.modifyTime;
    }
    public String getModuleCode(){
       return this.moduleCode;
    }
    public String getModuleName(){
       return this.moduleName;
    }
    public Integer getModuleOrder(){
       return this.moduleOrder;
    }
    public String getModuleType(){
       return this.moduleType;
    }
    public String getUrl(){
       return this.url;
    }
    public int hashCode(){
       String $moduleCode;
       int PRIME = 59;
       int result = 1;
       int i = (($moduleCode = this.getModuleCode()) == null)? 43: $moduleCode.hashCode();
       result = i + 59;
       String $moduleName = this.getModuleName();
       int i1 = result * 59;
       i = ($moduleName == null)? 43: $moduleName.hashCode();
       result = i1 + i;
       Integer $moduleOrder = this.getModuleOrder();
       i1 = result * 59;
       i = ($moduleOrder == null)? 43: $moduleOrder.hashCode();
       result = i1 + i;
       String $moduleType = this.getModuleType();
       i1 = result * 59;
       i = ($moduleType == null)? 43: $moduleType.hashCode();
       result = i1 + i;
       String $description = this.getDescription();
       i1 = result * 59;
       i = ($description == null)? 43: $description.hashCode();
       result = i1 + i;
       String icon = this.getIcon();
       i1 = result * 59;
       i = (icon == null)? 43: icon.hashCode();
       String url = this.getUrl();
       i1 = (i1 + i) * 59;
       i = (url == null)? 43: url.hashCode();
       String creator = this.getCreator();
       i1 = (i1 + i) * 59;
       i = (creator == null)? 43: creator.hashCode();
       LocalDateTime createdTime = this.getCreatedTime();
       i1 = (i1 + i) * 59;
       i = (createdTime == null)? 43: createdTime.hashCode();
       String modifier = this.getModifier();
       i1 = (i1 + i) * 59;
       i = (modifier == null)? 43: modifier.hashCode();
       LocalDateTime modifyTime = this.getModifyTime();
       i1 = (i1 + i) * 59;
       i = (modifyTime == null)? 43: modifyTime.hashCode();
       String isValid = this.getIsValid();
       i1 = (i1 + i) * 59;
       i = (isValid == null)? 43: isValid.hashCode();
       return (i1 + i);
    }
    public Module setCreatedTime(LocalDateTime createdTime){
       this.createdTime = createdTime;
       return this;
    }
    public Module setCreator(String creator){
       this.creator = creator;
       return this;
    }
    public Module setDescription(String description){
       this.description = description;
       return this;
    }
    public Module setIcon(String icon){
       this.icon = icon;
       return this;
    }
    public Module setIsValid(String isValid){
       this.isValid = isValid;
       return this;
    }
    public Module setModifier(String modifier){
       this.modifier = modifier;
       return this;
    }
    public Module setModifyTime(LocalDateTime modifyTime){
       this.modifyTime = modifyTime;
       return this;
    }
    public Module setModuleCode(String moduleCode){
       this.moduleCode = moduleCode;
       return this;
    }
    public Module setModuleName(String moduleName){
       this.moduleName = moduleName;
       return this;
    }
    public Module setModuleOrder(Integer moduleOrder){
       this.moduleOrder = moduleOrder;
       return this;
    }
    public Module setModuleType(String moduleType){
       this.moduleType = moduleType;
       return this;
    }
    public Module setUrl(String url){
       this.url = url;
       return this;
    }
    public String toString(){
       return "Module\(moduleCode="+this.getModuleCode()+", moduleName="+this.getModuleName()+", moduleOrder="+this.getModuleOrder()+", moduleType="+this.getModuleType()+", description="+this.getDescription()+", icon="+this.getIcon()+", url="+this.getUrl()+", creator="+this.getCreator()+", createdTime="+this.getCreatedTime()+", modifier="+this.getModifier()+", modifyTime="+this.getModifyTime()+", isValid="+this.getIsValid()+"\)";
    }
}
