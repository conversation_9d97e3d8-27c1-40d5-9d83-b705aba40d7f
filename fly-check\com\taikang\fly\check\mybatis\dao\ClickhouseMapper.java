package com.taikang.fly.check.mybatis.dao.ClickhouseMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.Map;
import java.util.List;
import java.lang.String;
import com.taikang.fly.check.mybatis.domain.ClickhouseFlyRule;
import java.lang.Integer;

public interface abstract ClickhouseMapper implements BaseMapper	// class@0001dc from classes.dex
{

    List findFlyRulePage(Map p0);
    List findformulateFlyRulePage(Map p0);
    List findformulateTemplateFlyRulePage(Map p0);
    List getIdsByCreater(String p0,String p1);
    List getStatisticsRule();
    List queryFlyRuleByRegion(Map p0);
    List queryRepeatData(Map p0);
    ClickhouseFlyRule selectByPrimaryKey(String p0);
    List selectByRegionAndByRuleName(String p0,String p1);
    List selectByRegionAndByRuleNameList(String p0,String p1);
    String selectCountByName(String p0,String p1);
    List selectDepartList(String p0);
    List selectIdListByRegion(String p0);
    List selectListRegion();
    List selectMzDepartList(String p0);
    Integer selectRuleCount(String p0);
    int updateByPrimaryKey(ClickhouseFlyRule p0);
    int updateFlyRuleById(ClickhouseFlyRule p0);
}
