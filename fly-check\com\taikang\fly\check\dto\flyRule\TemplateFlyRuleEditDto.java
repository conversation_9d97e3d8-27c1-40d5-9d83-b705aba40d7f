package com.taikang.fly.check.dto.flyRule.TemplateFlyRuleEditDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.util.Date;
import java.lang.StringBuilder;

public class TemplateFlyRuleEditDto implements Serializable	// class@00010f from classes.dex
{
    private List args;
    private String diagnosisType;
    private String id;
    private String isSpecial;
    private String newSqlName;
    private String policyBasis;
    private String ps;
    private String ruleCategory1;
    private String ruleCategory2;
    private String ruleDescribe;
    private String ruleDimension;
    private String ruleLevel;
    private String ruleName;
    private String ruleScopeApply;
    private Date ruleSuitTimeEnd;
    private Date ruleSuitTimeStart;
    private String sqlTemplate;
    private String writeRuleName;
    private static final long serialVersionUID = 0x1;

    public void TemplateFlyRuleEditDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof TemplateFlyRuleEditDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof TemplateFlyRuleEditDto){
          b = false;
       }else {
          TemplateFlyRuleEditDto templateFlyR = o;
          if (!templateFlyR.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = templateFlyR.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String ruleName = this.getRuleName();
             String ruleName1 = templateFlyR.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String writeRuleNam = this.getWriteRuleName();
             String writeRuleNam1 = templateFlyR.getWriteRuleName();
             if (writeRuleNam == null) {
                if (writeRuleNam1 != null) {
                   b = false;
                }
             }else if(writeRuleNam.equals(writeRuleNam1)){
             }
             String sqlTemplate = this.getSqlTemplate();
             String sqlTemplate1 = templateFlyR.getSqlTemplate();
             if (sqlTemplate == null) {
                if (sqlTemplate1 != null) {
                   b = false;
                }
             }else if(sqlTemplate.equals(sqlTemplate1)){
             }
             String newSqlName = this.getNewSqlName();
             String newSqlName1 = templateFlyR.getNewSqlName();
             if (newSqlName == null) {
                if (newSqlName1 != null) {
                   b = false;
                }
             }else if(newSqlName.equals(newSqlName1)){
             }
             List args = this.getArgs();
             List args1 = templateFlyR.getArgs();
             if (args == null) {
                if (args1 != null) {
                   b = false;
                }
             }else if(args.equals(args1)){
             }
             String ps = this.getPs();
             String ps1 = templateFlyR.getPs();
             if (ps == null) {
                if (ps1 != null) {
                label_00bf :
                   b = false;
                }
             }else if(ps.equals(ps1)){
             }
             String ruleCategory = this.getRuleCategory1();
             String ruleCategory1 = templateFlyR.getRuleCategory1();
             if (ruleCategory == null) {
                if (ruleCategory1 != null) {
                   b = false;
                }
             }else if(ruleCategory.equals(ruleCategory1)){
             }
             String ruleCategory2 = this.getRuleCategory2();
             String ruleCategory3 = templateFlyR.getRuleCategory2();
             if (ruleCategory2 == null) {
                if (ruleCategory3 != null) {
                label_00ef :
                   b = false;
                }
             }else if(ruleCategory2.equals(ruleCategory3)){
             }
             String diagnosisTyp = this.getDiagnosisType();
             String diagnosisTyp1 = templateFlyR.getDiagnosisType();
             if (diagnosisTyp == null) {
                if (diagnosisTyp1 != null) {
                   b = false;
                }
             }else if(diagnosisTyp.equals(diagnosisTyp1)){
             }
             String ruleDescribe = this.getRuleDescribe();
             String ruleDescribe1 = templateFlyR.getRuleDescribe();
             if (ruleDescribe == null) {
                if (ruleDescribe1 != null) {
                label_011f :
                   b = false;
                }
             }else if(ruleDescribe.equals(ruleDescribe1)){
             }
             String ruleLevel = this.getRuleLevel();
             String ruleLevel1 = templateFlyR.getRuleLevel();
             if (ruleLevel == null) {
                if (ruleLevel1 != null) {
                   b = false;
                }
             }else if(ruleLevel.equals(ruleLevel1)){
             }
             String policyBasis = this.getPolicyBasis();
             String policyBasis1 = templateFlyR.getPolicyBasis();
             if (policyBasis == null) {
                if (policyBasis1 != null) {
                label_014f :
                   b = false;
                }
             }else if(policyBasis.equals(policyBasis1)){
             }
             String isSpecial = this.getIsSpecial();
             String isSpecial1 = templateFlyR.getIsSpecial();
             if (isSpecial == null) {
                if (isSpecial1 != null) {
                   b = false;
                }
             }else if(isSpecial.equals(isSpecial1)){
             }
             Date ruleSuitTime = this.getRuleSuitTimeStart();
             Date ruleSuitTime1 = templateFlyR.getRuleSuitTimeStart();
             if (ruleSuitTime == null) {
                if (ruleSuitTime1 != null) {
                label_017f :
                   b = false;
                }
             }else if(ruleSuitTime.equals(ruleSuitTime1)){
             }
             Date ruleSuitTime2 = this.getRuleSuitTimeEnd();
             Date ruleSuitTime3 = templateFlyR.getRuleSuitTimeEnd();
             if (ruleSuitTime2 == null) {
                if (ruleSuitTime3 != null) {
                   b = false;
                }
             }else if(ruleSuitTime2.equals(ruleSuitTime3)){
             }
             String ruleScopeApp = this.getRuleScopeApply();
             String ruleScopeApp1 = templateFlyR.getRuleScopeApply();
             if (ruleScopeApp == null) {
                if (ruleScopeApp1 != null) {
                label_01b3 :
                   b = false;
                }
             }else if(ruleScopeApp.equals(ruleScopeApp1)){
             }
             String ruleDimensio = this.getRuleDimension();
             String ruleDimensio1 = templateFlyR.getRuleDimension();
             if (ruleDimensio == null) {
                if (ruleDimensio1 != null) {
                   b = false;
                }
             }else if(ruleDimensio.equals(ruleDimensio1)){
             }
             b = true;
          }
       }
       return b;
    }
    public List getArgs(){
       return this.args;
    }
    public String getDiagnosisType(){
       return this.diagnosisType;
    }
    public String getId(){
       return this.id;
    }
    public String getIsSpecial(){
       return this.isSpecial;
    }
    public String getNewSqlName(){
       return this.newSqlName;
    }
    public String getPolicyBasis(){
       return this.policyBasis;
    }
    public String getPs(){
       return this.ps;
    }
    public String getRuleCategory1(){
       return this.ruleCategory1;
    }
    public String getRuleCategory2(){
       return this.ruleCategory2;
    }
    public String getRuleDescribe(){
       return this.ruleDescribe;
    }
    public String getRuleDimension(){
       return this.ruleDimension;
    }
    public String getRuleLevel(){
       return this.ruleLevel;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleScopeApply(){
       return this.ruleScopeApply;
    }
    public Date getRuleSuitTimeEnd(){
       return this.ruleSuitTimeEnd;
    }
    public Date getRuleSuitTimeStart(){
       return this.ruleSuitTimeStart;
    }
    public String getSqlTemplate(){
       return this.sqlTemplate;
    }
    public String getWriteRuleName(){
       return this.writeRuleName;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $ruleName = this.getRuleName();
       int i1 = result * 59;
       i = ($ruleName == null)? 43: $ruleName.hashCode();
       result = i1 + i;
       String $writeRuleName = this.getWriteRuleName();
       i1 = result * 59;
       i = ($writeRuleName == null)? 43: $writeRuleName.hashCode();
       result = i1 + i;
       String $sqlTemplate = this.getSqlTemplate();
       i1 = result * 59;
       i = ($sqlTemplate == null)? 43: $sqlTemplate.hashCode();
       result = i1 + i;
       String $newSqlName = this.getNewSqlName();
       i1 = result * 59;
       i = ($newSqlName == null)? 43: $newSqlName.hashCode();
       result = i1 + i;
       List args = this.getArgs();
       i1 = result * 59;
       i = (args == null)? 43: args.hashCode();
       String ps = this.getPs();
       i1 = (i1 + i) * 59;
       i = (ps == null)? 43: ps.hashCode();
       String ruleCategory = this.getRuleCategory1();
       i1 = (i1 + i) * 59;
       i = (ruleCategory == null)? 43: ruleCategory.hashCode();
       String ruleCategory1 = this.getRuleCategory2();
       i1 = (i1 + i) * 59;
       i = (ruleCategory1 == null)? 43: ruleCategory1.hashCode();
       String diagnosisTyp = this.getDiagnosisType();
       i1 = (i1 + i) * 59;
       i = (diagnosisTyp == null)? 43: diagnosisTyp.hashCode();
       String ruleDescribe = this.getRuleDescribe();
       i1 = (i1 + i) * 59;
       i = (ruleDescribe == null)? 43: ruleDescribe.hashCode();
       String ruleLevel = this.getRuleLevel();
       i1 = (i1 + i) * 59;
       i = (ruleLevel == null)? 43: ruleLevel.hashCode();
       String policyBasis = this.getPolicyBasis();
       i1 = (i1 + i) * 59;
       i = (policyBasis == null)? 43: policyBasis.hashCode();
       String isSpecial = this.getIsSpecial();
       i1 = (i1 + i) * 59;
       i = (isSpecial == null)? 43: isSpecial.hashCode();
       Date ruleSuitTime = this.getRuleSuitTimeStart();
       i1 = (i1 + i) * 59;
       i = (ruleSuitTime == null)? 43: ruleSuitTime.hashCode();
       Date ruleSuitTime1 = this.getRuleSuitTimeEnd();
       i1 = (i1 + i) * 59;
       i = (ruleSuitTime1 == null)? 43: ruleSuitTime1.hashCode();
       String ruleScopeApp = this.getRuleScopeApply();
       i1 = (i1 + i) * 59;
       i = (ruleScopeApp == null)? 43: ruleScopeApp.hashCode();
       String ruleDimensio = this.getRuleDimension();
       i1 = (i1 + i) * 59;
       i = (ruleDimensio == null)? 43: ruleDimensio.hashCode();
       return (i1 + i);
    }
    public void setArgs(List args){
       this.args = args;
    }
    public void setDiagnosisType(String diagnosisType){
       this.diagnosisType = diagnosisType;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setIsSpecial(String isSpecial){
       this.isSpecial = isSpecial;
    }
    public void setNewSqlName(String newSqlName){
       this.newSqlName = newSqlName;
    }
    public void setPolicyBasis(String policyBasis){
       this.policyBasis = policyBasis;
    }
    public void setPs(String ps){
       this.ps = ps;
    }
    public void setRuleCategory1(String ruleCategory1){
       this.ruleCategory1 = ruleCategory1;
    }
    public void setRuleCategory2(String ruleCategory2){
       this.ruleCategory2 = ruleCategory2;
    }
    public void setRuleDescribe(String ruleDescribe){
       this.ruleDescribe = ruleDescribe;
    }
    public void setRuleDimension(String ruleDimension){
       this.ruleDimension = ruleDimension;
    }
    public void setRuleLevel(String ruleLevel){
       this.ruleLevel = ruleLevel;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleScopeApply(String ruleScopeApply){
       this.ruleScopeApply = ruleScopeApply;
    }
    public void setRuleSuitTimeEnd(Date ruleSuitTimeEnd){
       this.ruleSuitTimeEnd = ruleSuitTimeEnd;
    }
    public void setRuleSuitTimeStart(Date ruleSuitTimeStart){
       this.ruleSuitTimeStart = ruleSuitTimeStart;
    }
    public void setSqlTemplate(String sqlTemplate){
       this.sqlTemplate = sqlTemplate;
    }
    public void setWriteRuleName(String writeRuleName){
       this.writeRuleName = writeRuleName;
    }
    public String toString(){
       return "TemplateFlyRuleEditDto\(id="+this.getId()+", ruleName="+this.getRuleName()+", writeRuleName="+this.getWriteRuleName()+", sqlTemplate="+this.getSqlTemplate()+", newSqlName="+this.getNewSqlName()+", args="+this.getArgs()+", ps="+this.getPs()+", ruleCategory1="+this.getRuleCategory1()+", ruleCategory2="+this.getRuleCategory2()+", diagnosisType="+this.getDiagnosisType()+", ruleDescribe="+this.getRuleDescribe()+", ruleLevel="+this.getRuleLevel()+", policyBasis="+this.getPolicyBasis()+", isSpecial="+this.getIsSpecial()+", ruleSuitTimeStart="+this.getRuleSuitTimeStart()+", ruleSuitTimeEnd="+this.getRuleSuitTimeEnd()+", ruleScopeApply="+this.getRuleScopeApply()+", ruleDimension="+this.getRuleDimension()+"\)";
    }
}
