package com.taikang.fly.check.dto.resource.ResourceEditDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ResourceEditDto implements Serializable	// class@0001ad from classes.dex
{
    private String description;
    private String iclass;
    private String parentId;
    private String resourceId;
    private String resourceName;
    private String resourceOrder;
    private String resourceType;
    private String url;
    private static final long serialVersionUID = 0x21e31acb44c4c6a7;

    public void ResourceEditDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ResourceEditDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ResourceEditDto){
          b = false;
       }else {
          ResourceEditDto resourceEdit = o;
          if (!resourceEdit.canEqual(this)) {
             b = false;
          }else {
             String resourceId = this.getResourceId();
             String resourceId1 = resourceEdit.getResourceId();
             if (resourceId == null) {
                if (resourceId1 != null) {
                   b = false;
                }
             }else if(resourceId.equals(resourceId1)){
             }
             String resourceName = this.getResourceName();
             String resourceName1 = resourceEdit.getResourceName();
             if (resourceName == null) {
                if (resourceName1 != null) {
                   b = false;
                }
             }else if(resourceName.equals(resourceName1)){
             }
             String description = this.getDescription();
             String description1 = resourceEdit.getDescription();
             if (description == null) {
                if (description1 != null) {
                   b = false;
                }
             }else if(description.equals(description1)){
             }
             String url = this.getUrl();
             String url1 = resourceEdit.getUrl();
             if (url == null) {
                if (url1 != null) {
                   b = false;
                }
             }else if(url.equals(url1)){
             }
             String iclass = this.getIclass();
             String iclass1 = resourceEdit.getIclass();
             if (iclass == null) {
                if (iclass1 != null) {
                   b = false;
                }
             }else if(iclass.equals(iclass1)){
             }
             String resourceOrde = this.getResourceOrder();
             String resourceOrde1 = resourceEdit.getResourceOrder();
             if (resourceOrde == null) {
                if (resourceOrde1 != null) {
                label_009a :
                   b = false;
                }
             }else if(resourceOrde.equals(resourceOrde1)){
             }
             String parentId = this.getParentId();
             String parentId1 = resourceEdit.getParentId();
             if (parentId == null) {
                if (parentId1 != null) {
                   b = false;
                }
             }else if(parentId.equals(parentId1)){
             }
             String resourceType = this.getResourceType();
             String resourceType1 = resourceEdit.getResourceType();
             if (resourceType == null) {
                if (resourceType1 != null) {
                label_00c8 :
                   b = false;
                }
             }else if(resourceType.equals(resourceType1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getDescription(){
       return this.description;
    }
    public String getIclass(){
       return this.iclass;
    }
    public String getParentId(){
       return this.parentId;
    }
    public String getResourceId(){
       return this.resourceId;
    }
    public String getResourceName(){
       return this.resourceName;
    }
    public String getResourceOrder(){
       return this.resourceOrder;
    }
    public String getResourceType(){
       return this.resourceType;
    }
    public String getUrl(){
       return this.url;
    }
    public int hashCode(){
       String $resourceId;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($resourceId = this.getResourceId()) == null)? i: $resourceId.hashCode();
       result = i1 + 59;
       String $resourceName = this.getResourceName();
       int i2 = result * 59;
       i1 = ($resourceName == null)? i: $resourceName.hashCode();
       result = i2 + i1;
       String $description = this.getDescription();
       i2 = result * 59;
       i1 = ($description == null)? i: $description.hashCode();
       result = i2 + i1;
       String $url = this.getUrl();
       i2 = result * 59;
       i1 = ($url == null)? i: $url.hashCode();
       result = i2 + i1;
       String $iclass = this.getIclass();
       i2 = result * 59;
       i1 = ($iclass == null)? i: $iclass.hashCode();
       result = i2 + i1;
       String resourceOrde = this.getResourceOrder();
       i2 = result * 59;
       i1 = (resourceOrde == null)? i: resourceOrde.hashCode();
       String parentId = this.getParentId();
       i2 = (i2 + i1) * 59;
       i1 = (parentId == null)? i: parentId.hashCode();
       String resourceType = this.getResourceType();
       i1 = (i2 + i1) * 59;
       if (resourceType != null) {
          i = resourceType.hashCode();
       }
       return (i1 + i);
    }
    public void setDescription(String description){
       this.description = description;
    }
    public void setIclass(String iclass){
       this.iclass = iclass;
    }
    public void setParentId(String parentId){
       this.parentId = parentId;
    }
    public void setResourceId(String resourceId){
       this.resourceId = resourceId;
    }
    public void setResourceName(String resourceName){
       this.resourceName = resourceName;
    }
    public void setResourceOrder(String resourceOrder){
       this.resourceOrder = resourceOrder;
    }
    public void setResourceType(String resourceType){
       this.resourceType = resourceType;
    }
    public void setUrl(String url){
       this.url = url;
    }
    public String toString(){
       return "ResourceEditDto\(resourceId="+this.getResourceId()+", resourceName="+this.getResourceName()+", description="+this.getDescription()+", url="+this.getUrl()+", iclass="+this.getIclass()+", resourceOrder="+this.getResourceOrder()+", parentId="+this.getParentId()+", resourceType="+this.getResourceType()+"\)";
    }
}
