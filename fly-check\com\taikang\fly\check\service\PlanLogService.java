package com.taikang.fly.check.service.PlanLogService;
import com.taikang.fly.check.dto.planLog.AddPlanLogDto;
import javax.servlet.http.HttpServletResponse;
import java.lang.Integer;
import java.lang.String;
import com.taikang.fly.check.dto.plan.PlanLogRespDto;
import com.taikang.fly.check.dto.planLog.PlanExecuteDto;
import com.taikang.fly.check.mybatis.domain.Plan;
import com.taikang.fly.check.comm.NativePage;
import java.util.List;
import com.taikang.fly.check.dto.flyRule.FlyRuleRespDto;
import com.taikang.fly.check.dto.plan.PlanLogSearchDto;

public interface abstract PlanLogService	// class@0002f4 from classes.dex
{

    Integer addPlanLog(AddPlanLogDto p0,HttpServletResponse p1);
    Integer deleteById(String p0);
    void doPlan(PlanLogRespDto p0,PlanExecuteDto p1);
    void doPlanTask(HttpServletResponse p0,PlanExecuteDto p1,Plan p2);
    void downloadResultsById(String p0,HttpServletResponse p1);
    NativePage getErrorRules(Integer p0,Integer p1,String p2);
    NativePage getFlyRule(Integer p0,Integer p1,String p2);
    void insertMergeTable(List p0,FlyRuleRespDto p1);
    NativePage queryPlanLogListPage(PlanLogSearchDto p0,Integer p1,Integer p2);
}
