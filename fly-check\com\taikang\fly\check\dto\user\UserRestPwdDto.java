package com.taikang.fly.check.dto.user.UserRestPwdDto;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class UserRestPwdDto	// class@0001c4 from classes.dex
{
    private String name;
    private String newPwd;
    private String oldPwd;
    private static final long serialVersionUID = 0x1;

    public void UserRestPwdDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof UserRestPwdDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof UserRestPwdDto) {
             b = false;
          }else {
             UserRestPwdDto userRestPwdD = o;
             if (!userRestPwdD.canEqual(this)) {
                b = false;
             }else {
                String name = this.getName();
                String name1 = userRestPwdD.getName();
                if (name == null) {
                   if (name1 != null) {
                      b = false;
                   }
                }else if(name.equals(name1)){
                }
                String oldPwd = this.getOldPwd();
                String oldPwd1 = userRestPwdD.getOldPwd();
                if (oldPwd == null) {
                   if (oldPwd1 != null) {
                      b = false;
                   }
                }else if(oldPwd.equals(oldPwd1)){
                }
                String newPwd = this.getNewPwd();
                String newPwd1 = userRestPwdD.getNewPwd();
                if (newPwd == null) {
                   if (newPwd1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!newPwd.equals(newPwd1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getName(){
       return this.name;
    }
    public String getNewPwd(){
       return this.newPwd;
    }
    public String getOldPwd(){
       return this.oldPwd;
    }
    public int hashCode(){
       String $name;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($name = this.getName()) == null)? i: $name.hashCode();
       result = i1 + 59;
       String $oldPwd = this.getOldPwd();
       int i2 = result * 59;
       i1 = ($oldPwd == null)? i: $oldPwd.hashCode();
       result = i2 + i1;
       String $newPwd = this.getNewPwd();
       i1 = result * 59;
       if ($newPwd != null) {
          i = $newPwd.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setName(String name){
       this.name = name;
    }
    public void setNewPwd(String newPwd){
       this.newPwd = newPwd;
    }
    public void setOldPwd(String oldPwd){
       this.oldPwd = oldPwd;
    }
    public String toString(){
       return "UserRestPwdDto\(name="+this.getName()+", oldPwd="+this.getOldPwd()+", newPwd="+this.getNewPwd()+"\)";
    }
}
