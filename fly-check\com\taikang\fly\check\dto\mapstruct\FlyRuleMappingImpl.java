package com.taikang.fly.check.dto.mapstruct.FlyRuleMappingImpl;
import com.taikang.fly.check.dto.mapstruct.FlyRuleMapping;
import java.lang.Object;
import com.taikang.fly.check.dto.flyRule.FlyRuleAddDto;
import com.taikang.fly.check.mybatis.domain.FlyRule;
import java.lang.String;
import java.util.Date;
import com.taikang.fly.check.utils.DateUtils;
import com.taikang.fly.check.dto.flyRule.FlyRuleCommonAddDto;
import com.taikang.fly.check.security.core.DomainContext;
import com.taikang.fly.check.security.core.ThreadLocalContextHolder;
import com.taikang.fly.check.dto.user.UserDto;
import com.taikang.fly.check.utils.SequenceGenerator;
import com.taikang.fly.check.dto.flyRule.FlyRuleAuditConfirmationDto;
import com.taikang.fly.check.dto.flyRule.FlyRuleRespDto;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZoneId;
import java.time.LocalDateTime;
import java.time.LocalDate;
import com.taikang.fly.check.dto.flyRule.FlyRulePlanRespDto;
import com.taikang.fly.check.dto.planLog.PlanFlyRuleRespDto;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import com.taikang.fly.check.dto.flyRule.FlyRuleEditDto;
import com.taikang.fly.check.dto.flyRule.FlyRuleWorkOrderEditDto;
import com.taikang.fly.check.dto.flyRule.FlyRuleExchangerDto;
import com.taikang.fly.check.dto.mapper.common.TypeConversionMapper;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import com.taikang.fly.check.dto.flyRuleAudit.FlyRuleAuditRespDto;
import com.taikang.fly.check.dto.flyRule.TemplateFlyRuleEditDto;

public class FlyRuleMappingImpl implements FlyRuleMapping	// class@000157 from classes.dex
{
    private TypeConversionMapper typeConversionMapper;

    public void FlyRuleMappingImpl(){
       super();
    }
    public FlyRule addDtoToDomain(FlyRuleAddDto addDto){
       FlyRule uFlyRule;
       if (addDto == null) {
          uFlyRule = null;
       }else {
          uFlyRule = new FlyRule();
          uFlyRule.setRuleName(addDto.getRuleName());
          uFlyRule.setRuleType(addDto.getRuleType());
          uFlyRule.setPs(addDto.getPs());
          uFlyRule.setSqlName(addDto.getSqlName());
          uFlyRule.setNewSqlName(addDto.getNewSqlName());
          uFlyRule.setRuleCategory1(addDto.getRuleCategory1());
          uFlyRule.setRuleCategory2(addDto.getRuleCategory2());
          uFlyRule.setDiagnosisType(addDto.getDiagnosisType());
          uFlyRule.setRuleDescribe(addDto.getRuleDescribe());
          uFlyRule.setRuleLevel(addDto.getRuleLevel());
          uFlyRule.setPolicyBasis(addDto.getPolicyBasis());
          uFlyRule.setIsSpecial(addDto.getIsSpecial());
          uFlyRule.setRuleScopeApply(addDto.getRuleScopeApply());
          uFlyRule.setRuleDimension(addDto.getRuleDimension());
          uFlyRule.setCreatedTime(new Date());
          uFlyRule.setRuleSuitTimeEnd(DateUtils.parseDateSupportNull(addDto.getRuleSuitTimeEnd()));
          uFlyRule.setRuleSuitTimeStart(DateUtils.parseDateSupportNull(addDto.getRuleSuitTimeStart()));
          uFlyRule.setOperateTime(new Date());
       }
       return uFlyRule;
    }
    public FlyRule addDtoToDomain(FlyRuleCommonAddDto addDto){
       FlyRule uFlyRule;
       if (addDto == null) {
          uFlyRule = null;
       }else {
          uFlyRule = new FlyRule();
          uFlyRule.setRuleName(addDto.getRuleName());
          uFlyRule.setPs(addDto.getPs());
          uFlyRule.setSqlName(addDto.getSqlName());
          uFlyRule.setNewSqlName(addDto.getNewSqlName());
          uFlyRule.setRuleCategory1(addDto.getRuleCategory1());
          uFlyRule.setRuleCategory2(addDto.getRuleCategory2());
          uFlyRule.setDiagnosisType(addDto.getDiagnosisType());
          uFlyRule.setRuleDescribe(addDto.getRuleDescribe());
          uFlyRule.setRuleLevel(addDto.getRuleLevel());
          uFlyRule.setPolicyBasis(addDto.getPolicyBasis());
          uFlyRule.setRedField2("1");
          uFlyRule.setRedField1("1");
          uFlyRule.setOperateTime(new Date());
          uFlyRule.setOperator(ThreadLocalContextHolder.getContext().getUserInfo().getName());
          uFlyRule.setSourceOfRule("1");
          uFlyRule.setRemoved("1");
          uFlyRule.setRuleType("2");
          uFlyRule.setCreater(ThreadLocalContextHolder.getContext().getUserInfo().getName());
          uFlyRule.setCreatedTime(new Date());
          uFlyRule.setId(SequenceGenerator.getId());
          uFlyRule.setState("2");
          uFlyRule.setRegion(ThreadLocalContextHolder.getContext().getUserInfo().getRegion());
          uFlyRule.setSubmitState("1");
       }
       return uFlyRule;
    }
    public FlyRule confirmationDtoToDomain(FlyRuleAuditConfirmationDto flyRuleAuditConfirmationDto){
       FlyRule uFlyRule;
       if (flyRuleAuditConfirmationDto == null) {
          uFlyRule = null;
       }else {
          uFlyRule = new FlyRule();
          uFlyRule.setNewSqlName(flyRuleAuditConfirmationDto.getNewSqlName());
          uFlyRule.setPs(flyRuleAuditConfirmationDto.getPs());
          uFlyRule.setRuleName(flyRuleAuditConfirmationDto.getRuleName());
          uFlyRule.setId(flyRuleAuditConfirmationDto.getId());
          uFlyRule.setSqlName(flyRuleAuditConfirmationDto.getSqlName());
          uFlyRule.setOperateTime(new Date());
       }
       return uFlyRule;
    }
    public FlyRuleRespDto domainToInfoDto(FlyRule domain){
       FlyRuleRespDto uFlyRuleResp;
       if (domain == null) {
          uFlyRuleResp = null;
       }else {
          uFlyRuleResp = new FlyRuleRespDto();
          uFlyRuleResp.setRuleName(domain.getRuleName());
          uFlyRuleResp.setRegion(domain.getRegion());
          uFlyRuleResp.setRuleType(domain.getRuleType());
          uFlyRuleResp.setPs(domain.getPs());
          uFlyRuleResp.setSqlName(domain.getSqlName());
          uFlyRuleResp.setNewSqlName(domain.getNewSqlName());
          uFlyRuleResp.setId(domain.getId());
          uFlyRuleResp.setOperator(domain.getOperator());
          uFlyRuleResp.setResultFlag(domain.getResultFlag());
          uFlyRuleResp.setRemoved(domain.getRemoved());
          if (domain.getOperateTime() != null) {
             uFlyRuleResp.setOperateTime(LocalDateTime.ofInstant(domain.getOperateTime().toInstant(), ZoneOffset.UTC).toLocalDate());
          }
          if (domain.getCreatedTime() != null) {
             uFlyRuleResp.setCreatedTime(LocalDateTime.ofInstant(domain.getCreatedTime().toInstant(), ZoneOffset.UTC).toLocalDate());
          }
          uFlyRuleResp.setState(domain.getState());
          uFlyRuleResp.setCreater(domain.getCreater());
          uFlyRuleResp.setDataSources(domain.getDataSources());
          uFlyRuleResp.setSubmitState(domain.getSubmitState());
          uFlyRuleResp.setResultsEnforcement(domain.getResultsEnforcement());
          uFlyRuleResp.setExecutionDate(domain.getExecutionDate());
          uFlyRuleResp.setRuleLevel(domain.getRuleLevel());
          uFlyRuleResp.setRuleCategory1(domain.getRuleCategory1());
          uFlyRuleResp.setRuleCategory2(domain.getRuleCategory2());
          uFlyRuleResp.setDiagnosisType(domain.getDiagnosisType());
          uFlyRuleResp.setRuleDescribe(domain.getRuleDescribe());
          uFlyRuleResp.setSourceOfRule(domain.getSourceOfRule());
          uFlyRuleResp.setRuleLogic(domain.getRuleLogic());
          uFlyRuleResp.setRuleParameter(domain.getRuleParameter());
          uFlyRuleResp.setRedField3(domain.getRedField3());
          uFlyRuleResp.setRuleClassify(domain.getRuleClassify());
          uFlyRuleResp.setPolicyBasis(domain.getPolicyBasis());
          uFlyRuleResp.setIsSpecial(domain.getIsSpecial());
          uFlyRuleResp.setRuleScopeApply(domain.getRuleScopeApply());
          uFlyRuleResp.setRuleDimension(domain.getRuleDimension());
          uFlyRuleResp.setFeedbackStatus(domain.getFeedbackStatus());
          uFlyRuleResp.setResultIsClear(domain.getResultIsClear());
          uFlyRuleResp.setFalsePositiveRate(domain.getFalsePositiveRate());
          uFlyRuleResp.setProblemDescription(domain.getProblemDescription());
          uFlyRuleResp.setRuleSuitTimeStart(DateUtils.dateToLocalDate(domain.getRuleSuitTimeStart()));
          uFlyRuleResp.setRuleSuitTimeEnd(DateUtils.dateToLocalDate(domain.getRuleSuitTimeEnd()));
       }
       return uFlyRuleResp;
    }
    public FlyRulePlanRespDto domainToInfoPlanDto(FlyRule domain){
       FlyRulePlanRespDto uFlyRulePlan;
       if (domain == null) {
          uFlyRulePlan = null;
       }else {
          uFlyRulePlan = new FlyRulePlanRespDto();
          uFlyRulePlan.setRuleName(domain.getRuleName());
          uFlyRulePlan.setRegion(domain.getRegion());
          uFlyRulePlan.setRuleType(domain.getRuleType());
          uFlyRulePlan.setPs(domain.getPs());
          uFlyRulePlan.setSqlName(domain.getSqlName());
          uFlyRulePlan.setNewSqlName(domain.getNewSqlName());
          uFlyRulePlan.setId(domain.getId());
          uFlyRulePlan.setOperator(domain.getOperator());
          uFlyRulePlan.setResultFlag(domain.getResultFlag());
          uFlyRulePlan.setRemoved(domain.getRemoved());
          if (domain.getOperateTime() != null) {
             uFlyRulePlan.setOperateTime(LocalDateTime.ofInstant(domain.getOperateTime().toInstant(), ZoneOffset.UTC).toLocalDate());
          }
          if (domain.getCreatedTime() != null) {
             uFlyRulePlan.setCreatedTime(LocalDateTime.ofInstant(domain.getCreatedTime().toInstant(), ZoneOffset.UTC).toLocalDate());
          }
          uFlyRulePlan.setState(domain.getState());
          uFlyRulePlan.setCreater(domain.getCreater());
          uFlyRulePlan.setDataSources(domain.getDataSources());
          uFlyRulePlan.setSubmitState(domain.getSubmitState());
          uFlyRulePlan.setResultsEnforcement(domain.getResultsEnforcement());
          uFlyRulePlan.setExecutionDate(domain.getExecutionDate());
          uFlyRulePlan.setRuleLevel(domain.getRuleLevel());
          uFlyRulePlan.setRuleCategory1(domain.getRuleCategory1());
          uFlyRulePlan.setRuleCategory2(domain.getRuleCategory2());
          uFlyRulePlan.setDiagnosisType(domain.getDiagnosisType());
          uFlyRulePlan.setRuleDescribe(domain.getRuleDescribe());
          uFlyRulePlan.setSourceOfRule(domain.getSourceOfRule());
          uFlyRulePlan.setRuleLogic(domain.getRuleLogic());
          uFlyRulePlan.setRuleParameter(domain.getRuleParameter());
          uFlyRulePlan.setRedField3(domain.getRedField3());
          uFlyRulePlan.setRuleClassify(domain.getRuleClassify());
          uFlyRulePlan.setPolicyBasis(domain.getPolicyBasis());
          uFlyRulePlan.setRuleScopeApply(domain.getRuleScopeApply());
       }
       return uFlyRulePlan;
    }
    public PlanFlyRuleRespDto domainToInfoPlanFlyRuleDto(FlyRule domain){
       PlanFlyRuleRespDto planFlyRuleR;
       if (domain == null) {
          planFlyRuleR = null;
       }else {
          planFlyRuleR = new PlanFlyRuleRespDto();
          planFlyRuleR.setRuleIntension(domain.getRuleDescribe());
          planFlyRuleR.setRuleName(domain.getRuleName());
          planFlyRuleR.setRegion(domain.getRegion());
          planFlyRuleR.setRuleType(domain.getRuleType());
          planFlyRuleR.setPs(domain.getPs());
          planFlyRuleR.setSqlName(domain.getSqlName());
          planFlyRuleR.setNewSqlName(domain.getNewSqlName());
          planFlyRuleR.setId(domain.getId());
          planFlyRuleR.setOperator(domain.getOperator());
          planFlyRuleR.setResultFlag(domain.getResultFlag());
          planFlyRuleR.setRemoved(domain.getRemoved());
          if (domain.getOperateTime() != null) {
             planFlyRuleR.setOperateTime(LocalDateTime.ofInstant(domain.getOperateTime().toInstant(), ZoneOffset.UTC).toLocalDate());
          }
          if (domain.getCreatedTime() != null) {
             planFlyRuleR.setCreatedTime(LocalDateTime.ofInstant(domain.getCreatedTime().toInstant(), ZoneOffset.UTC).toLocalDate());
          }
          planFlyRuleR.setState(domain.getState());
          planFlyRuleR.setCreater(domain.getCreater());
          planFlyRuleR.setDataSources(domain.getDataSources());
          planFlyRuleR.setSubmitState(domain.getSubmitState());
          planFlyRuleR.setResultsEnforcement(domain.getResultsEnforcement());
          planFlyRuleR.setExecutionDate(domain.getExecutionDate());
          planFlyRuleR.setRuleLevel(domain.getRuleLevel());
          planFlyRuleR.setRuleCategory1(domain.getRuleCategory1());
          planFlyRuleR.setRuleCategory2(domain.getRuleCategory2());
          planFlyRuleR.setDiagnosisType(domain.getDiagnosisType());
          planFlyRuleR.setSourceOfRule(domain.getSourceOfRule());
          planFlyRuleR.setRuleLogic(domain.getRuleLogic());
          planFlyRuleR.setRuleParameter(domain.getRuleParameter());
          planFlyRuleR.setRedField3(domain.getRedField3());
          planFlyRuleR.setRuleClassify(domain.getRuleClassify());
          planFlyRuleR.setPolicyBasis(domain.getPolicyBasis());
       }
       return planFlyRuleR;
    }
    public FlyRuleAddDto domainToaddDto(FlyRule flyRule){
       FlyRuleAddDto uFlyRuleAddD;
       if (flyRule == null) {
          uFlyRuleAddD = null;
       }else {
          uFlyRuleAddD = new FlyRuleAddDto();
          uFlyRuleAddD.setRuleName(flyRule.getRuleName());
          uFlyRuleAddD.setSqlName(flyRule.getSqlName());
          uFlyRuleAddD.setNewSqlName(flyRule.getNewSqlName());
          uFlyRuleAddD.setRuleType(flyRule.getRuleType());
          uFlyRuleAddD.setPs(flyRule.getPs());
          uFlyRuleAddD.setRuleLevel(flyRule.getRuleLevel());
          uFlyRuleAddD.setRuleCategory1(flyRule.getRuleCategory1());
          uFlyRuleAddD.setRuleCategory2(flyRule.getRuleCategory2());
          uFlyRuleAddD.setDiagnosisType(flyRule.getDiagnosisType());
          uFlyRuleAddD.setRuleDescribe(flyRule.getRuleDescribe());
          uFlyRuleAddD.setPolicyBasis(flyRule.getPolicyBasis());
          uFlyRuleAddD.setIsSpecial(flyRule.getIsSpecial());
          uFlyRuleAddD.setRuleScopeApply(flyRule.getRuleScopeApply());
          uFlyRuleAddD.setRuleDimension(flyRule.getRuleDimension());
          uFlyRuleAddD.setRuleSuitTimeStart(DateUtils.formatDefaultPatternDate(flyRule.getRuleSuitTimeStart()));
          uFlyRuleAddD.setRuleSuitTimeEnd(DateUtils.formatDefaultPatternDate(flyRule.getRuleSuitTimeEnd()));
       }
       return uFlyRuleAddD;
    }
    public FlyRule dtoToDomain(FlyRuleRespDto flyRuleRespDto){
       FlyRule uFlyRule;
       if (flyRuleRespDto == null) {
          uFlyRule = null;
       }else {
          uFlyRule = new FlyRule();
          uFlyRule.setId(flyRuleRespDto.getId());
          uFlyRule.setRuleName(flyRuleRespDto.getRuleName());
          uFlyRule.setOperator(flyRuleRespDto.getOperator());
          uFlyRule.setRegion(flyRuleRespDto.getRegion());
          uFlyRule.setRuleType(flyRuleRespDto.getRuleType());
          uFlyRule.setResultFlag(flyRuleRespDto.getResultFlag());
          uFlyRule.setRemoved(flyRuleRespDto.getRemoved());
          uFlyRule.setPs(flyRuleRespDto.getPs());
          uFlyRule.setSqlName(flyRuleRespDto.getSqlName());
          uFlyRule.setNewSqlName(flyRuleRespDto.getNewSqlName());
          uFlyRule.setState(flyRuleRespDto.getState());
          uFlyRule.setCreater(flyRuleRespDto.getCreater());
          uFlyRule.setDataSources(flyRuleRespDto.getDataSources());
          uFlyRule.setSubmitState(flyRuleRespDto.getSubmitState());
          uFlyRule.setResultsEnforcement(flyRuleRespDto.getResultsEnforcement());
          uFlyRule.setExecutionDate(flyRuleRespDto.getExecutionDate());
          uFlyRule.setRuleCategory1(flyRuleRespDto.getRuleCategory1());
          uFlyRule.setRuleCategory2(flyRuleRespDto.getRuleCategory2());
          uFlyRule.setDiagnosisType(flyRuleRespDto.getDiagnosisType());
          uFlyRule.setRuleDescribe(flyRuleRespDto.getRuleDescribe());
          uFlyRule.setRuleLevel(flyRuleRespDto.getRuleLevel());
          uFlyRule.setSourceOfRule(flyRuleRespDto.getSourceOfRule());
          uFlyRule.setRuleLogic(flyRuleRespDto.getRuleLogic());
          uFlyRule.setRuleParameter(flyRuleRespDto.getRuleParameter());
          uFlyRule.setRedField3(flyRuleRespDto.getRedField3());
          uFlyRule.setRuleClassify(flyRuleRespDto.getRuleClassify());
          uFlyRule.setPolicyBasis(flyRuleRespDto.getPolicyBasis());
          uFlyRule.setIsSpecial(flyRuleRespDto.getIsSpecial());
          if (flyRuleRespDto.getRuleSuitTimeStart() != null) {
             uFlyRule.setRuleSuitTimeStart(Date.from(flyRuleRespDto.getRuleSuitTimeStart().atStartOfDay(ZoneOffset.UTC).toInstant()));
          }
          if (flyRuleRespDto.getRuleSuitTimeEnd() != null) {
             uFlyRule.setRuleSuitTimeEnd(Date.from(flyRuleRespDto.getRuleSuitTimeEnd().atStartOfDay(ZoneOffset.UTC).toInstant()));
          }
          uFlyRule.setRuleScopeApply(flyRuleRespDto.getRuleScopeApply());
          uFlyRule.setRuleDimension(flyRuleRespDto.getRuleDimension());
          uFlyRule.setFeedbackStatus(flyRuleRespDto.getFeedbackStatus());
          uFlyRule.setResultIsClear(flyRuleRespDto.getResultIsClear());
          uFlyRule.setFalsePositiveRate(flyRuleRespDto.getFalsePositiveRate());
          uFlyRule.setProblemDescription(flyRuleRespDto.getProblemDescription());
          uFlyRule.setCreatedTime(new Date());
          uFlyRule.setOperateTime(new Date());
       }
       return uFlyRule;
    }
    public List dtoToDomains(List flyRuleRespDtos){
       List list;
       if (flyRuleRespDtos == null) {
          list = null;
       }else {
          list = new ArrayList(flyRuleRespDtos.size());
          Iterator iterator = flyRuleRespDtos.iterator();
          while (iterator.hasNext()) {
             list.add(this.dtoToDomain(iterator.next()));
          }
       }
       return list;
    }
    public FlyRule editDtoToDomain(FlyRuleEditDto editDto,FlyRule domain){
       if (editDto == null) {
          domain = null;
       }else {
          domain.setRuleName(editDto.getRuleName());
          domain.setRegion(editDto.getRegion());
          domain.setRuleType(editDto.getRuleType());
          domain.setPs(editDto.getPs());
          domain.setNewSqlName(editDto.getNewSqlName());
          domain.setRuleCategory1(editDto.getRuleCategory1());
          domain.setRuleCategory2(editDto.getRuleCategory2());
          domain.setDiagnosisType(editDto.getDiagnosisType());
          domain.setRuleDescribe(editDto.getRuleDescribe());
          domain.setRuleLevel(editDto.getRuleLevel());
          domain.setPolicyBasis(editDto.getPolicyBasis());
          domain.setIsSpecial(editDto.getIsSpecial());
          domain.setRuleScopeApply(editDto.getRuleScopeApply());
          domain.setRuleDimension(editDto.getRuleDimension());
          domain.setFeedbackStatus(editDto.getFeedbackStatus());
          domain.setResultIsClear(editDto.getResultIsClear());
          domain.setFalsePositiveRate(editDto.getFalsePositiveRate());
          domain.setProblemDescription(editDto.getProblemDescription());
          domain.setRuleSuitTimeEnd(DateUtils.parseDateSupportNull(editDto.getRuleSuitTimeEnd()));
          domain.setRuleSuitTimeStart(DateUtils.parseDateSupportNull(editDto.getRuleSuitTimeStart()));
          domain.setOperateTime(new Date());
       }
       return domain;
    }
    public FlyRule editWorkOrderDtoToDomain(FlyRuleWorkOrderEditDto editDto,FlyRule domain){
       if (editDto == null) {
          domain = null;
       }else {
          domain.setRuleName(editDto.getRuleName());
          domain.setRegion(editDto.getRegion());
          domain.setRuleType(editDto.getRuleType());
          domain.setPs(editDto.getPs());
          domain.setSqlName(editDto.getSqlName());
          domain.setNewSqlName(editDto.getNewSqlName());
          domain.setRuleCategory1(editDto.getRuleCategory1());
          domain.setRuleCategory2(editDto.getRuleCategory2());
          domain.setDiagnosisType(editDto.getDiagnosisType());
          domain.setRuleDescribe(editDto.getRuleDescribe());
          domain.setRuleLevel(editDto.getRuleLevel());
          domain.setPolicyBasis(editDto.getPolicyBasis());
          domain.setIsSpecial(editDto.getIsSpecial());
          domain.setRuleScopeApply(editDto.getRuleScopeApply());
          domain.setRuleDimension(editDto.getRuleDimension());
          domain.setRuleSuitTimeEnd(DateUtils.parseDate(editDto.getRuleSuitTimeEnd()));
          domain.setRuleSuitTimeStart(DateUtils.parseDate(editDto.getRuleSuitTimeStart()));
          domain.setOperateTime(new Date());
       }
       return domain;
    }
    public List entityToDtos(List flyRules){
       List list;
       if (flyRules == null) {
          list = null;
       }else {
          list = new ArrayList(flyRules.size());
          Iterator iterator = flyRules.iterator();
          while (iterator.hasNext()) {
             list.add(this.domainToInfoDto(iterator.next()));
          }
       }
       return list;
    }
    public List entityToPlanDtos(List flyRules){
       List list;
       if (flyRules == null) {
          list = null;
       }else {
          list = new ArrayList(flyRules.size());
          Iterator iterator = flyRules.iterator();
          while (iterator.hasNext()) {
             list.add(this.domainToInfoPlanDto(iterator.next()));
          }
       }
       return list;
    }
    public List entityToPlanFlyRuleDtos(List flyRules){
       List list;
       if (flyRules == null) {
          list = null;
       }else {
          list = new ArrayList(flyRules.size());
          Iterator iterator = flyRules.iterator();
          while (iterator.hasNext()) {
             list.add(this.domainToInfoPlanFlyRuleDto(iterator.next()));
          }
       }
       return list;
    }
    public List exchangerListToFlyRuleList(List exchangerDtoList){
       List list;
       if (exchangerDtoList == null) {
          list = null;
       }else {
          list = new ArrayList(exchangerDtoList.size());
          Iterator iterator = exchangerDtoList.iterator();
          while (iterator.hasNext()) {
             list.add(this.exchangerToFlyRule(iterator.next()));
          }
       }
       return list;
    }
    public FlyRule exchangerToFlyRule(FlyRuleExchangerDto exchangerDto){
       FlyRule uFlyRule;
       if (exchangerDto == null) {
          uFlyRule = null;
       }else {
          uFlyRule = new FlyRule();
          uFlyRule.setId(exchangerDto.getId());
          uFlyRule.setRuleName(exchangerDto.getRuleName());
          uFlyRule.setOperator(exchangerDto.getOperator());
          uFlyRule.setRegion(exchangerDto.getRegion());
          uFlyRule.setRuleType(exchangerDto.getRuleType());
          uFlyRule.setResultFlag(exchangerDto.getResultFlag());
          uFlyRule.setRemoved(exchangerDto.getRemoved());
          uFlyRule.setCreatedTime(this.typeConversionMapper.String2Time(exchangerDto.getCreatedTime()));
          uFlyRule.setPs(exchangerDto.getPs());
          uFlyRule.setSqlName(exchangerDto.getSqlName());
          uFlyRule.setNewSqlName(exchangerDto.getNewSqlName());
          uFlyRule.setState(exchangerDto.getState());
          uFlyRule.setCreater(exchangerDto.getCreater());
          uFlyRule.setDataSources(exchangerDto.getDataSources());
          uFlyRule.setSubmitState(exchangerDto.getSubmitState());
          uFlyRule.setResultsEnforcement(exchangerDto.getResultsEnforcement());
          uFlyRule.setExecutionDate(this.typeConversionMapper.String2LocalDateTime(exchangerDto.getExecutionDate()));
          uFlyRule.setRuleCategory1(exchangerDto.getRuleCategory1());
          uFlyRule.setRuleCategory2(exchangerDto.getRuleCategory2());
          uFlyRule.setDiagnosisType(exchangerDto.getDiagnosisType());
          uFlyRule.setRuleDescribe(exchangerDto.getRuleDescribe());
          uFlyRule.setRuleLevel(exchangerDto.getRuleLevel());
          uFlyRule.setSourceOfRule(exchangerDto.getSourceOfRule());
          uFlyRule.setRuleLogic(exchangerDto.getRuleLogic());
          uFlyRule.setRuleParameter(exchangerDto.getRuleParameter());
          uFlyRule.setRedField1(exchangerDto.getRedField1());
          uFlyRule.setRedField2(exchangerDto.getRedField2());
          uFlyRule.setRedField3(exchangerDto.getRedField3());
          uFlyRule.setRuleClassify(exchangerDto.getRuleClassify());
          uFlyRule.setPolicyBasis(exchangerDto.getPolicyBasis());
          uFlyRule.setIsSpecial(exchangerDto.getIsSpecial());
          uFlyRule.setRuleScopeApply(exchangerDto.getRuleScopeApply());
          uFlyRule.setRuleDimension(exchangerDto.getRuleDimension());
          uFlyRule.setFeedbackStatus(exchangerDto.getFeedbackStatus());
          uFlyRule.setResultIsClear(exchangerDto.getResultIsClear());
          uFlyRule.setFalsePositiveRate(exchangerDto.getFalsePositiveRate());
          uFlyRule.setProblemDescription(exchangerDto.getProblemDescription());
          uFlyRule.setRuleSuitTimeEnd(DateUtils.parseDateSupportNull(exchangerDto.getRuleSuitTimeEnd()));
          uFlyRule.setRuleSuitTimeStart(DateUtils.parseDateSupportNull(exchangerDto.getRuleSuitTimeStart()));
          uFlyRule.setOperateTime(new Date());
       }
       return uFlyRule;
    }
    public List flyRuleListToexchangerList(List flyRuleList){
       List list;
       if (flyRuleList == null) {
          list = null;
       }else {
          list = new ArrayList(flyRuleList.size());
          Iterator iterator = flyRuleList.iterator();
          while (iterator.hasNext()) {
             list.add(this.flyRuleToExchanger(iterator.next()));
          }
       }
       return list;
    }
    public List flyRuleResListToexchangerList(List flyRuleResList){
       List list;
       if (flyRuleResList == null) {
          list = null;
       }else {
          list = new ArrayList(flyRuleResList.size());
          Iterator iterator = flyRuleResList.iterator();
          while (iterator.hasNext()) {
             list.add(this.flyRuleResToExchanger(iterator.next()));
          }
       }
       return list;
    }
    public FlyRuleExchangerDto flyRuleResToExchanger(FlyRuleRespDto flyRule){
       FlyRuleExchangerDto uFlyRuleExch;
       if (flyRule == null) {
          uFlyRuleExch = null;
       }else {
          uFlyRuleExch = new FlyRuleExchangerDto();
          uFlyRuleExch.setId(flyRule.getId());
          uFlyRuleExch.setRuleName(flyRule.getRuleName());
          uFlyRuleExch.setOperator(flyRule.getOperator());
          uFlyRuleExch.setRegion(flyRule.getRegion());
          uFlyRuleExch.setRuleType(flyRule.getRuleType());
          uFlyRuleExch.setResultFlag(flyRule.getResultFlag());
          uFlyRuleExch.setRemoved(flyRule.getRemoved());
          if (flyRule.getOperateTime() != null) {
             uFlyRuleExch.setOperateTime(DateTimeFormatter.ISO_LOCAL_DATE.format(flyRule.getOperateTime()));
          }
          if (flyRule.getCreatedTime() != null) {
             uFlyRuleExch.setCreatedTime(DateTimeFormatter.ISO_LOCAL_DATE.format(flyRule.getCreatedTime()));
          }
          uFlyRuleExch.setPs(flyRule.getPs());
          uFlyRuleExch.setState(flyRule.getState());
          uFlyRuleExch.setCreater(flyRule.getCreater());
          uFlyRuleExch.setResultsEnforcement(flyRule.getResultsEnforcement());
          if (flyRule.getExecutionDate() != null) {
             uFlyRuleExch.setExecutionDate(DateTimeFormatter.ISO_LOCAL_DATE_TIME.format(flyRule.getExecutionDate()));
          }
          uFlyRuleExch.setDataSources(flyRule.getDataSources());
          uFlyRuleExch.setSubmitState(flyRule.getSubmitState());
          uFlyRuleExch.setRuleLevel(flyRule.getRuleLevel());
          uFlyRuleExch.setRuleCategory1(flyRule.getRuleCategory1());
          uFlyRuleExch.setRuleCategory2(flyRule.getRuleCategory2());
          uFlyRuleExch.setDiagnosisType(flyRule.getDiagnosisType());
          uFlyRuleExch.setRuleDescribe(flyRule.getRuleDescribe());
          uFlyRuleExch.setSourceOfRule(flyRule.getSourceOfRule());
          uFlyRuleExch.setRuleLogic(flyRule.getRuleLogic());
          uFlyRuleExch.setRuleParameter(flyRule.getRuleParameter());
          uFlyRuleExch.setRedField3(flyRule.getRedField3());
          uFlyRuleExch.setSqlName(flyRule.getSqlName());
          uFlyRuleExch.setNewSqlName(flyRule.getNewSqlName());
          uFlyRuleExch.setRuleClassify(flyRule.getRuleClassify());
          uFlyRuleExch.setPolicyBasis(flyRule.getPolicyBasis());
          uFlyRuleExch.setIsSpecial(flyRule.getIsSpecial());
          if (flyRule.getRuleSuitTimeStart() != null) {
             uFlyRuleExch.setRuleSuitTimeStart(DateTimeFormatter.ISO_LOCAL_DATE.format(flyRule.getRuleSuitTimeStart()));
          }
          if (flyRule.getRuleSuitTimeEnd() != null) {
             uFlyRuleExch.setRuleSuitTimeEnd(DateTimeFormatter.ISO_LOCAL_DATE.format(flyRule.getRuleSuitTimeEnd()));
          }
          uFlyRuleExch.setRuleDimension(flyRule.getRuleDimension());
          uFlyRuleExch.setRuleScopeApply(flyRule.getRuleScopeApply());
          uFlyRuleExch.setFeedbackStatus(flyRule.getFeedbackStatus());
          uFlyRuleExch.setResultIsClear(flyRule.getResultIsClear());
          uFlyRuleExch.setFalsePositiveRate(flyRule.getFalsePositiveRate());
          uFlyRuleExch.setProblemDescription(flyRule.getProblemDescription());
       }
       return uFlyRuleExch;
    }
    public FlyRuleExchangerDto flyRuleToExchanger(FlyRule flyRule){
       FlyRuleExchangerDto uFlyRuleExch;
       if (flyRule == null) {
          uFlyRuleExch = null;
       }else {
          uFlyRuleExch = new FlyRuleExchangerDto();
          uFlyRuleExch.setId(flyRule.getId());
          uFlyRuleExch.setRuleName(flyRule.getRuleName());
          uFlyRuleExch.setOperator(flyRule.getOperator());
          uFlyRuleExch.setRegion(flyRule.getRegion());
          uFlyRuleExch.setRuleType(flyRule.getRuleType());
          uFlyRuleExch.setResultFlag(flyRule.getResultFlag());
          uFlyRuleExch.setRemoved(flyRule.getRemoved());
          uFlyRuleExch.setOperateTime(this.typeConversionMapper.DateTime2String(flyRule.getOperateTime()));
          uFlyRuleExch.setCreatedTime(this.typeConversionMapper.DateTime2String(flyRule.getCreatedTime()));
          uFlyRuleExch.setPs(flyRule.getPs());
          uFlyRuleExch.setState(flyRule.getState());
          uFlyRuleExch.setCreater(flyRule.getCreater());
          uFlyRuleExch.setResultsEnforcement(flyRule.getResultsEnforcement());
          if (flyRule.getExecutionDate() != null) {
             uFlyRuleExch.setExecutionDate(DateTimeFormatter.ISO_LOCAL_DATE_TIME.format(flyRule.getExecutionDate()));
          }
          uFlyRuleExch.setDataSources(flyRule.getDataSources());
          uFlyRuleExch.setSubmitState(flyRule.getSubmitState());
          uFlyRuleExch.setRuleLevel(flyRule.getRuleLevel());
          uFlyRuleExch.setRuleCategory1(flyRule.getRuleCategory1());
          uFlyRuleExch.setRuleCategory2(flyRule.getRuleCategory2());
          uFlyRuleExch.setDiagnosisType(flyRule.getDiagnosisType());
          uFlyRuleExch.setRuleDescribe(flyRule.getRuleDescribe());
          uFlyRuleExch.setSourceOfRule(flyRule.getSourceOfRule());
          uFlyRuleExch.setRuleLogic(flyRule.getRuleLogic());
          uFlyRuleExch.setRuleParameter(flyRule.getRuleParameter());
          uFlyRuleExch.setRedField1(flyRule.getRedField1());
          uFlyRuleExch.setRedField2(flyRule.getRedField2());
          uFlyRuleExch.setRedField3(flyRule.getRedField3());
          uFlyRuleExch.setSqlName(flyRule.getSqlName());
          uFlyRuleExch.setNewSqlName(flyRule.getNewSqlName());
          uFlyRuleExch.setRuleClassify(flyRule.getRuleClassify());
          uFlyRuleExch.setPolicyBasis(flyRule.getPolicyBasis());
          uFlyRuleExch.setIsSpecial(flyRule.getIsSpecial());
          uFlyRuleExch.setRuleSuitTimeStart(this.typeConversionMapper.DateTime2String(flyRule.getRuleSuitTimeStart()));
          uFlyRuleExch.setRuleSuitTimeEnd(this.typeConversionMapper.DateTime2String(flyRule.getRuleSuitTimeEnd()));
          uFlyRuleExch.setRuleDimension(flyRule.getRuleDimension());
          uFlyRuleExch.setRuleScopeApply(flyRule.getRuleScopeApply());
          uFlyRuleExch.setFeedbackStatus(flyRule.getFeedbackStatus());
          uFlyRuleExch.setResultIsClear(flyRule.getResultIsClear());
          uFlyRuleExch.setFalsePositiveRate(flyRule.getFalsePositiveRate());
          uFlyRuleExch.setProblemDescription(flyRule.getProblemDescription());
       }
       return uFlyRuleExch;
    }
    public List ruleAuditDtoListToRuleList(List flyRuleAuditRespDtoList){
       List list;
       if (flyRuleAuditRespDtoList == null) {
          list = null;
       }else {
          list = new ArrayList(flyRuleAuditRespDtoList.size());
          Iterator iterator = flyRuleAuditRespDtoList.iterator();
          while (iterator.hasNext()) {
             list.add(this.ruleAuditDtoToRule(iterator.next()));
          }
       }
       return list;
    }
    public FlyRule ruleAuditDtoToRule(FlyRuleAuditRespDto flyRuleAuditRespDto){
       FlyRule uFlyRule;
       if (flyRuleAuditRespDto == null) {
          uFlyRule = null;
       }else {
          uFlyRule = new FlyRule();
          uFlyRule.setId(flyRuleAuditRespDto.getId());
          uFlyRule.setRuleName(flyRuleAuditRespDto.getRuleName());
          uFlyRule.setRegion(flyRuleAuditRespDto.getRegion());
          uFlyRule.setDiagnosisType(flyRuleAuditRespDto.getDiagnosisType());
          uFlyRule.setRuleLevel(flyRuleAuditRespDto.getRuleLevel());
          uFlyRule.setRuleScopeApply(flyRuleAuditRespDto.getRuleScopeApply());
       }
       return uFlyRule;
    }
    public FlyRule templateEditDtoToDomain(TemplateFlyRuleEditDto templateFlyRuleEditDto,FlyRule flyRule){
       if (templateFlyRuleEditDto == null) {
          flyRule = null;
       }else {
          flyRule.setRuleName(templateFlyRuleEditDto.getRuleName());
          flyRule.setPs(templateFlyRuleEditDto.getPs());
          flyRule.setNewSqlName(templateFlyRuleEditDto.getNewSqlName());
          flyRule.setRuleCategory1(templateFlyRuleEditDto.getRuleCategory1());
          flyRule.setRuleCategory2(templateFlyRuleEditDto.getRuleCategory2());
          flyRule.setDiagnosisType(templateFlyRuleEditDto.getDiagnosisType());
          flyRule.setRuleDescribe(templateFlyRuleEditDto.getRuleDescribe());
          flyRule.setRuleLevel(templateFlyRuleEditDto.getRuleLevel());
          flyRule.setPolicyBasis(templateFlyRuleEditDto.getPolicyBasis());
          flyRule.setIsSpecial(templateFlyRuleEditDto.getIsSpecial());
          flyRule.setRuleSuitTimeStart(templateFlyRuleEditDto.getRuleSuitTimeStart());
          flyRule.setRuleSuitTimeEnd(templateFlyRuleEditDto.getRuleSuitTimeEnd());
          flyRule.setRuleScopeApply(templateFlyRuleEditDto.getRuleScopeApply());
          flyRule.setRuleDimension(templateFlyRuleEditDto.getRuleDimension());
          flyRule.setOperateTime(new Date());
       }
       return flyRule;
    }
}
