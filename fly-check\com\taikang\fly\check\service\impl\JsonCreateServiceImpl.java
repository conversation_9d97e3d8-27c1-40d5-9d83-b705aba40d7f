package com.taikang.fly.check.service.impl.JsonCreateServiceImpl;
import com.taikang.fly.check.service.JsonCreateService;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import com.taikang.fly.check.dto.jsoncreate.DatasourceDTO;
import java.lang.String;
import java.util.List;
import java.util.ArrayList;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.Statement;
import java.sql.ResultSet;
import java.sql.SQLException;
import com.taikang.fly.check.comm.BizException;
import com.taikang.fly.check.comm.enums.ResponseCodeEnum;
import java.lang.ClassNotFoundException;
import java.lang.Throwable;

public class JsonCreateServiceImpl implements JsonCreateService	// class@000318 from classes.dex
{
    private static final Logger log;

    static {
       JsonCreateServiceImpl.log = LoggerFactory.getLogger(JsonCreateServiceImpl.class);
    }
    public void JsonCreateServiceImpl(){
       super();
    }
    public List executeOneColumnSql(DatasourceDTO datasourceDTO,String driver,String queryTableNameSql){
       try{
          List list = new ArrayList();
          try{
             Class.forName(driver);
             Connection connection = DriverManager.getConnection(datasourceDTO.getUrl(), datasourceDTO.getUsername(), datasourceDTO.getPassword());
             try{
                int i = 0;
                Statement statement = connection.createStatement();
                try{
                   int i1 = 0;
                   ResultSet resultSet = statement.executeQuery(queryTableNameSql);
                   int i2 = 0;
                   try{
                      while (resultSet.next()) {
                         list.add(resultSet.getString(1));
                      }
                      try{
                         if (resultSet != null) {
                            if (0) {
                               resultSet.close();
                            }else {
                               try{
                                  resultSet.close();
                               }catch(java.lang.Throwable e5){
                                  throw e5;
                               }
                            }
                         }
                      }catch(java.lang.Throwable e9){
                         i2.addSuppressed(e9);
                      }
                      try{
                         if (statement != null) {
                            if (0) {
                               statement.close();
                            }else {
                               try{
                                  statement.close();
                               }catch(java.lang.Throwable e5){
                                  throw e5;
                               }
                            }
                         }
                      }catch(java.lang.Throwable e7){
                         i1.addSuppressed(e7);
                      }
                      try{
                         if (connection != null) {
                            if (0) {
                               connection.close();
                            }else {
                               connection.close();
                            }
                         }
                      }catch(java.lang.Throwable e6){
                         i.addSuppressed(e6);
                      }catch(java.sql.SQLException e1){
                         JsonCreateServiceImpl.log.debug("{}", e1.getMessage());
                         throw new BizException(ResponseCodeEnum.CONNECTION_ERROR);
                      }
                      return list;
                   }catch(java.lang.Throwable e5){
                      throw e5;
                   }
                }catch(java.lang.Throwable e5){
                }
             }catch(java.lang.Throwable e5){
             }
          }catch(java.sql.SQLException e1){
          }
       }catch(java.lang.ClassNotFoundException e1){
          JsonCreateServiceImpl.log.debug("{}", e1.getMessage());
          throw new BizException(ResponseCodeEnum.CONNECTION_ERROR);
       }
    }
}
