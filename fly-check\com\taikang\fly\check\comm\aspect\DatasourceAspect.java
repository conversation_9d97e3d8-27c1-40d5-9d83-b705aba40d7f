package com.taikang.fly.check.comm.aspect.DatasourceAspect;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import org.aspectj.lang.ProceedingJoinPoint;
import com.taikang.fly.check.security.core.DomainContext;
import com.taikang.fly.check.security.core.ThreadLocalContextHolder;
import com.taikang.fly.check.dto.user.UserDto;
import com.taikang.fly.check.dto.DatasourceInfo;
import java.lang.String;
import org.aspectj.lang.Signature;
import org.aspectj.lang.reflect.MethodSignature;
import java.lang.reflect.Method;
import com.taikang.fly.check.comm.annotation.FlyCheckDatasource;
import java.lang.annotation.Annotation;
import com.taikang.fly.check.comm.enums.QueryTypeEnum;
import com.taikang.fly.check.utils.DBUtils.DbHelper;
import com.taikang.fly.check.utils.DBUtils.ClickhouseDbHelper;

public class DatasourceAspect	// class@00007e from classes.dex
{
    private String clickhouseJdbcUrl;
    private String clickhousePasswd;
    private String clickhouseUsername;
    private String password;
    private String username;
    private static final Logger log;

    static {
       DatasourceAspect.log = LoggerFactory.getLogger(DatasourceAspect.class);
    }
    public void DatasourceAspect(){
       super();
    }
    public Object around(ProceedingJoinPoint joinPoint){
       DatasourceInfo datasourceInfo = ThreadLocalContextHolder.getContextThread().getUserInfo().getDatasourceInfo();
       String url = datasourceInfo.getUrl();
       String oraUserName = datasourceInfo.getUsername();
       String oraPassword = datasourceInfo.getPassword();
       MethodSignature signaturex = joinPoint.getSignature();
       DatasourceAspect.log.debug("===================================================");
       DatasourceAspect.log.debug("aop method: {}", signaturex.getName());
       DatasourceAspect.log.debug("oracle userName:{} ", oraUserName);
       DatasourceAspect.log.debug("===================================================");
       QueryTypeEnum sourceQueryType = signaturex.getMethod().getAnnotation(FlyCheckDatasource.class).queryType();
       Object result = null;
       DbHelper.close();
       if (QueryTypeEnum.JDBC_TEMPLATE == sourceQueryType) {
          DbHelper.init(url, oraUserName, oraPassword);
          result = joinPoint.proceed();
       }else if(QueryTypeEnum.DB_HELPER == sourceQueryType){
          DbHelper.init(url, oraUserName, oraPassword);
          result = joinPoint.proceed();
          DbHelper.close();
       }else if(QueryTypeEnum.JDBC_TEMPLATE_SYSTEM == sourceQueryType){
          DbHelper.init(url, this.username, this.password);
          result = joinPoint.proceed();
       }else if(QueryTypeEnum.DB_HELPER_SYSTEM == sourceQueryType){
          DbHelper.init(url, this.username, this.password);
          result = joinPoint.proceed();
          DbHelper.close();
       }else if(QueryTypeEnum.CLICKHOUSE_JDBC_TEMPLATE == sourceQueryType){
          ClickhouseDbHelper.init(this.clickhouseJdbcUrl, this.clickhouseUsername, this.clickhousePasswd);
          result = joinPoint.proceed();
       }else if(QueryTypeEnum.DB_HELPER_CLICKHOUSE == sourceQueryType){
          ClickhouseDbHelper.init(this.clickhouseJdbcUrl, this.clickhouseUsername, this.clickhousePasswd);
          result = joinPoint.proceed();
          DbHelper.close();
       }
       return result;
    }
}
