package com.taikang.fly.check.dto.flyRuleAudit.FlyRuleAuditRespDto;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class FlyRuleAuditRespDto	// class@000112 from classes.dex
{
    private String auditRejectReason;
    private String auditState;
    private String diagnosisType;
    private String id;
    private String region;
    private String ruleLevel;
    private String ruleName;
    private String ruleScopeApply;

    public void FlyRuleAuditRespDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRuleAuditRespDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof FlyRuleAuditRespDto){
          b = false;
       }else {
          FlyRuleAuditRespDto uFlyRuleAudi = o;
          if (!uFlyRuleAudi.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = uFlyRuleAudi.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String ruleName = this.getRuleName();
             String ruleName1 = uFlyRuleAudi.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String diagnosisTyp = this.getDiagnosisType();
             String diagnosisTyp1 = uFlyRuleAudi.getDiagnosisType();
             if (diagnosisTyp == null) {
                if (diagnosisTyp1 != null) {
                   b = false;
                }
             }else if(diagnosisTyp.equals(diagnosisTyp1)){
             }
             String ruleLevel = this.getRuleLevel();
             String ruleLevel1 = uFlyRuleAudi.getRuleLevel();
             if (ruleLevel == null) {
                if (ruleLevel1 != null) {
                   b = false;
                }
             }else if(ruleLevel.equals(ruleLevel1)){
             }
             String ruleScopeApp = this.getRuleScopeApply();
             String ruleScopeApp1 = uFlyRuleAudi.getRuleScopeApply();
             if (ruleScopeApp == null) {
                if (ruleScopeApp1 != null) {
                   b = false;
                }
             }else if(ruleScopeApp.equals(ruleScopeApp1)){
             }
             String auditState = this.getAuditState();
             String auditState1 = uFlyRuleAudi.getAuditState();
             if (auditState == null) {
                if (auditState1 != null) {
                   b = false;
                }
             }else if(auditState.equals(auditState1)){
             }
             String auditRejectR = this.getAuditRejectReason();
             String auditRejectR1 = uFlyRuleAudi.getAuditRejectReason();
             if (auditRejectR == null) {
                if (auditRejectR1 != null) {
                label_00b5 :
                   b = false;
                }
             }else if(auditRejectR.equals(auditRejectR1)){
             }
             String region = this.getRegion();
             String region1 = uFlyRuleAudi.getRegion();
             if (region == null) {
                if (region1 != null) {
                   b = false;
                }
             }else if(region.equals(region1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getAuditRejectReason(){
       return this.auditRejectReason;
    }
    public String getAuditState(){
       return this.auditState;
    }
    public String getDiagnosisType(){
       return this.diagnosisType;
    }
    public String getId(){
       return this.id;
    }
    public String getRegion(){
       return this.region;
    }
    public String getRuleLevel(){
       return this.ruleLevel;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleScopeApply(){
       return this.ruleScopeApply;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $ruleName = this.getRuleName();
       int i2 = result * 59;
       i1 = ($ruleName == null)? i: $ruleName.hashCode();
       result = i2 + i1;
       String $diagnosisType = this.getDiagnosisType();
       i2 = result * 59;
       i1 = ($diagnosisType == null)? i: $diagnosisType.hashCode();
       result = i2 + i1;
       String $ruleLevel = this.getRuleLevel();
       i2 = result * 59;
       i1 = ($ruleLevel == null)? i: $ruleLevel.hashCode();
       result = i2 + i1;
       String $ruleScopeApply = this.getRuleScopeApply();
       i2 = result * 59;
       i1 = ($ruleScopeApply == null)? i: $ruleScopeApply.hashCode();
       result = i2 + i1;
       String auditState = this.getAuditState();
       i2 = result * 59;
       i1 = (auditState == null)? i: auditState.hashCode();
       String auditRejectR = this.getAuditRejectReason();
       i2 = (i2 + i1) * 59;
       i1 = (auditRejectR == null)? i: auditRejectR.hashCode();
       String region = this.getRegion();
       i1 = (i2 + i1) * 59;
       if (region != null) {
          i = region.hashCode();
       }
       return (i1 + i);
    }
    public void setAuditRejectReason(String auditRejectReason){
       this.auditRejectReason = auditRejectReason;
    }
    public void setAuditState(String auditState){
       this.auditState = auditState;
    }
    public void setDiagnosisType(String diagnosisType){
       this.diagnosisType = diagnosisType;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setRuleLevel(String ruleLevel){
       this.ruleLevel = ruleLevel;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleScopeApply(String ruleScopeApply){
       this.ruleScopeApply = ruleScopeApply;
    }
    public String toString(){
       return "FlyRuleAuditRespDto\(id="+this.getId()+", ruleName="+this.getRuleName()+", diagnosisType="+this.getDiagnosisType()+", ruleLevel="+this.getRuleLevel()+", ruleScopeApply="+this.getRuleScopeApply()+", auditState="+this.getAuditState()+", auditRejectReason="+this.getAuditRejectReason()+", region="+this.getRegion()+"\)";
    }
}
