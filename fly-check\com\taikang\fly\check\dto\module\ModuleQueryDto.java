package com.taikang.fly.check.dto.module.ModuleQueryDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ModuleQueryDto implements Serializable	// class@000197 from classes.dex
{
    private String moduleCode;
    private String moduleName;

    public void ModuleQueryDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ModuleQueryDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof ModuleQueryDto) {
             b = false;
          }else {
             ModuleQueryDto moduleQueryD = o;
             if (!moduleQueryD.canEqual(this)) {
                b = false;
             }else {
                String moduleCode = this.getModuleCode();
                String moduleCode1 = moduleQueryD.getModuleCode();
                if (moduleCode == null) {
                   if (moduleCode1 != null) {
                      b = false;
                   }
                }else if(moduleCode.equals(moduleCode1)){
                }
                String moduleName = this.getModuleName();
                String moduleName1 = moduleQueryD.getModuleName();
                if (moduleName == null) {
                   if (moduleName1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!moduleName.equals(moduleName1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getModuleCode(){
       return this.moduleCode;
    }
    public String getModuleName(){
       return this.moduleName;
    }
    public int hashCode(){
       String $moduleCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($moduleCode = this.getModuleCode()) == null)? i: $moduleCode.hashCode();
       result = i1 + 59;
       String $moduleName = this.getModuleName();
       i1 = result * 59;
       if ($moduleName != null) {
          i = $moduleName.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setModuleCode(String moduleCode){
       this.moduleCode = moduleCode;
    }
    public void setModuleName(String moduleName){
       this.moduleName = moduleName;
    }
    public String toString(){
       return "ModuleQueryDto\(moduleCode="+this.getModuleCode()+", moduleName="+this.getModuleName()+"\)";
    }
}
