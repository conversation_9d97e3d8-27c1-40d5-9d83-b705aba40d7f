package com.taikang.fly.check.mybatis.domain.ModelExport;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ModelExport	// class@000257 from classes.dex
{
    private String exportName;
    private String exportSql;
    private String id;
    private String modelId;

    public void ModelExport(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ModelExport;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof ModelExport) {
             b = false;
          }else {
             ModelExport modelExport = o;
             if (!modelExport.canEqual(this)) {
                b = false;
             }else {
                String id = this.getId();
                String id1 = modelExport.getId();
                if (id == null) {
                   if (id1 != null) {
                      b = false;
                   }
                }else if(id.equals(id1)){
                }
                String modelId = this.getModelId();
                String modelId1 = modelExport.getModelId();
                if (modelId == null) {
                   if (modelId1 != null) {
                      b = false;
                   }
                }else if(modelId.equals(modelId1)){
                }
                String exportName = this.getExportName();
                String exportName1 = modelExport.getExportName();
                if (exportName == null) {
                   if (exportName1 != null) {
                      b = false;
                   }
                }else if(exportName.equals(exportName1)){
                }
                String exportSql = this.getExportSql();
                String exportSql1 = modelExport.getExportSql();
                if (exportSql == null) {
                   if (exportSql1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!exportSql.equals(exportSql1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getExportName(){
       return this.exportName;
    }
    public String getExportSql(){
       return this.exportSql;
    }
    public String getId(){
       return this.id;
    }
    public String getModelId(){
       return this.modelId;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $modelId = this.getModelId();
       int i2 = result * 59;
       i1 = ($modelId == null)? i: $modelId.hashCode();
       result = i2 + i1;
       String $exportName = this.getExportName();
       i2 = result * 59;
       i1 = ($exportName == null)? i: $exportName.hashCode();
       result = i2 + i1;
       String $exportSql = this.getExportSql();
       i1 = result * 59;
       if ($exportSql != null) {
          i = $exportSql.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setExportName(String exportName){
       this.exportName = exportName;
    }
    public void setExportSql(String exportSql){
       this.exportSql = exportSql;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setModelId(String modelId){
       this.modelId = modelId;
    }
    public String toString(){
       return "ModelExport\(id="+this.getId()+", modelId="+this.getModelId()+", exportName="+this.getExportName()+", exportSql="+this.getExportSql()+"\)";
    }
}
