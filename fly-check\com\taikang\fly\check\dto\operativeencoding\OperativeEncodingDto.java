package com.taikang.fly.check.dto.operativeencoding.OperativeEncodingDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class OperativeEncodingDto implements Serializable	// class@00019c from classes.dex
{
    private String createTime;
    private String creator;
    private String id;
    private String modby;
    private String modifyTime;
    private String surgeryCode;
    private String surgeryName;
    private static final long serialVersionUID = 0x1;

    public void OperativeEncodingDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof OperativeEncodingDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof OperativeEncodingDto){
          b = false;
       }else {
          OperativeEncodingDto operativeEnc = o;
          if (!operativeEnc.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = operativeEnc.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String surgeryCode = this.getSurgeryCode();
             String surgeryCode1 = operativeEnc.getSurgeryCode();
             if (surgeryCode == null) {
                if (surgeryCode1 != null) {
                   b = false;
                }
             }else if(surgeryCode.equals(surgeryCode1)){
             }
             String surgeryName = this.getSurgeryName();
             String surgeryName1 = operativeEnc.getSurgeryName();
             if (surgeryName == null) {
                if (surgeryName1 != null) {
                   b = false;
                }
             }else if(surgeryName.equals(surgeryName1)){
             }
             String creator = this.getCreator();
             String creator1 = operativeEnc.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             String createTime = this.getCreateTime();
             String createTime1 = operativeEnc.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modby = this.getModby();
             String modby1 = operativeEnc.getModby();
             if (modby == null) {
                if (modby1 != null) {
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             String modifyTime = this.getModifyTime();
             String modifyTime1 = operativeEnc.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                label_00b0 :
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getId(){
       return this.id;
    }
    public String getModby(){
       return this.modby;
    }
    public String getModifyTime(){
       return this.modifyTime;
    }
    public String getSurgeryCode(){
       return this.surgeryCode;
    }
    public String getSurgeryName(){
       return this.surgeryName;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $surgeryCode = this.getSurgeryCode();
       int i2 = result * 59;
       i1 = ($surgeryCode == null)? i: $surgeryCode.hashCode();
       result = i2 + i1;
       String $surgeryName = this.getSurgeryName();
       i2 = result * 59;
       i1 = ($surgeryName == null)? i: $surgeryName.hashCode();
       result = i2 + i1;
       String $creator = this.getCreator();
       i2 = result * 59;
       i1 = ($creator == null)? i: $creator.hashCode();
       result = i2 + i1;
       String $createTime = this.getCreateTime();
       i2 = result * 59;
       i1 = ($createTime == null)? i: $createTime.hashCode();
       result = i2 + i1;
       String modby = this.getModby();
       i2 = result * 59;
       i1 = (modby == null)? i: modby.hashCode();
       String modifyTime = this.getModifyTime();
       i1 = (i2 + i1) * 59;
       if (modifyTime != null) {
          i = modifyTime.hashCode();
       }
       return (i1 + i);
    }
    public void setCreateTime(String createTime){
       this.createTime = createTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setModby(String modby){
       this.modby = modby;
    }
    public void setModifyTime(String modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setSurgeryCode(String surgeryCode){
       this.surgeryCode = surgeryCode;
    }
    public void setSurgeryName(String surgeryName){
       this.surgeryName = surgeryName;
    }
    public String toString(){
       return "OperativeEncodingDto\(id="+this.getId()+", surgeryCode="+this.getSurgeryCode()+", surgeryName="+this.getSurgeryName()+", creator="+this.getCreator()+", createTime="+this.getCreateTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+"\)";
    }
}
