package com.taikang.fly.check.mybatis.domain.ClickHouseClusterTable;
import java.lang.Object;
import java.lang.String;
import java.util.Date;
import java.lang.StringBuilder;

public class ClickHouseClusterTable	// class@000230 from classes.dex
{
    private String ckTableName;
    private String createSql;
    private Date createTime;
    private String database;
    private String field;
    private String id;
    private String logPath;
    private String oracleTableName;
    private int status;
    private Date updateTime;

    public void ClickHouseClusterTable(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ClickHouseClusterTable;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ClickHouseClusterTable){
          b = false;
       }else {
          ClickHouseClusterTable uClickHouseC = o;
          if (!uClickHouseC.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = uClickHouseC.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String oracleTableN = this.getOracleTableName();
             String oracleTableN1 = uClickHouseC.getOracleTableName();
             if (oracleTableN == null) {
                if (oracleTableN1 != null) {
                   b = false;
                }
             }else if(oracleTableN.equals(oracleTableN1)){
             }
             Date createTime = this.getCreateTime();
             Date createTime1 = uClickHouseC.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             Date updateTime = this.getUpdateTime();
             Date updateTime1 = uClickHouseC.getUpdateTime();
             if (updateTime == null) {
                if (updateTime1 != null) {
                   b = false;
                }
             }else if(updateTime.equals(updateTime1)){
             }
             String logPath = this.getLogPath();
             String logPath1 = uClickHouseC.getLogPath();
             if (logPath == null) {
                if (logPath1 != null) {
                   b = false;
                }
             }else if(logPath.equals(logPath1)){
             }
             String field = this.getField();
             String field1 = uClickHouseC.getField();
             if (field == null) {
                if (field1 != null) {
                label_00a1 :
                   b = false;
                }
             }else if(field.equals(field1)){
             }
             if (this.getStatus() != uClickHouseC.getStatus()) {
                b = false;
             }else {
                String ckTableName = this.getCkTableName();
                String ckTableName1 = uClickHouseC.getCkTableName();
                if (ckTableName == null) {
                   if (ckTableName1 != null) {
                      b = false;
                   }
                }else if(ckTableName.equals(ckTableName1)){
                }
                String database = this.getDatabase();
                String database1 = uClickHouseC.getDatabase();
                if (database == null) {
                   if (database1 != null) {
                   label_00e1 :
                      b = false;
                   }
                }else if(database.equals(database1)){
                }
                String createSql = this.getCreateSql();
                String createSql1 = uClickHouseC.getCreateSql();
                if (createSql == null) {
                   if (createSql1 != null) {
                      b = false;
                   }
                }else if(createSql.equals(createSql1)){
                }
                b = true;
             }
          }
       }
       return b;
    }
    public String getCkTableName(){
       return this.ckTableName;
    }
    public String getCreateSql(){
       return this.createSql;
    }
    public Date getCreateTime(){
       return this.createTime;
    }
    public String getDatabase(){
       return this.database;
    }
    public String getField(){
       return this.field;
    }
    public String getId(){
       return this.id;
    }
    public String getLogPath(){
       return this.logPath;
    }
    public String getOracleTableName(){
       return this.oracleTableName;
    }
    public int getStatus(){
       return this.status;
    }
    public Date getUpdateTime(){
       return this.updateTime;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $oracleTableName = this.getOracleTableName();
       int i2 = result * 59;
       i1 = ($oracleTableName == null)? i: $oracleTableName.hashCode();
       result = i2 + i1;
       Date $createTime = this.getCreateTime();
       i2 = result * 59;
       i1 = ($createTime == null)? i: $createTime.hashCode();
       result = i2 + i1;
       Date $updateTime = this.getUpdateTime();
       i2 = result * 59;
       i1 = ($updateTime == null)? i: $updateTime.hashCode();
       result = i2 + i1;
       String $logPath = this.getLogPath();
       i2 = result * 59;
       i1 = ($logPath == null)? i: $logPath.hashCode();
       result = i2 + i1;
       String field = this.getField();
       i2 = result * 59;
       i1 = (field == null)? i: field.hashCode();
       String ckTableName = this.getCkTableName();
       i2 = (((i2 + i1) * 59) + this.getStatus()) * 59;
       i1 = (ckTableName == null)? i: ckTableName.hashCode();
       String database = this.getDatabase();
       i2 = (i2 + i1) * 59;
       i1 = (database == null)? i: database.hashCode();
       String createSql = this.getCreateSql();
       i1 = (i2 + i1) * 59;
       if (createSql != null) {
          i = createSql.hashCode();
       }
       return (i1 + i);
    }
    public void setCkTableName(String ckTableName){
       this.ckTableName = ckTableName;
    }
    public void setCreateSql(String createSql){
       this.createSql = createSql;
    }
    public void setCreateTime(Date createTime){
       this.createTime = createTime;
    }
    public void setDatabase(String database){
       this.database = database;
    }
    public void setField(String field){
       this.field = field;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setLogPath(String logPath){
       this.logPath = logPath;
    }
    public void setOracleTableName(String oracleTableName){
       this.oracleTableName = oracleTableName;
    }
    public void setStatus(int status){
       this.status = status;
    }
    public void setUpdateTime(Date updateTime){
       this.updateTime = updateTime;
    }
    public String toString(){
       return "ClickHouseClusterTable\(id="+this.getId()+", oracleTableName="+this.getOracleTableName()+", createTime="+this.getCreateTime()+", updateTime="+this.getUpdateTime()+", logPath="+this.getLogPath()+", field="+this.getField()+", status="+this.getStatus()+", ckTableName="+this.getCkTableName()+", database="+this.getDatabase()+", createSql="+this.getCreateSql()+"\)";
    }
}
