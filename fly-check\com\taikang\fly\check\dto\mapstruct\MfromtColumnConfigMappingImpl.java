package com.taikang.fly.check.dto.mapstruct.MfromtColumnConfigMappingImpl;
import com.taikang.fly.check.dto.mapstruct.MfromtColumnConfigMapping;
import java.lang.Object;
import com.taikang.fly.check.mybatis.domain.MfromtColumnConfig;
import com.taikang.fly.check.dto.businesscolconfig.ColConfigRespDto;
import java.lang.Integer;
import java.lang.String;
import com.taikang.fly.check.dto.mapper.common.TypeConversionMapper;
import java.time.LocalDateTime;
import com.taikang.fly.check.utils.DateUtils;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import com.taikang.fly.check.dto.businesscolconfig.ColConfigSaveDto;
import com.taikang.fly.check.security.core.DomainContext;
import com.taikang.fly.check.security.core.ThreadLocalContextHolder;
import com.taikang.fly.check.dto.user.UserDto;
import com.taikang.fly.check.utils.SequenceGenerator;

public class MfromtColumnConfigMappingImpl implements MfromtColumnConfigMapping	// class@000168 from classes.dex
{
    private TypeConversionMapper typeConversionMapper;

    public void MfromtColumnConfigMappingImpl(){
       super();
    }
    public ColConfigRespDto toColConfigRespDto(MfromtColumnConfig mfromtColumnConfig){
       ColConfigRespDto uColConfigRe;
       if (mfromtColumnConfig == null) {
          uColConfigRe = null;
       }else {
          uColConfigRe = new ColConfigRespDto();
          uColConfigRe.setColumnWidth(this.typeConversionMapper.Integer2String(mfromtColumnConfig.getColumnWidth()));
          uColConfigRe.setSortNum(this.typeConversionMapper.Integer2String(mfromtColumnConfig.getSortNum()));
          uColConfigRe.setIsValid(mfromtColumnConfig.getIsValid());
          uColConfigRe.setId(mfromtColumnConfig.getId());
          uColConfigRe.setBusinessId(mfromtColumnConfig.getBusinessId());
          uColConfigRe.setColumnName(mfromtColumnConfig.getColumnName());
          uColConfigRe.setColumnComment(mfromtColumnConfig.getColumnComment());
          uColConfigRe.setCreatedBy(mfromtColumnConfig.getCreatedBy());
          uColConfigRe.setModifier(mfromtColumnConfig.getModifier());
          uColConfigRe.setDictType(mfromtColumnConfig.getDictType());
          uColConfigRe.setParentId(mfromtColumnConfig.getParentId());
          uColConfigRe.setCreatedTime(DateUtils.formatLocalDateTime(mfromtColumnConfig.getCreatedTime()));
          uColConfigRe.setModifyTime(DateUtils.formatLocalDateTime(mfromtColumnConfig.getModifyTime()));
       }
       return uColConfigRe;
    }
    public List toColConfigRespDtoList(List mfromtColumnConfigs){
       List list;
       if (mfromtColumnConfigs == null) {
          list = null;
       }else {
          list = new ArrayList(mfromtColumnConfigs.size());
          Iterator iterator = mfromtColumnConfigs.iterator();
          while (iterator.hasNext()) {
             list.add(this.toColConfigRespDto(iterator.next()));
          }
       }
       return list;
    }
    public MfromtColumnConfig toEntity(ColConfigSaveDto colConfigSaveDto){
       MfromtColumnConfig mfromtColumn;
       if (colConfigSaveDto == null) {
          mfromtColumn = null;
       }else {
          mfromtColumn = new MfromtColumnConfig();
          mfromtColumn.setColumnWidth(this.typeConversionMapper.String2Integer(colConfigSaveDto.getColumnWidth()));
          mfromtColumn.setSortNum(this.typeConversionMapper.String2Integer(colConfigSaveDto.getSortNum()));
          mfromtColumn.setBusinessId(colConfigSaveDto.getBusinessId());
          mfromtColumn.setColumnName(colConfigSaveDto.getColumnName());
          mfromtColumn.setColumnComment(colConfigSaveDto.getColumnComment());
          mfromtColumn.setIsValid(colConfigSaveDto.getIsValid());
          mfromtColumn.setDictType(colConfigSaveDto.getDictType());
          mfromtColumn.setModifyTime(LocalDateTime.now());
          mfromtColumn.setCreatedBy(ThreadLocalContextHolder.getContext().getUserInfo().getName());
          mfromtColumn.setModifier(ThreadLocalContextHolder.getContext().getUserInfo().getName());
          mfromtColumn.setCreatedTime(LocalDateTime.now());
          mfromtColumn.setId(SequenceGenerator.getId());
       }
       return mfromtColumn;
    }
}
