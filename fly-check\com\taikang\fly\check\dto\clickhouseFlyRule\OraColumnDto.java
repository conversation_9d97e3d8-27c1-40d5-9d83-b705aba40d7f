package com.taikang.fly.check.dto.clickhouseFlyRule.OraColumnDto;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class OraColumnDto	// class@0000d0 from classes.dex
{
    private String columnName;
    private String columnType;
    private int indexSign;
    private int partitionSign;
    private int sign;

    public void OraColumnDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof OraColumnDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof OraColumnDto) {
             b = false;
          }else {
             OraColumnDto oraColumnDto = o;
             if (!oraColumnDto.canEqual(this)) {
                b = false;
             }else {
                String columnName = this.getColumnName();
                String columnName1 = oraColumnDto.getColumnName();
                if (columnName == null) {
                   if (columnName1 != null) {
                      b = false;
                   }
                }else if(columnName.equals(columnName1)){
                }
                String columnType = this.getColumnType();
                String columnType1 = oraColumnDto.getColumnType();
                if (columnType == null) {
                   if (columnType1 != null) {
                      b = false;
                   }
                }else if(columnType.equals(columnType1)){
                }
                if (this.getSign() != oraColumnDto.getSign()) {
                   b = false;
                }else if(this.getIndexSign() != oraColumnDto.getIndexSign()){
                   b = false;
                }else if(this.getPartitionSign() != oraColumnDto.getPartitionSign()){
                   b = false;
                }
             }
          }
       }
       return b;
    }
    public String getColumnName(){
       return this.columnName;
    }
    public String getColumnType(){
       return this.columnType;
    }
    public int getIndexSign(){
       return this.indexSign;
    }
    public int getPartitionSign(){
       return this.partitionSign;
    }
    public int getSign(){
       return this.sign;
    }
    public int hashCode(){
       String $columnName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($columnName = this.getColumnName()) == null)? i: $columnName.hashCode();
       result = i1 + 59;
       String $columnType = this.getColumnType();
       i1 = result * 59;
       if ($columnType != null) {
          i = $columnType.hashCode();
       }
       result = i1 + i;
       return ((((((result * 59) + this.getSign()) * 59) + this.getIndexSign()) * 59) + this.getPartitionSign());
    }
    public void setColumnName(String columnName){
       this.columnName = columnName;
    }
    public void setColumnType(String columnType){
       this.columnType = columnType;
    }
    public void setIndexSign(int indexSign){
       this.indexSign = indexSign;
    }
    public void setPartitionSign(int partitionSign){
       this.partitionSign = partitionSign;
    }
    public void setSign(int sign){
       this.sign = sign;
    }
    public String toString(){
       return "OraColumnDto\(columnName="+this.getColumnName()+", columnType="+this.getColumnType()+", sign="+this.getSign()+", indexSign="+this.getIndexSign()+", partitionSign="+this.getPartitionSign()+"\)";
    }
}
