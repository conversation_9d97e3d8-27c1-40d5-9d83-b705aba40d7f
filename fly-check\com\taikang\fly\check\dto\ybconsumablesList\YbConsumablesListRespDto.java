package com.taikang.fly.check.dto.ybconsumablesList.YbConsumablesListRespDto;
import java.io.Serializable;
import java.lang.Long;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class YbConsumablesListRespDto implements Serializable	// class@0001d2 from classes.dex
{
    private String endTime;
    private String hospitalSelfPay;
    private String maxPrice;
    private String oneMaxPrice;
    private String outProInfo;
    private String outpatientSelfPay;
    private String payer;
    private String paymentCategory;
    private String proCode;
    private String proConnotation;
    private String proName;
    private String remark;
    private String startTime;
    private String threeMaxPrice;
    private String twoMaxPrice;
    private String workInjurySelfPay;
    private String yourSelfPay;
    private static final Long serialVersionUID;

    static {
       YbConsumablesListRespDto.serialVersionUID = Long.valueOf(1);
    }
    public void YbConsumablesListRespDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof YbConsumablesListRespDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof YbConsumablesListRespDto){
          b = false;
       }else {
          YbConsumablesListRespDto ybConsumable = o;
          if (!ybConsumable.canEqual(this)) {
             b = false;
          }else {
             String proCode = this.getProCode();
             String proCode1 = ybConsumable.getProCode();
             if (proCode == null) {
                if (proCode1 != null) {
                   b = false;
                }
             }else if(proCode.equals(proCode1)){
             }
             String proName = this.getProName();
             String proName1 = ybConsumable.getProName();
             if (proName == null) {
                if (proName1 != null) {
                   b = false;
                }
             }else if(proName.equals(proName1)){
             }
             String proConnotati = this.getProConnotation();
             String proConnotati1 = ybConsumable.getProConnotation();
             if (proConnotati == null) {
                if (proConnotati1 != null) {
                   b = false;
                }
             }else if(proConnotati.equals(proConnotati1)){
             }
             String outProInfo = this.getOutProInfo();
             String outProInfo1 = ybConsumable.getOutProInfo();
             if (outProInfo == null) {
                if (outProInfo1 != null) {
                   b = false;
                }
             }else if(outProInfo.equals(outProInfo1)){
             }
             String payer = this.getPayer();
             String payer1 = ybConsumable.getPayer();
             if (payer == null) {
                if (payer1 != null) {
                   b = false;
                }
             }else if(payer.equals(payer1)){
             }
             String paymentCateg = this.getPaymentCategory();
             String paymentCateg1 = ybConsumable.getPaymentCategory();
             if (paymentCateg == null) {
                if (paymentCateg1 != null) {
                   b = false;
                }
             }else if(paymentCateg.equals(paymentCateg1)){
             }
             String maxPrice = this.getMaxPrice();
             String maxPrice1 = ybConsumable.getMaxPrice();
             if (maxPrice == null) {
                if (maxPrice1 != null) {
                label_00bb :
                   b = false;
                }
             }else if(maxPrice.equals(maxPrice1)){
             }
             String threeMaxPric = this.getThreeMaxPrice();
             String threeMaxPric1 = ybConsumable.getThreeMaxPrice();
             if (threeMaxPric == null) {
                if (threeMaxPric1 != null) {
                   b = false;
                }
             }else if(threeMaxPric.equals(threeMaxPric1)){
             }
             String twoMaxPrice = this.getTwoMaxPrice();
             String twoMaxPrice1 = ybConsumable.getTwoMaxPrice();
             if (twoMaxPrice == null) {
                if (twoMaxPrice1 != null) {
                label_00ed :
                   b = false;
                }
             }else if(twoMaxPrice.equals(twoMaxPrice1)){
             }
             String oneMaxPrice = this.getOneMaxPrice();
             String oneMaxPrice1 = ybConsumable.getOneMaxPrice();
             if (oneMaxPrice == null) {
                if (oneMaxPrice1 != null) {
                   b = false;
                }
             }else if(oneMaxPrice.equals(oneMaxPrice1)){
             }
             String outpatientSe = this.getOutpatientSelfPay();
             String outpatientSe1 = ybConsumable.getOutpatientSelfPay();
             if (outpatientSe == null) {
                if (outpatientSe1 != null) {
                label_011f :
                   b = false;
                }
             }else if(outpatientSe.equals(outpatientSe1)){
             }
             String hospitalSelf = this.getHospitalSelfPay();
             String hospitalSelf1 = ybConsumable.getHospitalSelfPay();
             if (hospitalSelf == null) {
                if (hospitalSelf1 != null) {
                   b = false;
                }
             }else if(hospitalSelf.equals(hospitalSelf1)){
             }
             String workInjurySe = this.getWorkInjurySelfPay();
             String workInjurySe1 = ybConsumable.getWorkInjurySelfPay();
             if (workInjurySe == null) {
                if (workInjurySe1 != null) {
                   b = false;
                }
             }else if(workInjurySe.equals(workInjurySe1)){
             }
             String yourSelfPay = this.getYourSelfPay();
             String yourSelfPay1 = ybConsumable.getYourSelfPay();
             if (yourSelfPay == null) {
                if (yourSelfPay1 != null) {
                label_0169 :
                   b = false;
                }
             }else if(yourSelfPay.equals(yourSelfPay1)){
             }
             String remark = this.getRemark();
             String remark1 = ybConsumable.getRemark();
             if (remark == null) {
                if (remark1 != null) {
                   b = false;
                }
             }else if(remark.equals(remark1)){
             }
             String startTime = this.getStartTime();
             String startTime1 = ybConsumable.getStartTime();
             if (startTime == null) {
                if (startTime1 != null) {
                label_019b :
                   b = false;
                }
             }else if(startTime.equals(startTime1)){
             }
             String endTime = this.getEndTime();
             String endTime1 = ybConsumable.getEndTime();
             if (endTime == null) {
                if (endTime1 != null) {
                   b = false;
                }
             }else if(endTime.equals(endTime1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getEndTime(){
       return this.endTime;
    }
    public String getHospitalSelfPay(){
       return this.hospitalSelfPay;
    }
    public String getMaxPrice(){
       return this.maxPrice;
    }
    public String getOneMaxPrice(){
       return this.oneMaxPrice;
    }
    public String getOutProInfo(){
       return this.outProInfo;
    }
    public String getOutpatientSelfPay(){
       return this.outpatientSelfPay;
    }
    public String getPayer(){
       return this.payer;
    }
    public String getPaymentCategory(){
       return this.paymentCategory;
    }
    public String getProCode(){
       return this.proCode;
    }
    public String getProConnotation(){
       return this.proConnotation;
    }
    public String getProName(){
       return this.proName;
    }
    public String getRemark(){
       return this.remark;
    }
    public String getStartTime(){
       return this.startTime;
    }
    public String getThreeMaxPrice(){
       return this.threeMaxPrice;
    }
    public String getTwoMaxPrice(){
       return this.twoMaxPrice;
    }
    public String getWorkInjurySelfPay(){
       return this.workInjurySelfPay;
    }
    public String getYourSelfPay(){
       return this.yourSelfPay;
    }
    public int hashCode(){
       String $proCode;
       int PRIME = 59;
       int result = 1;
       int i = (($proCode = this.getProCode()) == null)? 43: $proCode.hashCode();
       result = i + 59;
       String $proName = this.getProName();
       int i1 = result * 59;
       i = ($proName == null)? 43: $proName.hashCode();
       result = i1 + i;
       String $proConnotation = this.getProConnotation();
       i1 = result * 59;
       i = ($proConnotation == null)? 43: $proConnotation.hashCode();
       result = i1 + i;
       String $outProInfo = this.getOutProInfo();
       i1 = result * 59;
       i = ($outProInfo == null)? 43: $outProInfo.hashCode();
       result = i1 + i;
       String $payer = this.getPayer();
       i1 = result * 59;
       i = ($payer == null)? 43: $payer.hashCode();
       result = i1 + i;
       String paymentCateg = this.getPaymentCategory();
       i1 = result * 59;
       i = (paymentCateg == null)? 43: paymentCateg.hashCode();
       String maxPrice = this.getMaxPrice();
       i1 = (i1 + i) * 59;
       i = (maxPrice == null)? 43: maxPrice.hashCode();
       String threeMaxPric = this.getThreeMaxPrice();
       i1 = (i1 + i) * 59;
       i = (threeMaxPric == null)? 43: threeMaxPric.hashCode();
       String twoMaxPrice = this.getTwoMaxPrice();
       i1 = (i1 + i) * 59;
       i = (twoMaxPrice == null)? 43: twoMaxPrice.hashCode();
       String oneMaxPrice = this.getOneMaxPrice();
       i1 = (i1 + i) * 59;
       i = (oneMaxPrice == null)? 43: oneMaxPrice.hashCode();
       String outpatientSe = this.getOutpatientSelfPay();
       i1 = (i1 + i) * 59;
       i = (outpatientSe == null)? 43: outpatientSe.hashCode();
       String hospitalSelf = this.getHospitalSelfPay();
       i1 = (i1 + i) * 59;
       i = (hospitalSelf == null)? 43: hospitalSelf.hashCode();
       String workInjurySe = this.getWorkInjurySelfPay();
       i1 = (i1 + i) * 59;
       i = (workInjurySe == null)? 43: workInjurySe.hashCode();
       String yourSelfPay = this.getYourSelfPay();
       i1 = (i1 + i) * 59;
       i = (yourSelfPay == null)? 43: yourSelfPay.hashCode();
       String remark = this.getRemark();
       i1 = (i1 + i) * 59;
       i = (remark == null)? 43: remark.hashCode();
       String startTime = this.getStartTime();
       i1 = (i1 + i) * 59;
       i = (startTime == null)? 43: startTime.hashCode();
       String endTime = this.getEndTime();
       i1 = (i1 + i) * 59;
       i = (endTime == null)? 43: endTime.hashCode();
       return (i1 + i);
    }
    public void setEndTime(String endTime){
       this.endTime = endTime;
    }
    public void setHospitalSelfPay(String hospitalSelfPay){
       this.hospitalSelfPay = hospitalSelfPay;
    }
    public void setMaxPrice(String maxPrice){
       this.maxPrice = maxPrice;
    }
    public void setOneMaxPrice(String oneMaxPrice){
       this.oneMaxPrice = oneMaxPrice;
    }
    public void setOutProInfo(String outProInfo){
       this.outProInfo = outProInfo;
    }
    public void setOutpatientSelfPay(String outpatientSelfPay){
       this.outpatientSelfPay = outpatientSelfPay;
    }
    public void setPayer(String payer){
       this.payer = payer;
    }
    public void setPaymentCategory(String paymentCategory){
       this.paymentCategory = paymentCategory;
    }
    public void setProCode(String proCode){
       this.proCode = proCode;
    }
    public void setProConnotation(String proConnotation){
       this.proConnotation = proConnotation;
    }
    public void setProName(String proName){
       this.proName = proName;
    }
    public void setRemark(String remark){
       this.remark = remark;
    }
    public void setStartTime(String startTime){
       this.startTime = startTime;
    }
    public void setThreeMaxPrice(String threeMaxPrice){
       this.threeMaxPrice = threeMaxPrice;
    }
    public void setTwoMaxPrice(String twoMaxPrice){
       this.twoMaxPrice = twoMaxPrice;
    }
    public void setWorkInjurySelfPay(String workInjurySelfPay){
       this.workInjurySelfPay = workInjurySelfPay;
    }
    public void setYourSelfPay(String yourSelfPay){
       this.yourSelfPay = yourSelfPay;
    }
    public String toString(){
       return "YbConsumablesListRespDto\(proCode="+this.getProCode()+", proName="+this.getProName()+", proConnotation="+this.getProConnotation()+", outProInfo="+this.getOutProInfo()+", payer="+this.getPayer()+", paymentCategory="+this.getPaymentCategory()+", maxPrice="+this.getMaxPrice()+", threeMaxPrice="+this.getThreeMaxPrice()+", twoMaxPrice="+this.getTwoMaxPrice()+", oneMaxPrice="+this.getOneMaxPrice()+", outpatientSelfPay="+this.getOutpatientSelfPay()+", hospitalSelfPay="+this.getHospitalSelfPay()+", workInjurySelfPay="+this.getWorkInjurySelfPay()+", yourSelfPay="+this.getYourSelfPay()+", remark="+this.getRemark()+", startTime="+this.getStartTime()+", endTime="+this.getEndTime()+"\)";
    }
}
