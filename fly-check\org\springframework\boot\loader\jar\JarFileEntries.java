package org.springframework.boot.loader.jar.JarFileEntries;
import org.springframework.boot.loader.jar.CentralDirectoryVisitor;
import java.lang.Iterable;
import java.util.jar.Attributes$Name;
import java.lang.String;
import java.lang.Runtime;
import java.lang.Class;
import java.lang.reflect.Method;
import java.lang.Object;
import java.lang.Integer;
import org.springframework.boot.loader.jar.JarFile;
import org.springframework.boot.loader.jar.JarEntryFilter;
import org.springframework.boot.loader.jar.JarFileEntries$1;
import java.util.Map;
import java.util.Collections;
import java.lang.Boolean;
import org.springframework.boot.loader.jar.AsciiBytes;
import org.springframework.boot.loader.jar.FileHeader;
import java.lang.CharSequence;
import org.springframework.boot.loader.jar.CentralDirectoryFileHeader;
import org.springframework.boot.loader.jar.JarEntry;
import org.springframework.boot.loader.data.RandomAccessData;
import java.lang.IllegalStateException;
import java.lang.Throwable;
import java.lang.StringBuilder;
import org.springframework.boot.loader.jar.Bytes;
import java.util.Arrays;
import java.util.jar.Manifest;
import java.util.jar.Attributes;
import java.io.InputStream;
import org.springframework.boot.loader.jar.ZipInflaterInputStream;
import java.util.Iterator;
import org.springframework.boot.loader.jar.JarFileEntries$EntryIterator;
import org.springframework.boot.loader.jar.CentralDirectoryEndRecord;

class JarFileEntries implements CentralDirectoryVisitor, Iterable	// class@000559 from classes.dex
{
    private RandomAccessData centralDirectoryData;
    private int[] centralDirectoryOffsets;
    private final Map entriesCache;
    private final JarEntryFilter filter;
    private int[] hashCodes;
    private final JarFile jarFile;
    private Boolean multiReleaseJar;
    private int[] positions;
    private int size;
    private static final int BASE_VERSION = 8;
    protected static final int ENTRY_CACHE_SIZE = 25;
    private static final long LOCAL_FILE_HEADER_SIZE = 0x1e;
    private static final String META_INF_PREFIX = "META-INF/";
    private static final Attributes$Name MULTI_RELEASE;
    private static final char NO_SUFFIX;
    private static final int RUNTIME_VERSION;
    private static final char SLASH;

    static {
       int version;
       Attributes$Name name = new Attributes$Name("Multi-Release");
       try{
          JarFileEntries.MULTI_RELEASE = name;
          Class[] uClassArray = new Class[0];
          Object[] objArray = new Object[0];
          Object runtimeVersion = Runtime.class.getMethod("version", uClassArray).invoke(null, objArray);
          uClassArray = new Class[0];
          Object[] objArray1 = new Object[0];
          version = runtimeVersion.getClass().getMethod("major", uClassArray).invoke(runtimeVersion, objArray1).intValue();
       }catch(java.lang.Throwable e0){
          version = 8;
       }
       JarFileEntries.RUNTIME_VERSION = version;
    }
    void JarFileEntries(JarFile jarFile,JarEntryFilter filter){
       super();
       this.entriesCache = Collections.synchronizedMap(new JarFileEntries$1(this, 16, 0.75f, true));
       this.jarFile = jarFile;
       this.filter = filter;
       if (JarFileEntries.RUNTIME_VERSION == 8) {
          this.multiReleaseJar = Boolean.valueOf(false);
       }
       return;
    }
    static JarFile access$000(JarFileEntries x0){
       return x0.jarFile;
    }
    static int access$200(JarFileEntries x0){
       return x0.size;
    }
    static int[] access$300(JarFileEntries x0){
       return x0.positions;
    }
    static FileHeader access$400(JarFileEntries x0,int x1,Class x2,boolean x3,AsciiBytes x4){
       return x0.getEntry(x1, x2, x3, x4);
    }
    private void add(AsciiBytes name,int dataOffset){
       this.hashCodes[this.size] = name.hashCode();
       this.centralDirectoryOffsets[this.size] = dataOffset;
       this.positions[this.size] = this.size;
       this.size = this.size + 1;
    }
    private AsciiBytes applyFilter(AsciiBytes name){
       if (this.filter != null) {
          name = this.filter.apply(name);
       }
       return name;
    }
    private FileHeader doGetEntry(CharSequence name,Class type,boolean cacheEntry,AsciiBytes nameAlias){
       FileHeader entry;
       int hashCode = AsciiBytes.hashCode(name);
       if ((entry = this.getEntry(hashCode, name, 0, type, cacheEntry, nameAlias)) == null) {
          hashCode = AsciiBytes.hashCode(hashCode, '/');
          entry = this.getEntry(hashCode, name, '/', type, cacheEntry, nameAlias);
       }
       return entry;
    }
    private FileHeader getEntry(int hashCode,CharSequence name,char suffix,Class type,boolean cacheEntry,AsciiBytes nameAlias){
       FileHeader entry;
       int index = this.getFirstIndex(hashCode);
       while (true) {
          if (index >= 0 && (index < this.size && this.hashCodes[index] == hashCode)) {
             entry = this.getEntry(index, type, cacheEntry, nameAlias);
             if (entry.hasName(name, suffix)) {
                break ;
             }else {
                index++;
             }
          }else {
             entry = null;
             break ;
          }
       }
       return entry;
    }
    private FileHeader getEntry(int index,Class type,boolean cacheEntry,AsciiBytes nameAlias){
       FileHeader uFileHeader;
       try{
          FileHeader entry = ((uFileHeader = this.entriesCache.get(Integer.valueOf(index))) != null)? uFileHeader: CentralDirectoryFileHeader.fromRandomAccessData(this.centralDirectoryData, this.centralDirectoryOffsets[index], this.filter);
          if (CentralDirectoryFileHeader.class.equals(entry.getClass()) && type.equals(JarEntry.class)) {
             entry = new JarEntry(this.jarFile, entry, nameAlias);
          }
          if (cacheEntry && uFileHeader != entry) {
             this.entriesCache.put(Integer.valueOf(index), entry);
          }
          return entry;
       }catch(java.io.IOException e3){
          throw new IllegalStateException(e3);
       }
    }
    private FileHeader getEntry(CharSequence name,Class type,boolean cacheEntry){
       FileHeader versionedEntry;
       FileHeader entry = this.doGetEntry(name, type, cacheEntry, null);
       if (!this.isMetaInfEntry(name) && this.isMultiReleaseJar()) {
          int version = JarFileEntries.RUNTIME_VERSION;
          AsciiBytes nameAlias = (entry instanceof JarEntry)? entry.getAsciiBytesName(): new AsciiBytes(name.toString());
          while (true) {
             if (version > 8) {
                if ((versionedEntry = this.doGetEntry("".append("META-INF/versions/").append(version).append("/").append(name).toString(), type, cacheEntry, nameAlias)) != null) {
                   break ;
                }else {
                   version--;
                }
             }
          }
          return versionedEntry;
       }
       versionedEntry = entry;
    }
    private RandomAccessData getEntryData(FileHeader entry){
       RandomAccessData data = this.jarFile.getData();
       byte[] localHeader = data.read(entry.getLocalHeaderOffset(), 30);
       long nameLength = Bytes.littleEndianValue(localHeader, 26, 2);
       long extraLength = Bytes.littleEndianValue(localHeader, 28, 2);
       return data.getSubsection((((entry.getLocalHeaderOffset() + 30) + nameLength) + extraLength), entry.getCompressedSize());
    }
    private int getFirstIndex(int hashCode){
       int index;
       int i;
       if ((index = Arrays.binarySearch(this.hashCodes, 0, this.size, hashCode)) < 0) {
          i = -1;
       }else {
          int i1 = index;
          while (i1 > 0) {
             int i2 = i1 - 1;
             if (this.hashCodes[i2] == hashCode) {
                index = i1 - 1;
                i1 = index;
             }else {
                break ;
             }
          }
          i = i1;
       }
       return i;
    }
    private boolean isMetaInfEntry(CharSequence name){
       return name.toString().startsWith("META-INF/");
    }
    private boolean isMultiReleaseJar(){
       Boolean multiRelease;
       boolean b;
       Manifest manifest;
       if ((multiRelease = this.multiReleaseJar) != null) {
          b = multiRelease.booleanValue();
       }else if((manifest = this.jarFile.getManifest()) == null){
          multiRelease = Boolean.valueOf(false);
       }else {
          multiRelease = Boolean.valueOf(manifest.getMainAttributes().containsKey(JarFileEntries.MULTI_RELEASE));
       }
       return b;
    }
    private void sort(int left,int right){
       if (left < right) {
          int i = this.hashCodes[(((right - left) / 2) + left)];
          int i1 = left;
          int i2 = right;
          while (i1 <= i2) {
             while (this.hashCodes[i1] < i) {
                i1 = i1 + 1;
             }
             while (this.hashCodes[i2] > i) {
                i2 = i2 - 1;
             }
             if (i1 <= i2) {
                this.swap(i1, i2);
                i1 = i1 + 1;
                i2 = i2 - 1;
             }
          }
          if (left < i2) {
             this.sort(left, i2);
          }
          if (right > i1) {
             this.sort(i1, right);
          }
       }
       return;
    }
    private void swap(int i,int j){
       this.swap(this.hashCodes, i, j);
       this.swap(this.centralDirectoryOffsets, i, j);
       this.swap(this.positions, i, j);
    }
    private void swap(int[] array,int i,int j){
       int temp = array[i];
       array[i] = array[j];
       array[j] = temp;
    }
    public void clearCache(){
       this.entriesCache.clear();
    }
    public boolean containsEntry(CharSequence name){
       boolean b = true;
       if (this.getEntry(name, FileHeader.class, b) == null) {
          b = false;
       }
       return b;
    }
    public JarEntry getEntry(CharSequence name){
       return this.getEntry(name, JarEntry.class, true);
    }
    public RandomAccessData getEntryData(String name){
       FileHeader entry;
       RandomAccessData randomAccess = ((entry = this.getEntry(name, FileHeader.class, false)) == null)? null: this.getEntryData(entry);
       return randomAccess;
    }
    public InputStream getInputStream(String name){
       FileHeader entry = this.getEntry(name, FileHeader.class, false);
       return this.getInputStream(entry);
    }
    public InputStream getInputStream(FileHeader entry){
       InputStream inputStream;
       if (entry == null) {
          inputStream = null;
       }else {
          inputStream = this.getEntryData(entry).getInputStream();
          if (entry.getMethod() == 8) {
             inputStream = new ZipInflaterInputStream(inputStream, (int)entry.getSize());
          }
       }
       return inputStream;
    }
    int getSize(){
       return this.size;
    }
    public Iterator iterator(){
       return new JarFileEntries$EntryIterator(this, null);
    }
    public void visitEnd(){
       this.sort(0, (this.size - 1));
       int[] positions = this.positions;
       int[] ointArray = new int[positions.length];
       this.positions = ointArray;
       for (int i = 0; i < this.size; i++) {
          this.positions[positions[i]] = i;
       }
       return;
    }
    public void visitFileHeader(CentralDirectoryFileHeader fileHeader,int dataOffset){
       AsciiBytes name;
       if ((name = this.applyFilter(fileHeader.getName())) != null) {
          this.add(name, dataOffset);
       }
       return;
    }
    public void visitStart(CentralDirectoryEndRecord endRecord,RandomAccessData centralDirectoryData){
       int maxSize = endRecord.getNumberOfRecords();
       this.centralDirectoryData = centralDirectoryData;
       int[] ointArray = new int[maxSize];
       this.hashCodes = ointArray;
       ointArray = new int[maxSize];
       this.centralDirectoryOffsets = ointArray;
       ointArray = new int[maxSize];
       this.positions = ointArray;
    }
}
