package com.taikang.fly.check.vo.drg.DrgAuditTaskVo;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DrgAuditTaskVo	// class@00035e from classes.dex
{
    private String chkReqCode;
    private String chkSchmNo;
    private String chkTaskBchno;
    private String fitrDataWay;
    private String setlBegndate;
    private String setlEnddate;
    private String taskExeCrtStg;

    public void DrgAuditTaskVo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgAuditTaskVo;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof DrgAuditTaskVo){
          b = false;
       }else {
          DrgAuditTaskVo uDrgAuditTas = o;
          if (!uDrgAuditTas.canEqual(this)) {
             b = false;
          }else {
             String chkReqCode = this.getChkReqCode();
             String chkReqCode1 = uDrgAuditTas.getChkReqCode();
             if (chkReqCode == null) {
                if (chkReqCode1 != null) {
                   b = false;
                }
             }else if(chkReqCode.equals(chkReqCode1)){
             }
             String chkSchmNo = this.getChkSchmNo();
             String chkSchmNo1 = uDrgAuditTas.getChkSchmNo();
             if (chkSchmNo == null) {
                if (chkSchmNo1 != null) {
                   b = false;
                }
             }else if(chkSchmNo.equals(chkSchmNo1)){
             }
             String fitrDataWay = this.getFitrDataWay();
             String fitrDataWay1 = uDrgAuditTas.getFitrDataWay();
             if (fitrDataWay == null) {
                if (fitrDataWay1 != null) {
                   b = false;
                }
             }else if(fitrDataWay.equals(fitrDataWay1)){
             }
             String chkTaskBchno = this.getChkTaskBchno();
             String chkTaskBchno1 = uDrgAuditTas.getChkTaskBchno();
             if (chkTaskBchno == null) {
                if (chkTaskBchno1 != null) {
                   b = false;
                }
             }else if(chkTaskBchno.equals(chkTaskBchno1)){
             }
             String setlBegndate = this.getSetlBegndate();
             String setlBegndate1 = uDrgAuditTas.getSetlBegndate();
             if (setlBegndate == null) {
                if (setlBegndate1 != null) {
                   b = false;
                }
             }else if(setlBegndate.equals(setlBegndate1)){
             }
             String setlEnddate = this.getSetlEnddate();
             String setlEnddate1 = uDrgAuditTas.getSetlEnddate();
             if (setlEnddate == null) {
                if (setlEnddate1 != null) {
                label_0098 :
                   b = false;
                }
             }else if(setlEnddate.equals(setlEnddate1)){
             }
             String taskExeCrtSt = this.getTaskExeCrtStg();
             String taskExeCrtSt1 = uDrgAuditTas.getTaskExeCrtStg();
             if (taskExeCrtSt == null) {
                if (taskExeCrtSt1 != null) {
                   b = false;
                }
             }else if(taskExeCrtSt.equals(taskExeCrtSt1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getChkReqCode(){
       return this.chkReqCode;
    }
    public String getChkSchmNo(){
       return this.chkSchmNo;
    }
    public String getChkTaskBchno(){
       return this.chkTaskBchno;
    }
    public String getFitrDataWay(){
       return this.fitrDataWay;
    }
    public String getSetlBegndate(){
       return this.setlBegndate;
    }
    public String getSetlEnddate(){
       return this.setlEnddate;
    }
    public String getTaskExeCrtStg(){
       return this.taskExeCrtStg;
    }
    public int hashCode(){
       String $chkReqCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($chkReqCode = this.getChkReqCode()) == null)? i: $chkReqCode.hashCode();
       result = i1 + 59;
       String $chkSchmNo = this.getChkSchmNo();
       int i2 = result * 59;
       i1 = ($chkSchmNo == null)? i: $chkSchmNo.hashCode();
       result = i2 + i1;
       String $fitrDataWay = this.getFitrDataWay();
       i2 = result * 59;
       i1 = ($fitrDataWay == null)? i: $fitrDataWay.hashCode();
       result = i2 + i1;
       String $chkTaskBchno = this.getChkTaskBchno();
       i2 = result * 59;
       i1 = ($chkTaskBchno == null)? i: $chkTaskBchno.hashCode();
       result = i2 + i1;
       String $setlBegndate = this.getSetlBegndate();
       i2 = result * 59;
       i1 = ($setlBegndate == null)? i: $setlBegndate.hashCode();
       result = i2 + i1;
       String setlEnddate = this.getSetlEnddate();
       i2 = result * 59;
       i1 = (setlEnddate == null)? i: setlEnddate.hashCode();
       String taskExeCrtSt = this.getTaskExeCrtStg();
       i1 = (i2 + i1) * 59;
       if (taskExeCrtSt != null) {
          i = taskExeCrtSt.hashCode();
       }
       return (i1 + i);
    }
    public void setChkReqCode(String chkReqCode){
       this.chkReqCode = chkReqCode;
    }
    public void setChkSchmNo(String chkSchmNo){
       this.chkSchmNo = chkSchmNo;
    }
    public void setChkTaskBchno(String chkTaskBchno){
       this.chkTaskBchno = chkTaskBchno;
    }
    public void setFitrDataWay(String fitrDataWay){
       this.fitrDataWay = fitrDataWay;
    }
    public void setSetlBegndate(String setlBegndate){
       this.setlBegndate = setlBegndate;
    }
    public void setSetlEnddate(String setlEnddate){
       this.setlEnddate = setlEnddate;
    }
    public void setTaskExeCrtStg(String taskExeCrtStg){
       this.taskExeCrtStg = taskExeCrtStg;
    }
    public String toString(){
       return "DrgAuditTaskVo\(chkReqCode="+this.getChkReqCode()+", chkSchmNo="+this.getChkSchmNo()+", fitrDataWay="+this.getFitrDataWay()+", chkTaskBchno="+this.getChkTaskBchno()+", setlBegndate="+this.getSetlBegndate()+", setlEnddate="+this.getSetlEnddate()+", taskExeCrtStg="+this.getTaskExeCrtStg()+"\)";
    }
}
