package com.taikang.fly.check.dto.TableSpacesDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class TableSpacesDto implements Serializable	// class@0000b3 from classes.dex
{
    private String freeBytes;
    private String isOpen;
    private boolean reductionFlag;
    private String tablespaceName;
    private String totalBytes;
    private String use;
    private String useBytes;
    private static final long serialVersionUID = 0x1;

    public void TableSpacesDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof TableSpacesDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof TableSpacesDto){
          b = false;
       }else {
          TableSpacesDto tableSpacesD = o;
          if (!tableSpacesD.canEqual(this)) {
             b = false;
          }else {
             String tablespaceNa = this.getTablespaceName();
             String tablespaceNa1 = tableSpacesD.getTablespaceName();
             if (tablespaceNa == null) {
                if (tablespaceNa1 != null) {
                   b = false;
                }
             }else if(tablespaceNa.equals(tablespaceNa1)){
             }
             String totalBytes = this.getTotalBytes();
             String totalBytes1 = tableSpacesD.getTotalBytes();
             if (totalBytes == null) {
                if (totalBytes1 != null) {
                   b = false;
                }
             }else if(totalBytes.equals(totalBytes1)){
             }
             String useBytes = this.getUseBytes();
             String useBytes1 = tableSpacesD.getUseBytes();
             if (useBytes == null) {
                if (useBytes1 != null) {
                   b = false;
                }
             }else if(useBytes.equals(useBytes1)){
             }
             String freeBytes = this.getFreeBytes();
             String freeBytes1 = tableSpacesD.getFreeBytes();
             if (freeBytes == null) {
                if (freeBytes1 != null) {
                   b = false;
                }
             }else if(freeBytes.equals(freeBytes1)){
             }
             String use = this.getUse();
             String use1 = tableSpacesD.getUse();
             if (use == null) {
                if (use1 != null) {
                   b = false;
                }
             }else if(use.equals(use1)){
             }
             if (this.isReductionFlag() != tableSpacesD.isReductionFlag()) {
                b = false;
             }else {
                String isOpen = this.getIsOpen();
                String isOpen1 = tableSpacesD.getIsOpen();
                if (isOpen == null) {
                   if (isOpen1 != null) {
                   label_009d :
                      b = false;
                   }
                }else if(isOpen.equals(isOpen1)){
                }
                b = true;
             }
          }
       }
       return b;
    }
    public String getFreeBytes(){
       return this.freeBytes;
    }
    public String getIsOpen(){
       return this.isOpen;
    }
    public String getTablespaceName(){
       return this.tablespaceName;
    }
    public String getTotalBytes(){
       return this.totalBytes;
    }
    public String getUse(){
       return this.use;
    }
    public String getUseBytes(){
       return this.useBytes;
    }
    public int hashCode(){
       String $tablespaceName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($tablespaceName = this.getTablespaceName()) == null)? i: $tablespaceName.hashCode();
       result = i1 + 59;
       String $totalBytes = this.getTotalBytes();
       int i2 = result * 59;
       i1 = ($totalBytes == null)? i: $totalBytes.hashCode();
       result = i2 + i1;
       String $useBytes = this.getUseBytes();
       i2 = result * 59;
       i1 = ($useBytes == null)? i: $useBytes.hashCode();
       result = i2 + i1;
       String $freeBytes = this.getFreeBytes();
       i2 = result * 59;
       i1 = ($freeBytes == null)? i: $freeBytes.hashCode();
       result = i2 + i1;
       String $use = this.getUse();
       i2 = result * 59;
       i1 = ($use == null)? i: $use.hashCode();
       result = i2 + i1;
       i2 = result * 59;
       i1 = (this.isReductionFlag())? 79: 97;
       String isOpen = this.getIsOpen();
       i1 = (i2 + i1) * 59;
       if (isOpen != null) {
          i = isOpen.hashCode();
       }
       return (i1 + i);
    }
    public boolean isReductionFlag(){
       return this.reductionFlag;
    }
    public void setFreeBytes(String freeBytes){
       this.freeBytes = freeBytes;
    }
    public void setIsOpen(String isOpen){
       this.isOpen = isOpen;
    }
    public void setReductionFlag(boolean reductionFlag){
       this.reductionFlag = reductionFlag;
    }
    public void setTablespaceName(String tablespaceName){
       this.tablespaceName = tablespaceName;
    }
    public void setTotalBytes(String totalBytes){
       this.totalBytes = totalBytes;
    }
    public void setUse(String use){
       this.use = use;
    }
    public void setUseBytes(String useBytes){
       this.useBytes = useBytes;
    }
    public String toString(){
       return "TableSpacesDto\(tablespaceName="+this.getTablespaceName()+", totalBytes="+this.getTotalBytes()+", useBytes="+this.getUseBytes()+", freeBytes="+this.getFreeBytes()+", use="+this.getUse()+", reductionFlag="+this.isReductionFlag()+", isOpen="+this.getIsOpen()+"\)";
    }
}
