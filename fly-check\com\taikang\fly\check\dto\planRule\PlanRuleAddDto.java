package com.taikang.fly.check.dto.planRule.PlanRuleAddDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class PlanRuleAddDto implements Serializable	// class@0001aa from classes.dex
{
    private String medicalCategory;
    private String planId;
    private String ps;
    private String ruleId;
    private String ruleIntension;
    private String ruleName;
    private String ruleType;
    private String sourceOfRule;
    private static final long serialVersionUID = 0x1;

    public void PlanRuleAddDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof PlanRuleAddDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof PlanRuleAddDto){
          b = false;
       }else {
          PlanRuleAddDto planRuleAddD = o;
          if (!planRuleAddD.canEqual(this)) {
             b = false;
          }else {
             String ruleId = this.getRuleId();
             String ruleId1 = planRuleAddD.getRuleId();
             if (ruleId == null) {
                if (ruleId1 != null) {
                   b = false;
                }
             }else if(ruleId.equals(ruleId1)){
             }
             String planId = this.getPlanId();
             String planId1 = planRuleAddD.getPlanId();
             if (planId == null) {
                if (planId1 != null) {
                   b = false;
                }
             }else if(planId.equals(planId1)){
             }
             String ruleName = this.getRuleName();
             String ruleName1 = planRuleAddD.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String sourceOfRule = this.getSourceOfRule();
             String sourceOfRule1 = planRuleAddD.getSourceOfRule();
             if (sourceOfRule == null) {
                if (sourceOfRule1 != null) {
                   b = false;
                }
             }else if(sourceOfRule.equals(sourceOfRule1)){
             }
             String ruleIntensio = this.getRuleIntension();
             String ruleIntensio1 = planRuleAddD.getRuleIntension();
             if (ruleIntensio == null) {
                if (ruleIntensio1 != null) {
                   b = false;
                }
             }else if(ruleIntensio.equals(ruleIntensio1)){
             }
             String ps = this.getPs();
             String ps1 = planRuleAddD.getPs();
             if (ps == null) {
                if (ps1 != null) {
                label_009d :
                   b = false;
                }
             }else if(ps.equals(ps1)){
             }
             String ruleType = this.getRuleType();
             String ruleType1 = planRuleAddD.getRuleType();
             if (ruleType == null) {
                if (ruleType1 != null) {
                   b = false;
                }
             }else if(ruleType.equals(ruleType1)){
             }
             String medicalCateg = this.getMedicalCategory();
             String medicalCateg1 = planRuleAddD.getMedicalCategory();
             if (medicalCateg == null) {
                if (medicalCateg1 != null) {
                label_00cb :
                   b = false;
                }
             }else if(medicalCateg.equals(medicalCateg1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getMedicalCategory(){
       return this.medicalCategory;
    }
    public String getPlanId(){
       return this.planId;
    }
    public String getPs(){
       return this.ps;
    }
    public String getRuleId(){
       return this.ruleId;
    }
    public String getRuleIntension(){
       return this.ruleIntension;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleType(){
       return this.ruleType;
    }
    public String getSourceOfRule(){
       return this.sourceOfRule;
    }
    public int hashCode(){
       String $ruleId;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($ruleId = this.getRuleId()) == null)? i: $ruleId.hashCode();
       result = i1 + 59;
       String $planId = this.getPlanId();
       int i2 = result * 59;
       i1 = ($planId == null)? i: $planId.hashCode();
       result = i2 + i1;
       String $ruleName = this.getRuleName();
       i2 = result * 59;
       i1 = ($ruleName == null)? i: $ruleName.hashCode();
       result = i2 + i1;
       String $sourceOfRule = this.getSourceOfRule();
       i2 = result * 59;
       i1 = ($sourceOfRule == null)? i: $sourceOfRule.hashCode();
       result = i2 + i1;
       String $ruleIntension = this.getRuleIntension();
       i2 = result * 59;
       i1 = ($ruleIntension == null)? i: $ruleIntension.hashCode();
       result = i2 + i1;
       String ps = this.getPs();
       i2 = result * 59;
       i1 = (ps == null)? i: ps.hashCode();
       String ruleType = this.getRuleType();
       i2 = (i2 + i1) * 59;
       i1 = (ruleType == null)? i: ruleType.hashCode();
       String medicalCateg = this.getMedicalCategory();
       i1 = (i2 + i1) * 59;
       if (medicalCateg != null) {
          i = medicalCateg.hashCode();
       }
       return (i1 + i);
    }
    public void setMedicalCategory(String medicalCategory){
       this.medicalCategory = medicalCategory;
    }
    public void setPlanId(String planId){
       this.planId = planId;
    }
    public void setPs(String ps){
       this.ps = ps;
    }
    public void setRuleId(String ruleId){
       this.ruleId = ruleId;
    }
    public void setRuleIntension(String ruleIntension){
       this.ruleIntension = ruleIntension;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleType(String ruleType){
       this.ruleType = ruleType;
    }
    public void setSourceOfRule(String sourceOfRule){
       this.sourceOfRule = sourceOfRule;
    }
    public String toString(){
       return "PlanRuleAddDto\(ruleId="+this.getRuleId()+", planId="+this.getPlanId()+", ruleName="+this.getRuleName()+", sourceOfRule="+this.getSourceOfRule()+", ruleIntension="+this.getRuleIntension()+", ps="+this.getPs()+", ruleType="+this.getRuleType()+", medicalCategory="+this.getMedicalCategory()+"\)";
    }
}
