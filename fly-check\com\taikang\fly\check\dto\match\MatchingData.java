package com.taikang.fly.check.dto.match.MatchingData;
import java.io.Serializable;
import java.lang.Object;
import java.util.ArrayList;
import java.util.List;
import java.lang.String;
import java.lang.StringBuilder;

public class MatchingData implements Serializable	// class@00018b from classes.dex
{
    private List matchFailData;
    private List standardData;
    private static final long serialVersionUID = 0x21e31acb44c4c6a7;

    public void MatchingData(){
       super();
       this.matchFailData = new ArrayList();
       this.standardData = new ArrayList();
    }
    protected boolean canEqual(Object other){
       return other instanceof MatchingData;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof MatchingData) {
             b = false;
          }else if(!o.canEqual(this)){
             b = false;
          }
       }
       return b;
    }
    public List getMatchFailData(){
       return this.matchFailData;
    }
    public List getStandardData(){
       return this.standardData;
    }
    public int hashCode(){
       int result = 1;
       return 1;
    }
    public void setMatchFailData(List matchFailData){
       this.matchFailData = matchFailData;
    }
    public void setStandardData(List standardData){
       this.standardData = standardData;
    }
    public String toString(){
       return "MatchingData\(matchFailData="+this.getMatchFailData()+", standardData="+this.getStandardData()+"\)";
    }
}
