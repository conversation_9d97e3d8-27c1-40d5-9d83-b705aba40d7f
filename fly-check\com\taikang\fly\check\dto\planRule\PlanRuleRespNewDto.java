package com.taikang.fly.check.dto.planRule.PlanRuleRespNewDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class PlanRuleRespNewDto implements Serializable	// class@0001ab from classes.dex
{
    private String creater;
    private String diagnosisType;
    private String planId;
    private String region;
    private String ruleCategory1;
    private String ruleLevel;
    private String ruleName;
    private String state;
    private static final long serialVersionUID = 0x1;

    public void PlanRuleRespNewDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof PlanRuleRespNewDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof PlanRuleRespNewDto){
          b = false;
       }else {
          PlanRuleRespNewDto planRuleResp = o;
          if (!planRuleResp.canEqual(this)) {
             b = false;
          }else {
             String creater = this.getCreater();
             String creater1 = planRuleResp.getCreater();
             if (creater == null) {
                if (creater1 != null) {
                   b = false;
                }
             }else if(creater.equals(creater1)){
             }
             String diagnosisTyp = this.getDiagnosisType();
             String diagnosisTyp1 = planRuleResp.getDiagnosisType();
             if (diagnosisTyp == null) {
                if (diagnosisTyp1 != null) {
                   b = false;
                }
             }else if(diagnosisTyp.equals(diagnosisTyp1)){
             }
             String ruleName = this.getRuleName();
             String ruleName1 = planRuleResp.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String state = this.getState();
             String state1 = planRuleResp.getState();
             if (state == null) {
                if (state1 != null) {
                   b = false;
                }
             }else if(state.equals(state1)){
             }
             String ruleCategory = this.getRuleCategory1();
             String ruleCategory1 = planRuleResp.getRuleCategory1();
             if (ruleCategory == null) {
                if (ruleCategory1 != null) {
                   b = false;
                }
             }else if(ruleCategory.equals(ruleCategory1)){
             }
             String ruleLevel = this.getRuleLevel();
             String ruleLevel1 = planRuleResp.getRuleLevel();
             if (ruleLevel == null) {
                if (ruleLevel1 != null) {
                   b = false;
                }
             }else if(ruleLevel.equals(ruleLevel1)){
             }
             String region = this.getRegion();
             String region1 = planRuleResp.getRegion();
             if (region == null) {
                if (region1 != null) {
                label_00b5 :
                   b = false;
                }
             }else if(region.equals(region1)){
             }
             String planId = this.getPlanId();
             String planId1 = planRuleResp.getPlanId();
             if (planId == null) {
                if (planId1 != null) {
                   b = false;
                }
             }else if(planId.equals(planId1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getCreater(){
       return this.creater;
    }
    public String getDiagnosisType(){
       return this.diagnosisType;
    }
    public String getPlanId(){
       return this.planId;
    }
    public String getRegion(){
       return this.region;
    }
    public String getRuleCategory1(){
       return this.ruleCategory1;
    }
    public String getRuleLevel(){
       return this.ruleLevel;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getState(){
       return this.state;
    }
    public int hashCode(){
       String $creater;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($creater = this.getCreater()) == null)? i: $creater.hashCode();
       result = i1 + 59;
       String $diagnosisType = this.getDiagnosisType();
       int i2 = result * 59;
       i1 = ($diagnosisType == null)? i: $diagnosisType.hashCode();
       result = i2 + i1;
       String $ruleName = this.getRuleName();
       i2 = result * 59;
       i1 = ($ruleName == null)? i: $ruleName.hashCode();
       result = i2 + i1;
       String $state = this.getState();
       i2 = result * 59;
       i1 = ($state == null)? i: $state.hashCode();
       result = i2 + i1;
       String $ruleCategory1 = this.getRuleCategory1();
       i2 = result * 59;
       i1 = ($ruleCategory1 == null)? i: $ruleCategory1.hashCode();
       result = i2 + i1;
       String ruleLevel = this.getRuleLevel();
       i2 = result * 59;
       i1 = (ruleLevel == null)? i: ruleLevel.hashCode();
       String region = this.getRegion();
       i2 = (i2 + i1) * 59;
       i1 = (region == null)? i: region.hashCode();
       String planId = this.getPlanId();
       i1 = (i2 + i1) * 59;
       if (planId != null) {
          i = planId.hashCode();
       }
       return (i1 + i);
    }
    public void setCreater(String creater){
       this.creater = creater;
    }
    public void setDiagnosisType(String diagnosisType){
       this.diagnosisType = diagnosisType;
    }
    public void setPlanId(String planId){
       this.planId = planId;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setRuleCategory1(String ruleCategory1){
       this.ruleCategory1 = ruleCategory1;
    }
    public void setRuleLevel(String ruleLevel){
       this.ruleLevel = ruleLevel;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setState(String state){
       this.state = state;
    }
    public String toString(){
       return "PlanRuleRespNewDto\(creater="+this.getCreater()+", diagnosisType="+this.getDiagnosisType()+", ruleName="+this.getRuleName()+", state="+this.getState()+", ruleCategory1="+this.getRuleCategory1()+", ruleLevel="+this.getRuleLevel()+", region="+this.getRegion()+", planId="+this.getPlanId()+"\)";
    }
}
