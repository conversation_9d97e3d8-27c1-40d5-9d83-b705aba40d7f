package com.taikang.fly.check.dto.drg.DrgGroupOrDepartmentInfoDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DrgGroupOrDepartmentInfoDto implements Serializable	// class@0000eb from classes.dex
{
    private String name;
    private String oraUserName;
    private String profitOrLoss;
    private static final long serialVersionUID = 0x1;

    public void DrgGroupOrDepartmentInfoDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgGroupOrDepartmentInfoDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DrgGroupOrDepartmentInfoDto) {
             b = false;
          }else {
             DrgGroupOrDepartmentInfoDto uDrgGroupOrD = o;
             if (!uDrgGroupOrD.canEqual(this)) {
                b = false;
             }else {
                String oraUserName = this.getOraUserName();
                String oraUserName1 = uDrgGroupOrD.getOraUserName();
                if (oraUserName == null) {
                   if (oraUserName1 != null) {
                      b = false;
                   }
                }else if(oraUserName.equals(oraUserName1)){
                }
                String profitOrLoss = this.getProfitOrLoss();
                String profitOrLoss1 = uDrgGroupOrD.getProfitOrLoss();
                if (profitOrLoss == null) {
                   if (profitOrLoss1 != null) {
                      b = false;
                   }
                }else if(profitOrLoss.equals(profitOrLoss1)){
                }
                String name = this.getName();
                String name1 = uDrgGroupOrD.getName();
                if (name == null) {
                   if (name1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!name.equals(name1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getName(){
       return this.name;
    }
    public String getOraUserName(){
       return this.oraUserName;
    }
    public String getProfitOrLoss(){
       return this.profitOrLoss;
    }
    public int hashCode(){
       String $oraUserName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($oraUserName = this.getOraUserName()) == null)? i: $oraUserName.hashCode();
       result = i1 + 59;
       String $profitOrLoss = this.getProfitOrLoss();
       int i2 = result * 59;
       i1 = ($profitOrLoss == null)? i: $profitOrLoss.hashCode();
       result = i2 + i1;
       String $name = this.getName();
       i1 = result * 59;
       if ($name != null) {
          i = $name.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setName(String name){
       this.name = name;
    }
    public void setOraUserName(String oraUserName){
       this.oraUserName = oraUserName;
    }
    public void setProfitOrLoss(String profitOrLoss){
       this.profitOrLoss = profitOrLoss;
    }
    public String toString(){
       return "DrgGroupOrDepartmentInfoDto\(oraUserName="+this.getOraUserName()+", profitOrLoss="+this.getProfitOrLoss()+", name="+this.getName()+"\)";
    }
}
