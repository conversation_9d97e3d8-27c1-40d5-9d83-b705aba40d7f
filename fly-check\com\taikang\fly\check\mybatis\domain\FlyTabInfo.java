package com.taikang.fly.check.mybatis.domain.FlyTabInfo;
import java.lang.Object;
import java.lang.Integer;
import java.lang.String;
import java.util.Date;
import java.lang.StringBuilder;

public class FlyTabInfo	// class@000248 from classes.dex
{
    private String createBy;
    private Date createTime;
    private Integer id;
    private String modifyBy;
    private Date modifyTime;
    private String remark;
    private String tabCode;
    private String tabDescription;
    private String tabInfoTitle;
    private String tabName;

    public void FlyTabInfo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyTabInfo;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof FlyTabInfo){
          b = false;
       }else {
          FlyTabInfo uFlyTabInfo = o;
          if (!uFlyTabInfo.canEqual(this)) {
             b = false;
          }else {
             Integer id = this.getId();
             Integer id1 = uFlyTabInfo.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String tabInfoTitle = this.getTabInfoTitle();
             String tabInfoTitle1 = uFlyTabInfo.getTabInfoTitle();
             if (tabInfoTitle == null) {
                if (tabInfoTitle1 != null) {
                   b = false;
                }
             }else if(tabInfoTitle.equals(tabInfoTitle1)){
             }
             String tabCode = this.getTabCode();
             String tabCode1 = uFlyTabInfo.getTabCode();
             if (tabCode == null) {
                if (tabCode1 != null) {
                   b = false;
                }
             }else if(tabCode.equals(tabCode1)){
             }
             String tabName = this.getTabName();
             String tabName1 = uFlyTabInfo.getTabName();
             if (tabName == null) {
                if (tabName1 != null) {
                   b = false;
                }
             }else if(tabName.equals(tabName1)){
             }
             String tabDescripti = this.getTabDescription();
             String tabDescripti1 = uFlyTabInfo.getTabDescription();
             if (tabDescripti == null) {
                if (tabDescripti1 != null) {
                   b = false;
                }
             }else if(tabDescripti.equals(tabDescripti1)){
             }
             String createBy = this.getCreateBy();
             String createBy1 = uFlyTabInfo.getCreateBy();
             if (createBy == null) {
                if (createBy1 != null) {
                label_00a1 :
                   b = false;
                }
             }else if(createBy.equals(createBy1)){
             }
             Date createTime = this.getCreateTime();
             Date createTime1 = uFlyTabInfo.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modifyBy = this.getModifyBy();
             String modifyBy1 = uFlyTabInfo.getModifyBy();
             if (modifyBy == null) {
                if (modifyBy1 != null) {
                label_00cd :
                   b = false;
                }
             }else if(modifyBy.equals(modifyBy1)){
             }
             Date modifyTime = this.getModifyTime();
             Date modifyTime1 = uFlyTabInfo.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String remark = this.getRemark();
             String remark1 = uFlyTabInfo.getRemark();
             if (remark == null) {
                if (remark1 != null) {
                label_00fd :
                   b = false;
                }
             }else if(remark.equals(remark1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getCreateBy(){
       return this.createBy;
    }
    public Date getCreateTime(){
       return this.createTime;
    }
    public Integer getId(){
       return this.id;
    }
    public String getModifyBy(){
       return this.modifyBy;
    }
    public Date getModifyTime(){
       return this.modifyTime;
    }
    public String getRemark(){
       return this.remark;
    }
    public String getTabCode(){
       return this.tabCode;
    }
    public String getTabDescription(){
       return this.tabDescription;
    }
    public String getTabInfoTitle(){
       return this.tabInfoTitle;
    }
    public String getTabName(){
       return this.tabName;
    }
    public int hashCode(){
       Integer $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $tabInfoTitle = this.getTabInfoTitle();
       int i2 = result * 59;
       i1 = ($tabInfoTitle == null)? i: $tabInfoTitle.hashCode();
       result = i2 + i1;
       String $tabCode = this.getTabCode();
       i2 = result * 59;
       i1 = ($tabCode == null)? i: $tabCode.hashCode();
       result = i2 + i1;
       String $tabName = this.getTabName();
       i2 = result * 59;
       i1 = ($tabName == null)? i: $tabName.hashCode();
       result = i2 + i1;
       String $tabDescription = this.getTabDescription();
       i2 = result * 59;
       i1 = ($tabDescription == null)? i: $tabDescription.hashCode();
       result = i2 + i1;
       String createBy = this.getCreateBy();
       i2 = result * 59;
       i1 = (createBy == null)? i: createBy.hashCode();
       Date createTime = this.getCreateTime();
       i2 = (i2 + i1) * 59;
       i1 = (createTime == null)? i: createTime.hashCode();
       String modifyBy = this.getModifyBy();
       i2 = (i2 + i1) * 59;
       i1 = (modifyBy == null)? i: modifyBy.hashCode();
       Date modifyTime = this.getModifyTime();
       i2 = (i2 + i1) * 59;
       i1 = (modifyTime == null)? i: modifyTime.hashCode();
       String remark = this.getRemark();
       i1 = (i2 + i1) * 59;
       if (remark != null) {
          i = remark.hashCode();
       }
       return (i1 + i);
    }
    public void setCreateBy(String createBy){
       this.createBy = createBy;
    }
    public void setCreateTime(Date createTime){
       this.createTime = createTime;
    }
    public void setId(Integer id){
       this.id = id;
    }
    public void setModifyBy(String modifyBy){
       this.modifyBy = modifyBy;
    }
    public void setModifyTime(Date modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setRemark(String remark){
       this.remark = remark;
    }
    public void setTabCode(String tabCode){
       this.tabCode = tabCode;
    }
    public void setTabDescription(String tabDescription){
       this.tabDescription = tabDescription;
    }
    public void setTabInfoTitle(String tabInfoTitle){
       this.tabInfoTitle = tabInfoTitle;
    }
    public void setTabName(String tabName){
       this.tabName = tabName;
    }
    public String toString(){
       return "FlyTabInfo\(id="+this.getId()+", tabInfoTitle="+this.getTabInfoTitle()+", tabCode="+this.getTabCode()+", tabName="+this.getTabName()+", tabDescription="+this.getTabDescription()+", createBy="+this.getCreateBy()+", createTime="+this.getCreateTime()+", modifyBy="+this.getModifyBy()+", modifyTime="+this.getModifyTime()+", remark="+this.getRemark()+"\)";
    }
}
