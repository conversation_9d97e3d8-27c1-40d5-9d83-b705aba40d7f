package org.springframework.boot.loader.jar.ZipInflaterInputStream;
import java.util.zip.InflaterInputStream;
import java.io.InputStream;
import java.util.zip.Inflater;

class ZipInflaterInputStream extends InflaterInputStream	// class@00055e from classes.dex
{
    private int available;
    private boolean extraBytesWritten;

    void ZipInflaterInputStream(InputStream inputStream,int size){
       super(inputStream, new Inflater(true), ZipInflaterInputStream.getInflaterBufferSize((long)size));
       this.available = size;
    }
    private static int getInflaterBufferSize(long size){
       size = size + 2;
       if ((size - 0x10000) > 0) {
          size = 8192;
       }
       if ((size) <= 0) {
          size = 4096;
       }
       return (int)size;
    }
    public int available(){
       int i = (this.available < null)? super.available(): this.available;
       return i;
    }
    public void close(){
       super.close();
       this.inf.end();
    }
    protected void fill(){
       int i = 1;
       int i1 = 0;
       try{
          super.fill();
       }catch(java.io.EOFException e0){
          if (this.extraBytesWritten != null) {
             throw e0;
          }
          this.len = i;
          this.buf[i1] = i1;
          this.extraBytesWritten = i;
          this.inf.setInput(this.buf, i1, this.len);
       }
       return;
    }
    public int read(byte[] b,int off,int len){
       int result;
       if ((result = super.read(b, off, len)) != -1) {
          this.available = this.available - result;
       }
       return result;
    }
}
