package com.taikang.fly.check.vo.drg.DrgCheckTaskDateVo;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DrgCheckTaskDateVo	// class@000360 from classes.dex
{
    private String dataEnd;
    private String dataStart;

    public void DrgCheckTaskDateVo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgCheckTaskDateVo;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DrgCheckTaskDateVo) {
             b = false;
          }else {
             DrgCheckTaskDateVo uDrgCheckTas = o;
             if (!uDrgCheckTas.canEqual(this)) {
                b = false;
             }else {
                String dataStart = this.getDataStart();
                String dataStart1 = uDrgCheckTas.getDataStart();
                if (dataStart == null) {
                   if (dataStart1 != null) {
                      b = false;
                   }
                }else if(dataStart.equals(dataStart1)){
                }
                String dataEnd = this.getDataEnd();
                String dataEnd1 = uDrgCheckTas.getDataEnd();
                if (dataEnd == null) {
                   if (dataEnd1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!dataEnd.equals(dataEnd1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getDataEnd(){
       return this.dataEnd;
    }
    public String getDataStart(){
       return this.dataStart;
    }
    public int hashCode(){
       String $dataStart;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($dataStart = this.getDataStart()) == null)? i: $dataStart.hashCode();
       result = i1 + 59;
       String $dataEnd = this.getDataEnd();
       i1 = result * 59;
       if ($dataEnd != null) {
          i = $dataEnd.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDataEnd(String dataEnd){
       this.dataEnd = dataEnd;
    }
    public void setDataStart(String dataStart){
       this.dataStart = dataStart;
    }
    public String toString(){
       return "DrgCheckTaskDateVo\(dataStart="+this.getDataStart()+", dataEnd="+this.getDataEnd()+"\)";
    }
}
