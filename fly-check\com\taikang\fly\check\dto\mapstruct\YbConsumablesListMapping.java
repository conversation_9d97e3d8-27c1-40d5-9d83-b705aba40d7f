package com.taikang.fly.check.dto.mapstruct.YbConsumablesListMapping;
import com.taikang.fly.check.mybatis.domain.YbConsumablesList;
import com.taikang.fly.check.dto.ybconsumablesList.YbConsumablesListRespDto;
import com.taikang.fly.check.mybatis.domain.Consumables;
import java.util.List;

public interface abstract YbConsumablesListMapping	// class@000183 from classes.dex
{

    YbConsumablesListRespDto entryToDto(YbConsumablesList p0);
    YbConsumablesListRespDto entryToDtoList(Consumables p0);
    List entryToDtoList(List p0);
}
