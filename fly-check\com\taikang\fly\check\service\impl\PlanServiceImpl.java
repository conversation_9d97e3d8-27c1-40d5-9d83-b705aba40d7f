package com.taikang.fly.check.service.impl.PlanServiceImpl;
import com.taikang.fly.check.service.PlanService;
import java.lang.Object;
import com.taikang.fly.check.dto.plan.PlanAddDto;
import java.lang.Integer;
import com.taikang.fly.check.mybatis.domain.Plan;
import com.taikang.fly.check.dto.mapstruct.PlanMapping;
import com.taikang.fly.check.mybatis.dao.PlanMapper;
import com.taikang.fly.check.dto.plan.PlanRuleSearchDto;
import com.taikang.fly.check.comm.NativePage;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import java.util.List;
import com.taikang.fly.check.mybatis.dao.PlanRuleMapper;
import com.taikang.fly.check.dto.mapstruct.PlanRuleMapping;
import java.lang.String;
import org.springframework.util.ObjectUtils;
import com.taikang.fly.check.comm.BizException;
import com.taikang.fly.check.comm.enums.ResponseCodeEnum;
import java.io.Serializable;
import com.taikang.fly.check.dto.plan.PlanSearchDto;

public class PlanServiceImpl implements PlanService	// class@00031e from classes.dex
{
    private PlanMapper planMapper;
    private PlanMapping planMapping;
    private PlanRuleMapper planRuleMapper;
    private PlanRuleMapping planRuleMapping;

    public void PlanServiceImpl(){
       super();
    }
    public Integer add(PlanAddDto planAddDto){
       Plan plan = this.planMapping.dtoToEntry(planAddDto);
       return Integer.valueOf(this.planMapper.insert(plan));
    }
    public NativePage configRules(PlanRuleSearchDto planRuleSearchDto,Integer pageSize,Integer pageNum){
       Page records = PageHelper.startPage(pageNum.intValue(), pageSize.intValue(), true);
       List planRuleModels = this.planRuleMapper.selectPlanRuleByPage(JSONObject.toJSON(planRuleSearchDto));
       List planRuleRespVos = this.planRuleMapping.entryToDtos(planRuleModels);
       return new NativePage(planRuleRespVos, Integer.valueOf((int)records.getTotal()), Integer.valueOf(records.getPageSize()), Integer.valueOf(records.getPageNum()), records.getStartRow());
    }
    public Integer deleteById(String id){
       if (ObjectUtils.isEmpty(id)) {
          throw new BizException(ResponseCodeEnum.PARAMETER_ISNULL_ERROR);
       }
       Plan plan = this.planMapper.selectById(id);
       if (ObjectUtils.isEmpty(plan)) {
          throw new BizException(ResponseCodeEnum.DATA_NOT_EXISTS);
       }
       this.planRuleMapper.updatePlanRuleStatus(id, "0");
       plan.setStatus("0");
       return Integer.valueOf(this.planMapper.updateByPrimaryKey(plan));
    }
    public NativePage queryPlanListPage(PlanSearchDto planSearchDto,Integer pageNum,Integer pageSize){
       Page records = PageHelper.startPage(pageNum.intValue(), pageSize.intValue(), true);
       List planList = this.planMapper.findPlanList(JSONObject.toJSON(planSearchDto));
       return new NativePage(planList, Integer.valueOf((int)records.getTotal()), Integer.valueOf(records.getPageSize()), Integer.valueOf(records.getPageNum()), records.getStartRow());
    }
}
