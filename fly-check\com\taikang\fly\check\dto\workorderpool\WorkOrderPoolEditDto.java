package com.taikang.fly.check.dto.workorderpool.WorkOrderPoolEditDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class WorkOrderPoolEditDto implements Serializable	// class@0001ca from classes.dex
{
    private String createdTime;
    private String creater;
    private String demandSource;
    private String diagnosisType;
    private String id;
    private String isSpecial;
    private String operateTime;
    private String operator;
    private String policyBasis;
    private String redField1;
    private String redField2;
    private String redField3;
    private String region;
    private String removed;
    private String ruleCategory1;
    private String ruleCategory2;
    private String ruleDescribe;
    private String ruleDimension;
    private String ruleLevel;
    private String ruleName;
    private String ruleParameter;
    private String ruleState;
    private String ruleSuitTimeEnd;
    private String ruleSuitTimeStart;
    private String sqlName;
    private static final long serialVersionUID = 0x1;

    public void WorkOrderPoolEditDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof WorkOrderPoolEditDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof WorkOrderPoolEditDto){
          b = false;
       }else {
          WorkOrderPoolEditDto workOrderPoo = o;
          if (!workOrderPoo.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = workOrderPoo.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String ruleName = this.getRuleName();
             String ruleName1 = workOrderPoo.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String operator = this.getOperator();
             String operator1 = workOrderPoo.getOperator();
             if (operator == null) {
                if (operator1 != null) {
                   b = false;
                }
             }else if(operator.equals(operator1)){
             }
             String region = this.getRegion();
             String region1 = workOrderPoo.getRegion();
             if (region == null) {
                if (region1 != null) {
                   b = false;
                }
             }else if(region.equals(region1)){
             }
             String removed = this.getRemoved();
             String removed1 = workOrderPoo.getRemoved();
             if (removed == null) {
                if (removed1 != null) {
                   b = false;
                }
             }else if(removed.equals(removed1)){
             }
             String operateTime = this.getOperateTime();
             String operateTime1 = workOrderPoo.getOperateTime();
             if (operateTime == null) {
                if (operateTime1 != null) {
                   b = false;
                }
             }else if(operateTime.equals(operateTime1)){
             }
             String createdTime = this.getCreatedTime();
             String createdTime1 = workOrderPoo.getCreatedTime();
             if (createdTime == null) {
                if (createdTime1 != null) {
                label_00bf :
                   b = false;
                }
             }else if(createdTime.equals(createdTime1)){
             }
             String ruleState = this.getRuleState();
             String ruleState1 = workOrderPoo.getRuleState();
             if (ruleState == null) {
                if (ruleState1 != null) {
                   b = false;
                }
             }else if(ruleState.equals(ruleState1)){
             }
             String creater = this.getCreater();
             String creater1 = workOrderPoo.getCreater();
             if (creater == null) {
                if (creater1 != null) {
                label_00f1 :
                   b = false;
                }
             }else if(creater.equals(creater1)){
             }
             String ruleLevel = this.getRuleLevel();
             String ruleLevel1 = workOrderPoo.getRuleLevel();
             if (ruleLevel == null) {
                if (ruleLevel1 != null) {
                   b = false;
                }
             }else if(ruleLevel.equals(ruleLevel1)){
             }
             String ruleCategory = this.getRuleCategory1();
             String ruleCategory1 = workOrderPoo.getRuleCategory1();
             if (ruleCategory == null) {
                if (ruleCategory1 != null) {
                label_0123 :
                   b = false;
                }
             }else if(ruleCategory.equals(ruleCategory1)){
             }
             String ruleCategory2 = this.getRuleCategory2();
             String ruleCategory3 = workOrderPoo.getRuleCategory2();
             if (ruleCategory2 == null) {
                if (ruleCategory3 != null) {
                   b = false;
                }
             }else if(ruleCategory2.equals(ruleCategory3)){
             }
             String diagnosisTyp = this.getDiagnosisType();
             String diagnosisTyp1 = workOrderPoo.getDiagnosisType();
             if (diagnosisTyp == null) {
                if (diagnosisTyp1 != null) {
                label_0157 :
                   b = false;
                }
             }else if(diagnosisTyp.equals(diagnosisTyp1)){
             }
             String ruleDescribe = this.getRuleDescribe();
             String ruleDescribe1 = workOrderPoo.getRuleDescribe();
             if (ruleDescribe == null) {
                if (ruleDescribe1 != null) {
                   b = false;
                }
             }else if(ruleDescribe.equals(ruleDescribe1)){
             }
             String ruleParamete = this.getRuleParameter();
             String ruleParamete1 = workOrderPoo.getRuleParameter();
             if (ruleParamete == null) {
                if (ruleParamete1 != null) {
                label_0189 :
                   b = false;
                }
             }else if(ruleParamete.equals(ruleParamete1)){
             }
             String redField1 = this.getRedField1();
             String redField11 = workOrderPoo.getRedField1();
             if (redField1 == null) {
                if (redField11 != null) {
                   b = false;
                }
             }else if(redField1.equals(redField11)){
             }
             String redField2 = this.getRedField2();
             String redField21 = workOrderPoo.getRedField2();
             if (redField2 == null) {
                if (redField21 != null) {
                   b = false;
                }
             }else if(redField2.equals(redField21)){
             }
             String redField3 = this.getRedField3();
             String redField31 = workOrderPoo.getRedField3();
             if (redField3 == null) {
                if (redField31 != null) {
                label_01d3 :
                   b = false;
                }
             }else if(redField3.equals(redField31)){
             }
             String sqlName = this.getSqlName();
             String sqlName1 = workOrderPoo.getSqlName();
             if (sqlName == null) {
                if (sqlName1 != null) {
                   b = false;
                }
             }else if(sqlName.equals(sqlName1)){
             }
             String demandSource = this.getDemandSource();
             String demandSource1 = workOrderPoo.getDemandSource();
             if (demandSource == null) {
                if (demandSource1 != null) {
                label_0205 :
                   b = false;
                }
             }else if(demandSource.equals(demandSource1)){
             }
             String policyBasis = this.getPolicyBasis();
             String policyBasis1 = workOrderPoo.getPolicyBasis();
             if (policyBasis == null) {
                if (policyBasis1 != null) {
                   b = false;
                }
             }else if(policyBasis.equals(policyBasis1)){
             }
             String isSpecial = this.getIsSpecial();
             String isSpecial1 = workOrderPoo.getIsSpecial();
             if (isSpecial == null) {
                if (isSpecial1 != null) {
                label_0235 :
                   b = false;
                }
             }else if(isSpecial.equals(isSpecial1)){
             }
             String ruleSuitTime = this.getRuleSuitTimeStart();
             String ruleSuitTime1 = workOrderPoo.getRuleSuitTimeStart();
             if (ruleSuitTime == null) {
                if (ruleSuitTime1 != null) {
                   b = false;
                }
             }else if(ruleSuitTime.equals(ruleSuitTime1)){
             }
             String ruleSuitTime2 = this.getRuleSuitTimeEnd();
             String ruleSuitTime3 = workOrderPoo.getRuleSuitTimeEnd();
             if (ruleSuitTime2 == null) {
                if (ruleSuitTime3 != null) {
                label_0267 :
                   b = false;
                }
             }else if(ruleSuitTime2.equals(ruleSuitTime3)){
             }
             String ruleDimensio = this.getRuleDimension();
             String ruleDimensio1 = workOrderPoo.getRuleDimension();
             if (ruleDimensio == null) {
                if (ruleDimensio1 != null) {
                   b = false;
                }
             }else if(ruleDimensio.equals(ruleDimensio1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getCreatedTime(){
       return this.createdTime;
    }
    public String getCreater(){
       return this.creater;
    }
    public String getDemandSource(){
       return this.demandSource;
    }
    public String getDiagnosisType(){
       return this.diagnosisType;
    }
    public String getId(){
       return this.id;
    }
    public String getIsSpecial(){
       return this.isSpecial;
    }
    public String getOperateTime(){
       return this.operateTime;
    }
    public String getOperator(){
       return this.operator;
    }
    public String getPolicyBasis(){
       return this.policyBasis;
    }
    public String getRedField1(){
       return this.redField1;
    }
    public String getRedField2(){
       return this.redField2;
    }
    public String getRedField3(){
       return this.redField3;
    }
    public String getRegion(){
       return this.region;
    }
    public String getRemoved(){
       return this.removed;
    }
    public String getRuleCategory1(){
       return this.ruleCategory1;
    }
    public String getRuleCategory2(){
       return this.ruleCategory2;
    }
    public String getRuleDescribe(){
       return this.ruleDescribe;
    }
    public String getRuleDimension(){
       return this.ruleDimension;
    }
    public String getRuleLevel(){
       return this.ruleLevel;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleParameter(){
       return this.ruleParameter;
    }
    public String getRuleState(){
       return this.ruleState;
    }
    public String getRuleSuitTimeEnd(){
       return this.ruleSuitTimeEnd;
    }
    public String getRuleSuitTimeStart(){
       return this.ruleSuitTimeStart;
    }
    public String getSqlName(){
       return this.sqlName;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $ruleName = this.getRuleName();
       int i1 = result * 59;
       i = ($ruleName == null)? 43: $ruleName.hashCode();
       result = i1 + i;
       String $operator = this.getOperator();
       i1 = result * 59;
       i = ($operator == null)? 43: $operator.hashCode();
       result = i1 + i;
       String $region = this.getRegion();
       i1 = result * 59;
       i = ($region == null)? 43: $region.hashCode();
       result = i1 + i;
       String $removed = this.getRemoved();
       i1 = result * 59;
       i = ($removed == null)? 43: $removed.hashCode();
       result = i1 + i;
       String operateTime = this.getOperateTime();
       i1 = result * 59;
       i = (operateTime == null)? 43: operateTime.hashCode();
       String createdTime = this.getCreatedTime();
       i1 = (i1 + i) * 59;
       i = (createdTime == null)? 43: createdTime.hashCode();
       String ruleState = this.getRuleState();
       i1 = (i1 + i) * 59;
       i = (ruleState == null)? 43: ruleState.hashCode();
       String creater = this.getCreater();
       i1 = (i1 + i) * 59;
       i = (creater == null)? 43: creater.hashCode();
       String ruleLevel = this.getRuleLevel();
       i1 = (i1 + i) * 59;
       i = (ruleLevel == null)? 43: ruleLevel.hashCode();
       String ruleCategory = this.getRuleCategory1();
       i1 = (i1 + i) * 59;
       i = (ruleCategory == null)? 43: ruleCategory.hashCode();
       String ruleCategory1 = this.getRuleCategory2();
       i1 = (i1 + i) * 59;
       i = (ruleCategory1 == null)? 43: ruleCategory1.hashCode();
       String diagnosisTyp = this.getDiagnosisType();
       i1 = (i1 + i) * 59;
       i = (diagnosisTyp == null)? 43: diagnosisTyp.hashCode();
       String ruleDescribe = this.getRuleDescribe();
       i1 = (i1 + i) * 59;
       i = (ruleDescribe == null)? 43: ruleDescribe.hashCode();
       String ruleParamete = this.getRuleParameter();
       i1 = (i1 + i) * 59;
       i = (ruleParamete == null)? 43: ruleParamete.hashCode();
       String redField1 = this.getRedField1();
       i1 = (i1 + i) * 59;
       i = (redField1 == null)? 43: redField1.hashCode();
       String redField2 = this.getRedField2();
       i1 = (i1 + i) * 59;
       i = (redField2 == null)? 43: redField2.hashCode();
       String redField3 = this.getRedField3();
       i1 = (i1 + i) * 59;
       i = (redField3 == null)? 43: redField3.hashCode();
       String sqlName = this.getSqlName();
       i1 = (i1 + i) * 59;
       i = (sqlName == null)? 43: sqlName.hashCode();
       String demandSource = this.getDemandSource();
       i1 = (i1 + i) * 59;
       i = (demandSource == null)? 43: demandSource.hashCode();
       String policyBasis = this.getPolicyBasis();
       i1 = (i1 + i) * 59;
       i = (policyBasis == null)? 43: policyBasis.hashCode();
       String isSpecial = this.getIsSpecial();
       i1 = (i1 + i) * 59;
       i = (isSpecial == null)? 43: isSpecial.hashCode();
       String ruleSuitTime = this.getRuleSuitTimeStart();
       i1 = (i1 + i) * 59;
       i = (ruleSuitTime == null)? 43: ruleSuitTime.hashCode();
       String ruleSuitTime1 = this.getRuleSuitTimeEnd();
       i1 = (i1 + i) * 59;
       i = (ruleSuitTime1 == null)? 43: ruleSuitTime1.hashCode();
       String ruleDimensio = this.getRuleDimension();
       i1 = (i1 + i) * 59;
       i = (ruleDimensio == null)? 43: ruleDimensio.hashCode();
       return (i1 + i);
    }
    public void setCreatedTime(String createdTime){
       this.createdTime = createdTime;
    }
    public void setCreater(String creater){
       this.creater = creater;
    }
    public void setDemandSource(String demandSource){
       this.demandSource = demandSource;
    }
    public void setDiagnosisType(String diagnosisType){
       this.diagnosisType = diagnosisType;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setIsSpecial(String isSpecial){
       this.isSpecial = isSpecial;
    }
    public void setOperateTime(String operateTime){
       this.operateTime = operateTime;
    }
    public void setOperator(String operator){
       this.operator = operator;
    }
    public void setPolicyBasis(String policyBasis){
       this.policyBasis = policyBasis;
    }
    public void setRedField1(String redField1){
       this.redField1 = redField1;
    }
    public void setRedField2(String redField2){
       this.redField2 = redField2;
    }
    public void setRedField3(String redField3){
       this.redField3 = redField3;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setRemoved(String removed){
       this.removed = removed;
    }
    public void setRuleCategory1(String ruleCategory1){
       this.ruleCategory1 = ruleCategory1;
    }
    public void setRuleCategory2(String ruleCategory2){
       this.ruleCategory2 = ruleCategory2;
    }
    public void setRuleDescribe(String ruleDescribe){
       this.ruleDescribe = ruleDescribe;
    }
    public void setRuleDimension(String ruleDimension){
       this.ruleDimension = ruleDimension;
    }
    public void setRuleLevel(String ruleLevel){
       this.ruleLevel = ruleLevel;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleParameter(String ruleParameter){
       this.ruleParameter = ruleParameter;
    }
    public void setRuleState(String ruleState){
       this.ruleState = ruleState;
    }
    public void setRuleSuitTimeEnd(String ruleSuitTimeEnd){
       this.ruleSuitTimeEnd = ruleSuitTimeEnd;
    }
    public void setRuleSuitTimeStart(String ruleSuitTimeStart){
       this.ruleSuitTimeStart = ruleSuitTimeStart;
    }
    public void setSqlName(String sqlName){
       this.sqlName = sqlName;
    }
    public String toString(){
       return "WorkOrderPoolEditDto\(id="+this.getId()+", ruleName="+this.getRuleName()+", operator="+this.getOperator()+", region="+this.getRegion()+", removed="+this.getRemoved()+", operateTime="+this.getOperateTime()+", createdTime="+this.getCreatedTime()+", ruleState="+this.getRuleState()+", creater="+this.getCreater()+", ruleLevel="+this.getRuleLevel()+", ruleCategory1="+this.getRuleCategory1()+", ruleCategory2="+this.getRuleCategory2()+", diagnosisType="+this.getDiagnosisType()+", ruleDescribe="+this.getRuleDescribe()+", ruleParameter="+this.getRuleParameter()+", redField1="+this.getRedField1()+", redField2="+this.getRedField2()+", redField3="+this.getRedField3()+", sqlName="+this.getSqlName()+", demandSource="+this.getDemandSource()+", policyBasis="+this.getPolicyBasis()+", isSpecial="+this.getIsSpecial()+", ruleSuitTimeStart="+this.getRuleSuitTimeStart()+", ruleSuitTimeEnd="+this.getRuleSuitTimeEnd()+", ruleDimension="+this.getRuleDimension()+"\)";
    }
}
