package com.taikang.fly.check.mybatis.dao.OracleTabSpaceExMapper;
import java.lang.String;
import java.util.List;
import java.lang.Boolean;
import java.lang.Integer;

public interface abstract OracleTabSpaceExMapper	// class@000214 from classes.dex
{

    List getFileName(String p0);
    List getFileName1();
    List getTableSpaceName();
    String getTableSpaces(String p0);
    List getTableSpacesList(String p0);
    List getTableSpacesReductionList(String p0);
    List getTempFileName(String p0);
    List getUserName();
    Boolean insertTabSpace(String p0,String p1);
    Integer updateTabSpaceEx(String p0,String p1,String p2,String p3);
    Integer updateTabSpaceReduction(String p0,String p1);
    Integer updateUserSpace(String p0,String p1);
}
