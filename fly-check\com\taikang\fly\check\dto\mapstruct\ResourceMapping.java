package com.taikang.fly.check.dto.mapstruct.ResourceMapping;
import com.taikang.fly.check.dto.resource.ResourceAddDto;
import com.taikang.fly.check.mybatis.domain.Resource;
import com.taikang.fly.check.dto.resource.ResourceEditDto;
import java.lang.String;
import com.taikang.fly.check.dto.resource.ResourceIndexDto;
import com.taikang.fly.check.dto.resource.ResourceResDto;
import java.util.List;
import com.taikang.fly.check.dto.resource.ResourceIndexRDto;

public interface abstract ResourceMapping	// class@000177 from classes.dex
{

    Resource addDto2Entity(ResourceAddDto p0);
    Resource editDto2Entity(ResourceEditDto p0,Resource p1);
    ResourceIndexDto toIndexDto(Resource p0,int p1,String p2);
    ResourceResDto toResDto(Resource p0);
    List toResDtoList(List p0);
    ResourceIndexRDto toResourceIndexRDto(Resource p0,int p1,String p2);
}
