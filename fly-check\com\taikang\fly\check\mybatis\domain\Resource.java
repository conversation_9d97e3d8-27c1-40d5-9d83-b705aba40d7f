package com.taikang.fly.check.mybatis.domain.Resource;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import java.util.Date;
import java.lang.StringBuilder;

public class Resource implements Serializable	// class@000267 from classes.dex
{
    private String aclass;
    private Date createTime;
    private String creator;
    private String description;
    private String iclass;
    private String icon;
    private String isLeaf;
    private String modby;
    private Date modifyTime;
    private String parentId;
    private String resourceId;
    private String resourceName;
    private Integer resourceOrder;
    private String resourceType;
    private String signature;
    private String url;
    private static final long serialVersionUID = 0x1;

    public void Resource(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof Resource;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof Resource){
          b = false;
       }else {
          Resource resource = o;
          if (!resource.canEqual(this)) {
             b = false;
          }else {
             String resourceId = this.getResourceId();
             String resourceId1 = resource.getResourceId();
             if (resourceId == null) {
                if (resourceId1 != null) {
                   b = false;
                }
             }else if(resourceId.equals(resourceId1)){
             }
             String parentId = this.getParentId();
             String parentId1 = resource.getParentId();
             if (parentId == null) {
                if (parentId1 != null) {
                   b = false;
                }
             }else if(parentId.equals(parentId1)){
             }
             String resourceName = this.getResourceName();
             String resourceName1 = resource.getResourceName();
             if (resourceName == null) {
                if (resourceName1 != null) {
                   b = false;
                }
             }else if(resourceName.equals(resourceName1)){
             }
             String url = this.getUrl();
             String url1 = resource.getUrl();
             if (url == null) {
                if (url1 != null) {
                   b = false;
                }
             }else if(url.equals(url1)){
             }
             Integer resourceOrde = this.getResourceOrder();
             Integer resourceOrde1 = resource.getResourceOrder();
             if (resourceOrde == null) {
                if (resourceOrde1 != null) {
                   b = false;
                }
             }else if(resourceOrde.equals(resourceOrde1)){
             }
             String icon = this.getIcon();
             String icon1 = resource.getIcon();
             if (icon == null) {
                if (icon1 != null) {
                label_00a5 :
                   b = false;
                }
             }else if(icon.equals(icon1)){
             }
             String creator = this.getCreator();
             String creator1 = resource.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             Date createTime = this.getCreateTime();
             Date createTime1 = resource.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                label_00d5 :
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modby = this.getModby();
             String modby1 = resource.getModby();
             if (modby == null) {
                if (modby1 != null) {
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             Date modifyTime = this.getModifyTime();
             Date modifyTime1 = resource.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                label_0105 :
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String signature = this.getSignature();
             String signature1 = resource.getSignature();
             if (signature == null) {
                if (signature1 != null) {
                   b = false;
                }
             }else if(signature.equals(signature1)){
             }
             String description = this.getDescription();
             String description1 = resource.getDescription();
             if (description == null) {
                if (description1 != null) {
                label_0137 :
                   b = false;
                }
             }else if(description.equals(description1)){
             }
             String iclass = this.getIclass();
             String iclass1 = resource.getIclass();
             if (iclass == null) {
                if (iclass1 != null) {
                   b = false;
                }
             }else if(iclass.equals(iclass1)){
             }
             String resourceType = this.getResourceType();
             String resourceType1 = resource.getResourceType();
             if (resourceType == null) {
                if (resourceType1 != null) {
                label_0167 :
                   b = false;
                }
             }else if(resourceType.equals(resourceType1)){
             }
             String isLeaf = this.getIsLeaf();
             String isLeaf1 = resource.getIsLeaf();
             if (isLeaf == null) {
                if (isLeaf1 != null) {
                   b = false;
                }
             }else if(isLeaf.equals(isLeaf1)){
             }
             String aclass = this.getAclass();
             String aclass1 = resource.getAclass();
             if (aclass == null) {
                if (aclass1 != null) {
                label_0199 :
                   b = false;
                }
             }else if(aclass.equals(aclass1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getAclass(){
       return this.aclass;
    }
    public Date getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getDescription(){
       return this.description;
    }
    public String getIclass(){
       return this.iclass;
    }
    public String getIcon(){
       return this.icon;
    }
    public String getIsLeaf(){
       return this.isLeaf;
    }
    public String getModby(){
       return this.modby;
    }
    public Date getModifyTime(){
       return this.modifyTime;
    }
    public String getParentId(){
       return this.parentId;
    }
    public String getResourceId(){
       return this.resourceId;
    }
    public String getResourceName(){
       return this.resourceName;
    }
    public Integer getResourceOrder(){
       return this.resourceOrder;
    }
    public String getResourceType(){
       return this.resourceType;
    }
    public String getSignature(){
       return this.signature;
    }
    public String getUrl(){
       return this.url;
    }
    public int hashCode(){
       String $resourceId;
       int PRIME = 59;
       int result = 1;
       int i = (($resourceId = this.getResourceId()) == null)? 43: $resourceId.hashCode();
       result = i + 59;
       String $parentId = this.getParentId();
       int i1 = result * 59;
       i = ($parentId == null)? 43: $parentId.hashCode();
       result = i1 + i;
       String $resourceName = this.getResourceName();
       i1 = result * 59;
       i = ($resourceName == null)? 43: $resourceName.hashCode();
       result = i1 + i;
       String $url = this.getUrl();
       i1 = result * 59;
       i = ($url == null)? 43: $url.hashCode();
       result = i1 + i;
       Integer $resourceOrder = this.getResourceOrder();
       i1 = result * 59;
       i = ($resourceOrder == null)? 43: $resourceOrder.hashCode();
       result = i1 + i;
       String icon = this.getIcon();
       i1 = result * 59;
       i = (icon == null)? 43: icon.hashCode();
       String creator = this.getCreator();
       i1 = (i1 + i) * 59;
       i = (creator == null)? 43: creator.hashCode();
       Date createTime = this.getCreateTime();
       i1 = (i1 + i) * 59;
       i = (createTime == null)? 43: createTime.hashCode();
       String modby = this.getModby();
       i1 = (i1 + i) * 59;
       i = (modby == null)? 43: modby.hashCode();
       Date modifyTime = this.getModifyTime();
       i1 = (i1 + i) * 59;
       i = (modifyTime == null)? 43: modifyTime.hashCode();
       String signature = this.getSignature();
       i1 = (i1 + i) * 59;
       i = (signature == null)? 43: signature.hashCode();
       String description = this.getDescription();
       i1 = (i1 + i) * 59;
       i = (description == null)? 43: description.hashCode();
       String iclass = this.getIclass();
       i1 = (i1 + i) * 59;
       i = (iclass == null)? 43: iclass.hashCode();
       String resourceType = this.getResourceType();
       i1 = (i1 + i) * 59;
       i = (resourceType == null)? 43: resourceType.hashCode();
       String isLeaf = this.getIsLeaf();
       i1 = (i1 + i) * 59;
       i = (isLeaf == null)? 43: isLeaf.hashCode();
       String aclass = this.getAclass();
       i1 = (i1 + i) * 59;
       i = (aclass == null)? 43: aclass.hashCode();
       return (i1 + i);
    }
    public Resource setAclass(String aclass){
       this.aclass = aclass;
       return this;
    }
    public Resource setCreateTime(Date createTime){
       this.createTime = createTime;
       return this;
    }
    public Resource setCreator(String creator){
       this.creator = creator;
       return this;
    }
    public Resource setDescription(String description){
       this.description = description;
       return this;
    }
    public Resource setIclass(String iclass){
       this.iclass = iclass;
       return this;
    }
    public Resource setIcon(String icon){
       this.icon = icon;
       return this;
    }
    public Resource setIsLeaf(String isLeaf){
       this.isLeaf = isLeaf;
       return this;
    }
    public Resource setModby(String modby){
       this.modby = modby;
       return this;
    }
    public Resource setModifyTime(Date modifyTime){
       this.modifyTime = modifyTime;
       return this;
    }
    public Resource setParentId(String parentId){
       this.parentId = parentId;
       return this;
    }
    public Resource setResourceId(String resourceId){
       this.resourceId = resourceId;
       return this;
    }
    public Resource setResourceName(String resourceName){
       this.resourceName = resourceName;
       return this;
    }
    public Resource setResourceOrder(Integer resourceOrder){
       this.resourceOrder = resourceOrder;
       return this;
    }
    public Resource setResourceType(String resourceType){
       this.resourceType = resourceType;
       return this;
    }
    public Resource setSignature(String signature){
       this.signature = signature;
       return this;
    }
    public Resource setUrl(String url){
       this.url = url;
       return this;
    }
    public String toString(){
       return "Resource\(resourceId="+this.getResourceId()+", parentId="+this.getParentId()+", resourceName="+this.getResourceName()+", url="+this.getUrl()+", resourceOrder="+this.getResourceOrder()+", icon="+this.getIcon()+", creator="+this.getCreator()+", createTime="+this.getCreateTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+", signature="+this.getSignature()+", description="+this.getDescription()+", iclass="+this.getIclass()+", resourceType="+this.getResourceType()+", isLeaf="+this.getIsLeaf()+", aclass="+this.getAclass()+"\)";
    }
}
