package com.taikang.fly.check.mybatis.dao.FlyRuleMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.Map;
import java.util.List;
import java.lang.String;
import java.lang.Integer;
import com.taikang.fly.check.mybatis.domain.FlyRule;
import java.lang.Object;
import com.taikang.fly.check.dto.exportReport.DataExportEntry;
import com.taikang.fly.check.dto.exportReport.ZDMXEntry;

public interface abstract FlyRuleMapper implements BaseMapper	// class@0001f3 from classes.dex
{

    List commFlyRuleList(Map p0);
    int deleteByPrimaryKey(String p0);
    Integer dropTable(String p0);
    Integer dropTables(String p0,String p1);
    List enableExportRegion(String p0);
    List findFlyRule(Map p0);
    List findFlyRuleExecute(Map p0);
    List findFlyRuleFeedBackPage(Map p0);
    List findFlyRulePage(Map p0);
    List findFlyRulePlanPage(Map p0);
    List findformulateFlyRulePage(Map p0);
    List findformulateTemplateFlyRulePage(Map p0);
    List getHasTables();
    List getIdsByCreater(String p0,String p1);
    List getIdsByRegion(String p0);
    List getRuleHosCount(String p0);
    List getSpecialType();
    int insert(FlyRule p0);
    default int insert(Object p0){
       return this.insert(p0);
    }
    Integer insertFlyRuleHisToFleRule();
    List queryAllId();
    List queryExportFlyRule(Map p0);
    List queryFlyRuleByRegion(Map p0);
    Integer queryRepeatData(String p0,String p1);
    String querySourceOfRuleById(String p0);
    List selectAll();
    FlyRule selectByPrimaryKey(String p0);
    List selectByRegionAndByRuleName(String p0,String p1);
    List selectByRegionAndByRuleNameList(String p0,String p1);
    List selectByRegionAndRuleType(Map p0);
    List selectC(String p0);
    List selectC1(String p0);
    List selectC2(String p0);
    List selectC3(String p0);
    String selectCountByName(String p0,String p1);
    DataExportEntry selectDataExportCount(String p0);
    ZDMXEntry selectExportCount(String p0);
    List selectIdListByRegion(String p0);
    List selectInsurance(String p0);
    List selectListByRegion(String p0);
    List selectListRegion();
    List selectPriceNoInfo(String p0);
    List selectPriceNoInfoM(String p0);
    Integer selectRuleCount(String p0);
    List selectRuleName();
    List selectSetlDateColumnType(String p0);
    Integer selectTotalTable(String p0,String p1);
    List statisFlyRule(Map p0);
    int updateByPrimaryKey(FlyRule p0);
    int updateFlyRuleById(FlyRule p0);
    void updateFlyRuleFeedbackStatusById(String p0);
    void updateFlyRuleResultsEnforcementById(String p0);
    void updateFlyRuleStateById(FlyRule p0);
    void updateResultById(String p0);
    int updateResultFlagById(String p0);
}
