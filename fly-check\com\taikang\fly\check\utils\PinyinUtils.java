package com.taikang.fly.check.utils.PinyinUtils;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuffer;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import java.lang.StringBuilder;
import java.lang.Character;

public class PinyinUtils	// class@000345 from classes.dex
{

    public void PinyinUtils(){
       super();
    }
    public static String getFirstSpell(String chinese){
       String[] temp;
       StringBuffer pybf = "";
       char[] arr = chinese.toCharArray();
       HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat();
       defaultFormat.setCaseType(HanyuPinyinCaseType.LOWERCASE);
       defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
       int i = 0;
       while (i < arr.length) {
          try{
             if (arr[i] > 128) {
                if ((temp = PinyinHelper.toHanyuPinyinStringArray(arr[i], defaultFormat)) != null) {
                   pybf = pybf.append(temp[0].charAt(0));
                }
             }else {
                pybf = pybf.append(arr[i]);
             }
          }catch(net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination e2){
             e2.printStackTrace();
          }
          i++;
       }
       return pybf.replaceAll("\\W", "").trim();
    }
    public static String getFullSpell(String chinese){
       StringBuffer pybf = "";
       char[] arr = chinese.toCharArray();
       HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat();
       defaultFormat.setCaseType(HanyuPinyinCaseType.LOWERCASE);
       defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
       int i = 0;
       while (i < arr.length) {
          try{
             pybf = (arr[i] > 128)? pybf.append(PinyinHelper.toHanyuPinyinStringArray(arr[i], defaultFormat)[0]): pybf.append(arr[i]);
          }catch(net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination e2){
             e2.printStackTrace();
          }
          i++;
       }
       return pybf;
    }
    public static String getPingYin(String inputString){
       HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
       format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
       format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
       format.setVCharType(HanyuPinyinVCharType.WITH_V);
       char[] input = inputString.trim().toCharArray();
       StringBuilder output = "";
       int i = 0;
       try{
          while (i < input.length) {
             if (Character.toString(input[i]).matches("[\\u4E00-\\u9FA5]+")) {
                String[] temp = PinyinHelper.toHanyuPinyinStringArray(input[i], format);
                output = output.append(temp[0]);
             }else {
                output = output.append(Character.toString(input[i]));
             }
             i++;
          }
       }catch(net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination e0){
          e0.printStackTrace();
       }
       return String.valueOf(output);
    }
}
