package com.taikang.fly.check.dto.menu.MenuEditDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class MenuEditDto implements Serializable	// class@00018f from classes.dex
{
    private String iclass;
    private String isMenu;
    private String menuDesc;
    private String menuId;
    private String menuName;
    private String menuOrder;
    private String url;
    private static final long serialVersionUID = 0x21e31acb44c4c6a7;

    public void MenuEditDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MenuEditDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof MenuEditDto){
          b = false;
       }else {
          MenuEditDto menuEditDto = o;
          if (!menuEditDto.canEqual(this)) {
             b = false;
          }else {
             String menuId = this.getMenuId();
             String menuId1 = menuEditDto.getMenuId();
             if (menuId == null) {
                if (menuId1 != null) {
                   b = false;
                }
             }else if(menuId.equals(menuId1)){
             }
             String menuName = this.getMenuName();
             String menuName1 = menuEditDto.getMenuName();
             if (menuName == null) {
                if (menuName1 != null) {
                   b = false;
                }
             }else if(menuName.equals(menuName1)){
             }
             String menuDesc = this.getMenuDesc();
             String menuDesc1 = menuEditDto.getMenuDesc();
             if (menuDesc == null) {
                if (menuDesc1 != null) {
                   b = false;
                }
             }else if(menuDesc.equals(menuDesc1)){
             }
             String url = this.getUrl();
             String url1 = menuEditDto.getUrl();
             if (url == null) {
                if (url1 != null) {
                   b = false;
                }
             }else if(url.equals(url1)){
             }
             String iclass = this.getIclass();
             String iclass1 = menuEditDto.getIclass();
             if (iclass == null) {
                if (iclass1 != null) {
                   b = false;
                }
             }else if(iclass.equals(iclass1)){
             }
             String menuOrder = this.getMenuOrder();
             String menuOrder1 = menuEditDto.getMenuOrder();
             if (menuOrder == null) {
                if (menuOrder1 != null) {
                label_009a :
                   b = false;
                }
             }else if(menuOrder.equals(menuOrder1)){
             }
             String isMenu = this.getIsMenu();
             String isMenu1 = menuEditDto.getIsMenu();
             if (isMenu == null) {
                if (isMenu1 != null) {
                   b = false;
                }
             }else if(isMenu.equals(isMenu1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getIclass(){
       return this.iclass;
    }
    public String getIsMenu(){
       return this.isMenu;
    }
    public String getMenuDesc(){
       return this.menuDesc;
    }
    public String getMenuId(){
       return this.menuId;
    }
    public String getMenuName(){
       return this.menuName;
    }
    public String getMenuOrder(){
       return this.menuOrder;
    }
    public String getUrl(){
       return this.url;
    }
    public int hashCode(){
       String $menuId;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($menuId = this.getMenuId()) == null)? i: $menuId.hashCode();
       result = i1 + 59;
       String $menuName = this.getMenuName();
       int i2 = result * 59;
       i1 = ($menuName == null)? i: $menuName.hashCode();
       result = i2 + i1;
       String $menuDesc = this.getMenuDesc();
       i2 = result * 59;
       i1 = ($menuDesc == null)? i: $menuDesc.hashCode();
       result = i2 + i1;
       String $url = this.getUrl();
       i2 = result * 59;
       i1 = ($url == null)? i: $url.hashCode();
       result = i2 + i1;
       String $iclass = this.getIclass();
       i2 = result * 59;
       i1 = ($iclass == null)? i: $iclass.hashCode();
       result = i2 + i1;
       String menuOrder = this.getMenuOrder();
       i2 = result * 59;
       i1 = (menuOrder == null)? i: menuOrder.hashCode();
       String isMenu = this.getIsMenu();
       i1 = (i2 + i1) * 59;
       if (isMenu != null) {
          i = isMenu.hashCode();
       }
       return (i1 + i);
    }
    public void setIclass(String iclass){
       this.iclass = iclass;
    }
    public void setIsMenu(String isMenu){
       this.isMenu = isMenu;
    }
    public void setMenuDesc(String menuDesc){
       this.menuDesc = menuDesc;
    }
    public void setMenuId(String menuId){
       this.menuId = menuId;
    }
    public void setMenuName(String menuName){
       this.menuName = menuName;
    }
    public void setMenuOrder(String menuOrder){
       this.menuOrder = menuOrder;
    }
    public void setUrl(String url){
       this.url = url;
    }
    public String toString(){
       return "MenuEditDto\(menuId="+this.getMenuId()+", menuName="+this.getMenuName()+", menuDesc="+this.getMenuDesc()+", url="+this.getUrl()+", iclass="+this.getIclass()+", menuOrder="+this.getMenuOrder()+", isMenu="+this.getIsMenu()+"\)";
    }
}
