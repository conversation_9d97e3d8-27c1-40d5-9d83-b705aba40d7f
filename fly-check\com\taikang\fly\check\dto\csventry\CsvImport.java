package com.taikang.fly.check.dto.csventry.CsvImport;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class CsvImport	// class@0000d7 from classes.dex
{
    private String config;
    private String id;
    private String separator;

    public void CsvImport(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof CsvImport;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof CsvImport) {
             b = false;
          }else {
             CsvImport uCsvImport = o;
             if (!uCsvImport.canEqual(this)) {
                b = false;
             }else {
                String id = this.getId();
                String id1 = uCsvImport.getId();
                if (id == null) {
                   if (id1 != null) {
                      b = false;
                   }
                }else if(id.equals(id1)){
                }
                String separator = this.getSeparator();
                String separator1 = uCsvImport.getSeparator();
                if (separator == null) {
                   if (separator1 != null) {
                      b = false;
                   }
                }else if(separator.equals(separator1)){
                }
                String config = this.getConfig();
                String config1 = uCsvImport.getConfig();
                if (config == null) {
                   if (config1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!config.equals(config1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getConfig(){
       return this.config;
    }
    public String getId(){
       return this.id;
    }
    public String getSeparator(){
       return this.separator;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $separator = this.getSeparator();
       int i2 = result * 59;
       i1 = ($separator == null)? i: $separator.hashCode();
       result = i2 + i1;
       String $config = this.getConfig();
       i1 = result * 59;
       if ($config != null) {
          i = $config.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setConfig(String config){
       this.config = config;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setSeparator(String separator){
       this.separator = separator;
    }
    public String toString(){
       return "CsvImport\(id="+this.getId()+", separator="+this.getSeparator()+", config="+this.getConfig()+"\)";
    }
}
