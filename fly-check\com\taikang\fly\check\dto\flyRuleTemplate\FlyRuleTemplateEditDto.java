package com.taikang.fly.check.dto.flyRuleTemplate.FlyRuleTemplateEditDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class FlyRuleTemplateEditDto implements Serializable	// class@000118 from classes.dex
{
    private String diagnosisType;
    private String id;
    private String parameter;
    private String policyBasis;
    private String ps;
    private String ruleCategory1;
    private String ruleCategory2;
    private String ruleDescribe;
    private String ruleLevel;
    private String ruleName;
    private String sqlExample;
    private String sqlTemplate;
    private static final long serialVersionUID = 0x1;

    public void FlyRuleTemplateEditDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRuleTemplateEditDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof FlyRuleTemplateEditDto){
          b = false;
       }else {
          FlyRuleTemplateEditDto uFlyRuleTemp = o;
          if (!uFlyRuleTemp.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = uFlyRuleTemp.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String ruleName = this.getRuleName();
             String ruleName1 = uFlyRuleTemp.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String ruleLevel = this.getRuleLevel();
             String ruleLevel1 = uFlyRuleTemp.getRuleLevel();
             if (ruleLevel == null) {
                if (ruleLevel1 != null) {
                   b = false;
                }
             }else if(ruleLevel.equals(ruleLevel1)){
             }
             String ruleCategory = this.getRuleCategory1();
             String ruleCategory1 = uFlyRuleTemp.getRuleCategory1();
             if (ruleCategory == null) {
                if (ruleCategory1 != null) {
                   b = false;
                }
             }else if(ruleCategory.equals(ruleCategory1)){
             }
             String ruleCategory2 = this.getRuleCategory2();
             String ruleCategory3 = uFlyRuleTemp.getRuleCategory2();
             if (ruleCategory2 == null) {
                if (ruleCategory3 != null) {
                   b = false;
                }
             }else if(ruleCategory2.equals(ruleCategory3)){
             }
             String diagnosisTyp = this.getDiagnosisType();
             String diagnosisTyp1 = uFlyRuleTemp.getDiagnosisType();
             if (diagnosisTyp == null) {
                if (diagnosisTyp1 != null) {
                   b = false;
                }
             }else if(diagnosisTyp.equals(diagnosisTyp1)){
             }
             String ruleDescribe = this.getRuleDescribe();
             String ruleDescribe1 = uFlyRuleTemp.getRuleDescribe();
             if (ruleDescribe == null) {
                if (ruleDescribe1 != null) {
                label_00b9 :
                   b = false;
                }
             }else if(ruleDescribe.equals(ruleDescribe1)){
             }
             String ps = this.getPs();
             String ps1 = uFlyRuleTemp.getPs();
             if (ps == null) {
                if (ps1 != null) {
                   b = false;
                }
             }else if(ps.equals(ps1)){
             }
             String sqlTemplate = this.getSqlTemplate();
             String sqlTemplate1 = uFlyRuleTemp.getSqlTemplate();
             if (sqlTemplate == null) {
                if (sqlTemplate1 != null) {
                label_00e9 :
                   b = false;
                }
             }else if(sqlTemplate.equals(sqlTemplate1)){
             }
             String sqlExample = this.getSqlExample();
             String sqlExample1 = uFlyRuleTemp.getSqlExample();
             if (sqlExample == null) {
                if (sqlExample1 != null) {
                   b = false;
                }
             }else if(sqlExample.equals(sqlExample1)){
             }
             String parameter = this.getParameter();
             String parameter1 = uFlyRuleTemp.getParameter();
             if (parameter == null) {
                if (parameter1 != null) {
                label_0119 :
                   b = false;
                }
             }else if(parameter.equals(parameter1)){
             }
             String policyBasis = this.getPolicyBasis();
             String policyBasis1 = uFlyRuleTemp.getPolicyBasis();
             if (policyBasis == null) {
                if (policyBasis1 != null) {
                   b = false;
                }
             }else if(policyBasis.equals(policyBasis1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getDiagnosisType(){
       return this.diagnosisType;
    }
    public String getId(){
       return this.id;
    }
    public String getParameter(){
       return this.parameter;
    }
    public String getPolicyBasis(){
       return this.policyBasis;
    }
    public String getPs(){
       return this.ps;
    }
    public String getRuleCategory1(){
       return this.ruleCategory1;
    }
    public String getRuleCategory2(){
       return this.ruleCategory2;
    }
    public String getRuleDescribe(){
       return this.ruleDescribe;
    }
    public String getRuleLevel(){
       return this.ruleLevel;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getSqlExample(){
       return this.sqlExample;
    }
    public String getSqlTemplate(){
       return this.sqlTemplate;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $ruleName = this.getRuleName();
       int i1 = result * 59;
       i = ($ruleName == null)? 43: $ruleName.hashCode();
       result = i1 + i;
       String $ruleLevel = this.getRuleLevel();
       i1 = result * 59;
       i = ($ruleLevel == null)? 43: $ruleLevel.hashCode();
       result = i1 + i;
       String $ruleCategory1 = this.getRuleCategory1();
       i1 = result * 59;
       i = ($ruleCategory1 == null)? 43: $ruleCategory1.hashCode();
       result = i1 + i;
       String $ruleCategory2 = this.getRuleCategory2();
       i1 = result * 59;
       i = ($ruleCategory2 == null)? 43: $ruleCategory2.hashCode();
       result = i1 + i;
       String diagnosisTyp = this.getDiagnosisType();
       i1 = result * 59;
       i = (diagnosisTyp == null)? 43: diagnosisTyp.hashCode();
       String ruleDescribe = this.getRuleDescribe();
       i1 = (i1 + i) * 59;
       i = (ruleDescribe == null)? 43: ruleDescribe.hashCode();
       String ps = this.getPs();
       i1 = (i1 + i) * 59;
       i = (ps == null)? 43: ps.hashCode();
       String sqlTemplate = this.getSqlTemplate();
       i1 = (i1 + i) * 59;
       i = (sqlTemplate == null)? 43: sqlTemplate.hashCode();
       String sqlExample = this.getSqlExample();
       i1 = (i1 + i) * 59;
       i = (sqlExample == null)? 43: sqlExample.hashCode();
       String parameter = this.getParameter();
       i1 = (i1 + i) * 59;
       i = (parameter == null)? 43: parameter.hashCode();
       String policyBasis = this.getPolicyBasis();
       i1 = (i1 + i) * 59;
       i = (policyBasis == null)? 43: policyBasis.hashCode();
       return (i1 + i);
    }
    public void setDiagnosisType(String diagnosisType){
       this.diagnosisType = diagnosisType;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setParameter(String parameter){
       this.parameter = parameter;
    }
    public void setPolicyBasis(String policyBasis){
       this.policyBasis = policyBasis;
    }
    public void setPs(String ps){
       this.ps = ps;
    }
    public void setRuleCategory1(String ruleCategory1){
       this.ruleCategory1 = ruleCategory1;
    }
    public void setRuleCategory2(String ruleCategory2){
       this.ruleCategory2 = ruleCategory2;
    }
    public void setRuleDescribe(String ruleDescribe){
       this.ruleDescribe = ruleDescribe;
    }
    public void setRuleLevel(String ruleLevel){
       this.ruleLevel = ruleLevel;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setSqlExample(String sqlExample){
       this.sqlExample = sqlExample;
    }
    public void setSqlTemplate(String sqlTemplate){
       this.sqlTemplate = sqlTemplate;
    }
    public String toString(){
       return "FlyRuleTemplateEditDto\(id="+this.getId()+", ruleName="+this.getRuleName()+", ruleLevel="+this.getRuleLevel()+", ruleCategory1="+this.getRuleCategory1()+", ruleCategory2="+this.getRuleCategory2()+", diagnosisType="+this.getDiagnosisType()+", ruleDescribe="+this.getRuleDescribe()+", ps="+this.getPs()+", sqlTemplate="+this.getSqlTemplate()+", sqlExample="+this.getSqlExample()+", parameter="+this.getParameter()+", policyBasis="+this.getPolicyBasis()+"\)";
    }
}
