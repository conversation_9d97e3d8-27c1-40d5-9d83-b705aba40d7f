package com.taikang.fly.check.mybatis.domain.YbDiagnosticEncoding;
import java.lang.Object;
import java.lang.String;
import java.util.Date;
import java.lang.StringBuilder;

public class YbDiagnosticEncoding	// class@000278 from classes.dex
{
    private Date createTime;
    private String creator;
    private String diagnosisCode;
    private String diagnosisName;
    private String id;
    private String modby;
    private Date modifyTime;

    public void YbDiagnosticEncoding(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof YbDiagnosticEncoding;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof YbDiagnosticEncoding){
          b = false;
       }else {
          YbDiagnosticEncoding ybDiagnostic = o;
          if (!ybDiagnostic.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = ybDiagnostic.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String diagnosisCod = this.getDiagnosisCode();
             String diagnosisCod1 = ybDiagnostic.getDiagnosisCode();
             if (diagnosisCod == null) {
                if (diagnosisCod1 != null) {
                   b = false;
                }
             }else if(diagnosisCod.equals(diagnosisCod1)){
             }
             String diagnosisNam = this.getDiagnosisName();
             String diagnosisNam1 = ybDiagnostic.getDiagnosisName();
             if (diagnosisNam == null) {
                if (diagnosisNam1 != null) {
                   b = false;
                }
             }else if(diagnosisNam.equals(diagnosisNam1)){
             }
             String creator = this.getCreator();
             String creator1 = ybDiagnostic.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             Date createTime = this.getCreateTime();
             Date createTime1 = ybDiagnostic.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modby = this.getModby();
             String modby1 = ybDiagnostic.getModby();
             if (modby == null) {
                if (modby1 != null) {
                label_0098 :
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             Date modifyTime = this.getModifyTime();
             Date modifyTime1 = ybDiagnostic.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             b = true;
          }
       }
       return b;
    }
    public Date getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getDiagnosisCode(){
       return this.diagnosisCode;
    }
    public String getDiagnosisName(){
       return this.diagnosisName;
    }
    public String getId(){
       return this.id;
    }
    public String getModby(){
       return this.modby;
    }
    public Date getModifyTime(){
       return this.modifyTime;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $diagnosisCode = this.getDiagnosisCode();
       int i2 = result * 59;
       i1 = ($diagnosisCode == null)? i: $diagnosisCode.hashCode();
       result = i2 + i1;
       String $diagnosisName = this.getDiagnosisName();
       i2 = result * 59;
       i1 = ($diagnosisName == null)? i: $diagnosisName.hashCode();
       result = i2 + i1;
       String $creator = this.getCreator();
       i2 = result * 59;
       i1 = ($creator == null)? i: $creator.hashCode();
       result = i2 + i1;
       Date $createTime = this.getCreateTime();
       i2 = result * 59;
       i1 = ($createTime == null)? i: $createTime.hashCode();
       result = i2 + i1;
       String modby = this.getModby();
       i2 = result * 59;
       i1 = (modby == null)? i: modby.hashCode();
       Date modifyTime = this.getModifyTime();
       i1 = (i2 + i1) * 59;
       if (modifyTime != null) {
          i = modifyTime.hashCode();
       }
       return (i1 + i);
    }
    public void setCreateTime(Date createTime){
       this.createTime = createTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setDiagnosisCode(String diagnosisCode){
       this.diagnosisCode = diagnosisCode;
    }
    public void setDiagnosisName(String diagnosisName){
       this.diagnosisName = diagnosisName;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setModby(String modby){
       this.modby = modby;
    }
    public void setModifyTime(Date modifyTime){
       this.modifyTime = modifyTime;
    }
    public String toString(){
       return "YbDiagnosticEncoding\(id="+this.getId()+", diagnosisCode="+this.getDiagnosisCode()+", diagnosisName="+this.getDiagnosisName()+", creator="+this.getCreator()+", createTime="+this.getCreateTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+"\)";
    }
}
