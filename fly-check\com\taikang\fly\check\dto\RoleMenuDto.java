package com.taikang.fly.check.dto.RoleMenuDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class RoleMenuDto implements Serializable	// class@0000ac from classes.dex
{
    private String createTime;
    private String creator;
    private String id;
    private String menuCode;
    private String modby;
    private String modifyTime;
    private String roleCode;
    private String signature;
    private static final long serialVersionUID = 0x349d06aa3e3fa7a6;

    public void RoleMenuDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof RoleMenuDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof RoleMenuDto){
          b = false;
       }else {
          RoleMenuDto roleMenuDto = o;
          if (!roleMenuDto.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = roleMenuDto.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String roleCode = this.getRoleCode();
             String roleCode1 = roleMenuDto.getRoleCode();
             if (roleCode == null) {
                if (roleCode1 != null) {
                   b = false;
                }
             }else if(roleCode.equals(roleCode1)){
             }
             String menuCode = this.getMenuCode();
             String menuCode1 = roleMenuDto.getMenuCode();
             if (menuCode == null) {
                if (menuCode1 != null) {
                   b = false;
                }
             }else if(menuCode.equals(menuCode1)){
             }
             String creator = this.getCreator();
             String creator1 = roleMenuDto.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             String createTime = this.getCreateTime();
             String createTime1 = roleMenuDto.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modby = this.getModby();
             String modby1 = roleMenuDto.getModby();
             if (modby == null) {
                if (modby1 != null) {
                label_009a :
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             String modifyTime = this.getModifyTime();
             String modifyTime1 = roleMenuDto.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String signature = this.getSignature();
             String signature1 = roleMenuDto.getSignature();
             if (signature == null) {
                if (signature1 != null) {
                label_00c8 :
                   b = false;
                }
             }else if(signature.equals(signature1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getId(){
       return this.id;
    }
    public String getMenuCode(){
       return this.menuCode;
    }
    public String getModby(){
       return this.modby;
    }
    public String getModifyTime(){
       return this.modifyTime;
    }
    public String getRoleCode(){
       return this.roleCode;
    }
    public String getSignature(){
       return this.signature;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $roleCode = this.getRoleCode();
       int i2 = result * 59;
       i1 = ($roleCode == null)? i: $roleCode.hashCode();
       result = i2 + i1;
       String $menuCode = this.getMenuCode();
       i2 = result * 59;
       i1 = ($menuCode == null)? i: $menuCode.hashCode();
       result = i2 + i1;
       String $creator = this.getCreator();
       i2 = result * 59;
       i1 = ($creator == null)? i: $creator.hashCode();
       result = i2 + i1;
       String $createTime = this.getCreateTime();
       i2 = result * 59;
       i1 = ($createTime == null)? i: $createTime.hashCode();
       result = i2 + i1;
       String modby = this.getModby();
       i2 = result * 59;
       i1 = (modby == null)? i: modby.hashCode();
       String modifyTime = this.getModifyTime();
       i2 = (i2 + i1) * 59;
       i1 = (modifyTime == null)? i: modifyTime.hashCode();
       String signature = this.getSignature();
       i1 = (i2 + i1) * 59;
       if (signature != null) {
          i = signature.hashCode();
       }
       return (i1 + i);
    }
    public void setCreateTime(String createTime){
       this.createTime = createTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setMenuCode(String menuCode){
       this.menuCode = menuCode;
    }
    public void setModby(String modby){
       this.modby = modby;
    }
    public void setModifyTime(String modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setRoleCode(String roleCode){
       this.roleCode = roleCode;
    }
    public void setSignature(String signature){
       this.signature = signature;
    }
    public String toString(){
       return "RoleMenuDto\(id="+this.getId()+", roleCode="+this.getRoleCode()+", menuCode="+this.getMenuCode()+", creator="+this.getCreator()+", createTime="+this.getCreateTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+", signature="+this.getSignature()+"\)";
    }
}
