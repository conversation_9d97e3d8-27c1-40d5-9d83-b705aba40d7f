package com.taikang.fly.check.rest.SystemConfigController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import java.lang.String;
import com.taikang.fly.check.dto.RmpResponse;
import java.lang.Integer;
import com.taikang.fly.check.service.SystemConfigService;
import com.taikang.fly.check.mybatis.domain.SystemConfig;

public class SystemConfigController	// class@0002b1 from classes.dex
{
    private SystemConfigService systemConfigService;
    private String version;
    private static final Logger log;

    static {
       SystemConfigController.log = LoggerFactory.getLogger(SystemConfigController.class);
    }
    public void SystemConfigController(){
       super();
    }
    public RmpResponse editApplicationName(String applicationName){
       this.systemConfigService.editApplicationName(applicationName);
       return RmpResponse.success();
    }
    public RmpResponse getApplicationName(){
       SystemConfig systemConfig = this.systemConfigService.getApplicationName();
       systemConfig.setApplicationVersion(this.version);
       return RmpResponse.success(systemConfig);
    }
}
