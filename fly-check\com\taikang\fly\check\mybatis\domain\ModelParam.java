package com.taikang.fly.check.mybatis.domain.ModelParam;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ModelParam	// class@000259 from classes.dex
{
    private String id;
    private String modelId;
    private String paramName;
    private String paramType;

    public void ModelParam(){
       super();
    }
    public void ModelParam(String id,String modelId,String paramName,String paramType){
       super();
       this.id = id;
       this.modelId = modelId;
       this.paramName = paramName;
       this.paramType = paramType;
    }
    protected boolean canEqual(Object other){
       return other instanceof ModelParam;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof ModelParam) {
             b = false;
          }else {
             ModelParam modelParam = o;
             if (!modelParam.canEqual(this)) {
                b = false;
             }else {
                String id = this.getId();
                String id1 = modelParam.getId();
                if (id == null) {
                   if (id1 != null) {
                      b = false;
                   }
                }else if(id.equals(id1)){
                }
                String modelId = this.getModelId();
                String modelId1 = modelParam.getModelId();
                if (modelId == null) {
                   if (modelId1 != null) {
                      b = false;
                   }
                }else if(modelId.equals(modelId1)){
                }
                String paramName = this.getParamName();
                String paramName1 = modelParam.getParamName();
                if (paramName == null) {
                   if (paramName1 != null) {
                      b = false;
                   }
                }else if(paramName.equals(paramName1)){
                }
                String paramType = this.getParamType();
                String paramType1 = modelParam.getParamType();
                if (paramType == null) {
                   if (paramType1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!paramType.equals(paramType1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getId(){
       return this.id;
    }
    public String getModelId(){
       return this.modelId;
    }
    public String getParamName(){
       return this.paramName;
    }
    public String getParamType(){
       return this.paramType;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $modelId = this.getModelId();
       int i2 = result * 59;
       i1 = ($modelId == null)? i: $modelId.hashCode();
       result = i2 + i1;
       String $paramName = this.getParamName();
       i2 = result * 59;
       i1 = ($paramName == null)? i: $paramName.hashCode();
       result = i2 + i1;
       String $paramType = this.getParamType();
       i1 = result * 59;
       if ($paramType != null) {
          i = $paramType.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setModelId(String modelId){
       this.modelId = modelId;
    }
    public void setParamName(String paramName){
       this.paramName = paramName;
    }
    public void setParamType(String paramType){
       this.paramType = paramType;
    }
    public String toString(){
       return "ModelParam\(id="+this.getId()+", modelId="+this.getModelId()+", paramName="+this.getParamName()+", paramType="+this.getParamType()+"\)";
    }
}
