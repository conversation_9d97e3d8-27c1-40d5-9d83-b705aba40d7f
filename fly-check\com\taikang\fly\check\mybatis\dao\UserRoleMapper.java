package com.taikang.fly.check.mybatis.dao.UserRoleMapper;
import java.util.Map;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.taikang.fly.check.mybatis.domain.UserRole;

public interface abstract UserRoleMapper	// class@000227 from classes.dex
{

    void addUserRole(Map p0);
    Integer deleteById(String p0);
    void deleteByUserCode(String p0);
    List findRoleList();
    UserRole getById(String p0);
    List getUserListByRoleCode(Map p0);
    List queryListByParams(Map p0);
    List roleIdsByUserCode(String p0);
    Integer save(UserRole p0);
    Integer update(UserRole p0);
}
