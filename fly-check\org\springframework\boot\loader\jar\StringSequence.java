package org.springframework.boot.loader.jar.StringSequence;
import java.lang.CharSequence;
import java.lang.String;
import java.lang.Object;
import java.util.Objects;
import java.lang.StringIndexOutOfBoundsException;

final class StringSequence implements CharSequence	// class@00055d from classes.dex
{
    private final int end;
    private int hash;
    private final String source;
    private final int start;

    void StringSequence(String source){
       int i = (source != null)? source.length(): -1;
       super(source, 0, i);
       return;
    }
    void StringSequence(String source,int start,int end){
       super();
       Objects.requireNonNull(source, "Source must not be null");
       if (start < 0) {
          throw new StringIndexOutOfBoundsException(start);
       }
       if (end > source.length()) {
          throw new StringIndexOutOfBoundsException(end);
       }
       this.source = source;
       this.start = start;
       this.end = end;
       return;
    }
    public char charAt(int index){
       return this.source.charAt((this.start + index));
    }
    public boolean equals(Object obj){
       int i;
       boolean b = true;
       if (this != obj) {
          if (!obj instanceof CharSequence) {
             b = false;
          }else {
             CharSequence uCharSequenc = obj;
             if ((i = this.length()) != uCharSequenc.length()) {
                b = false;
             }else {
                int i1 = 0;
                int i2 = i;
                while (true) {
                   i = i2 - 1;
                   if (i2) {
                      if (this.charAt(i1) != uCharSequenc.charAt(i1)) {
                         b = false;
                         break ;
                      }else {
                         i1 = i1 + 1;
                         i2 = i;
                      }
                   }
                }
             }
          }
       }
       return b;
    }
    public int hashCode(){
       int hash;
       if ((hash = this.hash) == null && this.length() > 0) {
          for (StringSequence tstart = this.start; tstart < this.end; tstart = tstart + 1) {
             int i = hash * 31;
             hash = i + this.source.charAt(tstart);
          }
          this.hash = hash;
       }
       return hash;
    }
    public int indexOf(char ch){
       return (this.source.indexOf(ch, this.start) - this.start);
    }
    public int indexOf(String str){
       return (this.source.indexOf(str, this.start) - this.start);
    }
    public int indexOf(String str,int fromIndex){
       return (this.source.indexOf(str, (this.start + fromIndex)) - this.start);
    }
    public boolean isEmpty(){
       boolean b = (!this.length())? true: false;
       return b;
    }
    public int length(){
       return (this.end - this.start);
    }
    public boolean startsWith(CharSequence prefix){
       return this.startsWith(prefix, 0);
    }
    public boolean startsWith(CharSequence prefix,int offset){
       boolean b = false;
       int prefixLength = prefix.length();
       if ((((this.length() - prefixLength) - offset)) >= 0) {
          int prefixOffset = 0;
          int sourceOffset = offset;
          sourceOffset = sourceOffset;
          prefixOffset = prefixOffset;
          prefixLength = prefixLength;
          while (true) {
             prefixLength = prefixLength - 1;
             if (prefixLength) {
                sourceOffset = sourceOffset + 1;
                prefixOffset = prefixOffset + 1;
                if (this.charAt(sourceOffset) == prefix.charAt(prefixOffset)) {
                   sourceOffset = sourceOffset;
                   prefixOffset = prefixOffset;
                   prefixLength = prefixLength;
                }
             }else {
                b = true;
                break ;
             }
          }
       }
       return b;
    }
    public CharSequence subSequence(int p0,int p1){
       return this.subSequence(p0, p1);
    }
    public StringSequence subSequence(int start){
       return this.subSequence(start, this.length());
    }
    public StringSequence subSequence(int start,int end){
       int subSequenceStart = this.start + start;
       int subSequenceEnd = this.start + end;
       if (subSequenceStart > this.end) {
          throw new StringIndexOutOfBoundsException(start);
       }
       if (subSequenceEnd > this.end) {
          throw new StringIndexOutOfBoundsException(end);
       }
       if (start || subSequenceEnd != this.end) {
          StringSequence stringSequen = new StringSequence(this.source, subSequenceStart, subSequenceEnd);
       }
       return this;
    }
    public String toString(){
       return this.source.substring(this.start, this.end);
    }
}
