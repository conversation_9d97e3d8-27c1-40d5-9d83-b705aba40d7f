package com.taikang.fly.check.dto.mapstruct.YbDrugInstructionsMappingImpl;
import com.taikang.fly.check.dto.mapstruct.YbDrugInstructionsMapping;
import java.lang.Object;
import com.taikang.fly.check.mybatis.domain.YbDrugInstructions;
import com.taikang.fly.check.dto.ybDrugInstructions.YbDrugInstructionsRespDto;
import java.util.Date;
import java.lang.String;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;

public class YbDrugInstructionsMappingImpl implements YbDrugInstructionsMapping	// class@000188 from classes.dex
{

    public void YbDrugInstructionsMappingImpl(){
       super();
    }
    public YbDrugInstructionsRespDto domainToRespDto(YbDrugInstructions ybDrugInstructions){
       YbDrugInstructionsRespDto ybDrugInstru;
       if (ybDrugInstructions == null) {
          ybDrugInstru = null;
       }else {
          ybDrugInstru = new YbDrugInstructionsRespDto();
          ybDrugInstru.setApprovalData(ybDrugInstructions.getApprovalData());
          ybDrugInstru.setId(ybDrugInstructions.getId());
          ybDrugInstru.setGenericName(ybDrugInstructions.getGenericName());
          ybDrugInstru.setClassification(ybDrugInstructions.getClassification());
          ybDrugInstru.setTradeName(ybDrugInstructions.getTradeName());
          ybDrugInstru.setGenericName2(ybDrugInstructions.getGenericName2());
          ybDrugInstru.setEnglishName(ybDrugInstructions.getEnglishName());
          ybDrugInstru.setComponent(ybDrugInstructions.getComponent());
          ybDrugInstru.setCharacter(ybDrugInstructions.getCharacter());
          ybDrugInstru.setApplicable(ybDrugInstructions.getApplicable());
          ybDrugInstru.setSpecifications(ybDrugInstructions.getSpecifications());
          ybDrugInstru.setUsageDosage(ybDrugInstructions.getUsageDosage());
          ybDrugInstru.setSideEffects(ybDrugInstructions.getSideEffects());
          ybDrugInstru.setTaboo(ybDrugInstructions.getTaboo());
          ybDrugInstru.setMattersNeeding(ybDrugInstructions.getMattersNeeding());
          ybDrugInstru.setDrugInteractions(ybDrugInstructions.getDrugInteractions());
          ybDrugInstru.setDrugAction(ybDrugInstructions.getDrugAction());
          ybDrugInstru.setStore(ybDrugInstructions.getStore());
          ybDrugInstru.setTermOfValidity(ybDrugInstructions.getTermOfValidity());
          ybDrugInstru.setApprovalNumber(ybDrugInstructions.getApprovalNumber());
          ybDrugInstru.setManufacturingEnterprise(ybDrugInstructions.getManufacturingEnterprise());
          ybDrugInstru.setAddre(ybDrugInstructions.getAddre());
       }
       return ybDrugInstru;
    }
    public List domainsToRespDtoList(List ybDrugInstructionsList){
       List list;
       if (ybDrugInstructionsList == null) {
          list = null;
       }else {
          list = new ArrayList(ybDrugInstructionsList.size());
          Iterator iterator = ybDrugInstructionsList.iterator();
          while (iterator.hasNext()) {
             list.add(this.domainToRespDto(iterator.next()));
          }
       }
       return list;
    }
}
