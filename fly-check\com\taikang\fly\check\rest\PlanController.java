package com.taikang.fly.check.rest.PlanController;
import java.lang.Object;
import com.taikang.fly.check.dto.plan.PlanAddDto;
import com.taikang.fly.check.dto.RmpResponse;
import java.lang.Integer;
import com.taikang.fly.check.service.PlanService;
import com.taikang.fly.check.dto.plan.PlanRuleSearchDto;
import com.taikang.fly.check.comm.NativePage;
import java.lang.String;
import com.taikang.fly.check.dto.plan.PlanSearchDto;

public class PlanController	// class@0002a5 from classes.dex
{
    private PlanService planService;

    public void PlanController(){
       super();
    }
    public RmpResponse add(PlanAddDto planAddDto){
       return RmpResponse.success(this.planService.add(planAddDto));
    }
    public RmpResponse configRules(PlanRuleSearchDto planRuleSearchDto,Integer pageNum,Integer pageSize){
       return RmpResponse.success(this.planService.configRules(planRuleSearchDto, pageSize, pageNum));
    }
    public RmpResponse deletePlan(String id){
       return RmpResponse.success(this.planService.deleteById(id));
    }
    public RmpResponse queryPlanListPage(PlanSearchDto planSearchDto,Integer pageNum,Integer pageSize){
       NativePage planLogListPage = this.planService.queryPlanListPage(planSearchDto, pageNum, pageSize);
       return RmpResponse.success(planLogListPage);
    }
}
