package org.springframework.boot.loader.data.RandomAccessDataFile$FileAccess;
import java.io.File;
import java.lang.Object;
import org.springframework.boot.loader.data.RandomAccessDataFile$1;
import java.io.RandomAccessFile;
import java.lang.String;
import java.lang.IllegalArgumentException;

final class RandomAccessDataFile$FileAccess	// class@000547 from classes.dex
{
    private final File file;
    private final Object monitor;
    private RandomAccessFile randomAccessFile;

    private void RandomAccessDataFile$FileAccess(File file){
       super();
       this.monitor = new Object();
       this.file = file;
       this.openIfNecessary();
    }
    void RandomAccessDataFile$FileAccess(File x0,RandomAccessDataFile$1 x1){
       super(x0);
    }
    static File access$100(RandomAccessDataFile$FileAccess x0){
       return x0.file;
    }
    static int access$300(RandomAccessDataFile$FileAccess x0,long x1){
       return x0.readByte(x1);
    }
    static int access$400(RandomAccessDataFile$FileAccess x0,byte[] x1,long x2,int x3,int x4){
       return x0.read(x1, x2, x3, x4);
    }
    static void access$500(RandomAccessDataFile$FileAccess x0){
       x0.close();
    }
    private void close(){
       RandomAccessDataFile$FileAccess tmonitor = this.monitor;
       _monitor_enter(tmonitor);
       if (this.randomAccessFile != null) {
          this.randomAccessFile.close();
          this.randomAccessFile = null;
       }
       _monitor_exit(tmonitor);
       return;
    }
    private void openIfNecessary(){
       try{
          if (this.randomAccessFile == null) {
             this.randomAccessFile = new RandomAccessFile(this.file, "r");
          }
          return;
       }catch(java.io.FileNotFoundException e0){
          Object[] objArray = new Object[]{this.file.getAbsolutePath()};
          throw new IllegalArgumentException(String.format("File %s must exist", objArray));
       }
    }
    private int read(byte[] bytes,long position,int offset,int length){
       RandomAccessDataFile$FileAccess tmonitor = this.monitor;
       _monitor_enter(tmonitor);
       this.openIfNecessary();
       this.randomAccessFile.seek(position);
       _monitor_exit(tmonitor);
       return this.randomAccessFile.read(bytes, offset, length);
    }
    private int readByte(long position){
       RandomAccessDataFile$FileAccess tmonitor = this.monitor;
       _monitor_enter(tmonitor);
       this.openIfNecessary();
       this.randomAccessFile.seek(position);
       _monitor_exit(tmonitor);
       return this.randomAccessFile.read();
    }
}
