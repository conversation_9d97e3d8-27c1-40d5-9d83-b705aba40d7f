package com.taikang.fly.check.dto.module.UserModuleManageDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class UserModuleManageDto implements Serializable	// class@00019b from classes.dex
{
    private String moduleCode;
    private String userCode;
    private static final long serialVersionUID = 0x1;

    public void UserModuleManageDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof UserModuleManageDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof UserModuleManageDto) {
             b = false;
          }else {
             UserModuleManageDto userModuleMa = o;
             if (!userModuleMa.canEqual(this)) {
                b = false;
             }else {
                String userCode = this.getUserCode();
                String userCode1 = userModuleMa.getUserCode();
                if (userCode == null) {
                   if (userCode1 != null) {
                      b = false;
                   }
                }else if(userCode.equals(userCode1)){
                }
                String moduleCode = this.getModuleCode();
                String moduleCode1 = userModuleMa.getModuleCode();
                if (moduleCode == null) {
                   if (moduleCode1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!moduleCode.equals(moduleCode1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getModuleCode(){
       return this.moduleCode;
    }
    public String getUserCode(){
       return this.userCode;
    }
    public int hashCode(){
       String $userCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($userCode = this.getUserCode()) == null)? i: $userCode.hashCode();
       result = i1 + 59;
       String $moduleCode = this.getModuleCode();
       i1 = result * 59;
       if ($moduleCode != null) {
          i = $moduleCode.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setModuleCode(String moduleCode){
       this.moduleCode = moduleCode;
    }
    public void setUserCode(String userCode){
       this.userCode = userCode;
    }
    public String toString(){
       return "UserModuleManageDto\(userCode="+this.getUserCode()+", moduleCode="+this.getModuleCode()+"\)";
    }
}
