package com.taikang.fly.check.dto.sql.SqlDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import java.lang.StringBuilder;

public class SqlDto implements Serializable	// class@0001b2 from classes.dex
{
    private String exeSql;
    private Integer isClickhouse;
    private static final long serialVersionUID = 0x7dede0ec71772e8;

    public void SqlDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof SqlDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof SqlDto) {
             b = false;
          }else {
             SqlDto sqlDto = o;
             if (!sqlDto.canEqual(this)) {
                b = false;
             }else {
                String exeSql = this.getExeSql();
                String exeSql1 = sqlDto.getExeSql();
                if (exeSql == null) {
                   if (exeSql1 != null) {
                      b = false;
                   }
                }else if(exeSql.equals(exeSql1)){
                }
                Integer isClickhouse = this.getIsClickhouse();
                Integer isClickhouse1 = sqlDto.getIsClickhouse();
                if (isClickhouse == null) {
                   if (isClickhouse1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!isClickhouse.equals(isClickhouse1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getExeSql(){
       return this.exeSql;
    }
    public Integer getIsClickhouse(){
       return this.isClickhouse;
    }
    public int hashCode(){
       String $exeSql;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($exeSql = this.getExeSql()) == null)? i: $exeSql.hashCode();
       result = i1 + 59;
       Integer $isClickhouse = this.getIsClickhouse();
       i1 = result * 59;
       if ($isClickhouse != null) {
          i = $isClickhouse.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setExeSql(String exeSql){
       this.exeSql = exeSql;
    }
    public void setIsClickhouse(Integer isClickhouse){
       this.isClickhouse = isClickhouse;
    }
    public String toString(){
       return "SqlDto\(exeSql="+this.getExeSql()+", isClickhouse="+this.getIsClickhouse()+"\)";
    }
}
