package com.taikang.fly.check.mybatis.dao.ThreeOrderMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import com.taikang.fly.check.dto.threeOrder.ThreeOrderDto;
import java.util.List;
import java.util.Map;

public interface abstract ThreeOrderMapper implements BaseMapper	// class@000223 from classes.dex
{

    void createTable(String p0);
    void dropThreeOrderTable(String p0);
    int exist(String p0);
    void extract(String p0);
    List findAllList(ThreeOrderDto p0,String p1);
    List findCharItemLList(String p0);
    List findCostTypeList(String p0);
    List findListByProjectName(Map p0);
    List findTypesOfChargesList(String p0);
    List getList(Map p0);
    List selectDepartList(String p0);
    List selectMzDepartList(String p0);
}
