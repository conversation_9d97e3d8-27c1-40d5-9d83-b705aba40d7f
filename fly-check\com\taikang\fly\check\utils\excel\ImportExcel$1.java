package com.taikang.fly.check.utils.excel.ImportExcel$1;
import java.util.Comparator;
import com.taikang.fly.check.utils.excel.ImportExcel;
import java.lang.Object;
import com.taikang.fly.check.utils.excel.annotation.ExcelField;
import java.lang.Integer;

class ImportExcel$1 implements Comparator	// class@000354 from classes.dex
{
    final ImportExcel this$0;

    void ImportExcel$1(ImportExcel this$0){
       this.this$0 = this$0;
       super();
    }
    public int compare(Object p0,Object p1){
       return this.compare(p0, p1);
    }
    public int compare(Object[] o1,Object[] o2){
       return Integer.valueOf(o1[0].sort()).compareTo(Integer.valueOf(o2[0].sort()));
    }
}
