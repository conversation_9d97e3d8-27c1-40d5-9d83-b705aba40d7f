package com.taikang.fly.check.mybatis.dao.FlyRuleAuditMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.Map;
import java.util.List;
import com.taikang.fly.check.mybatis.domain.FlyRule;

public interface abstract FlyRuleAuditMapper implements BaseMapper	// class@0001f1 from classes.dex
{

    List findFlyRuleAuditPage(Map p0);
    List queryAllId();
    int updateStatus(FlyRule p0);
}
