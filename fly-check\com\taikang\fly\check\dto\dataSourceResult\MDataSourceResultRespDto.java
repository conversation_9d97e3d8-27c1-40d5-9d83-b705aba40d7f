package com.taikang.fly.check.dto.dataSourceResult.MDataSourceResultRespDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.time.LocalDate;
import java.lang.StringBuilder;

public class MDataSourceResultRespDto implements Serializable	// class@0000e0 from classes.dex
{
    private LocalDate createdTime;
    private String creator;
    private String hospName;
    private String id;
    private String oraUserName;
    private String path;
    private String region;
    private static final long serialVersionUID = 0x1;

    public void MDataSourceResultRespDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MDataSourceResultRespDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof MDataSourceResultRespDto){
          b = false;
       }else {
          MDataSourceResultRespDto mDataSourceR = o;
          if (!mDataSourceR.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = mDataSourceR.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String hospName = this.getHospName();
             String hospName1 = mDataSourceR.getHospName();
             if (hospName == null) {
                if (hospName1 != null) {
                   b = false;
                }
             }else if(hospName.equals(hospName1)){
             }
             String creator = this.getCreator();
             String creator1 = mDataSourceR.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             LocalDate createdTime = this.getCreatedTime();
             LocalDate createdTime1 = mDataSourceR.getCreatedTime();
             if (createdTime == null) {
                if (createdTime1 != null) {
                   b = false;
                }
             }else if(createdTime.equals(createdTime1)){
             }
             String region = this.getRegion();
             String region1 = mDataSourceR.getRegion();
             if (region == null) {
                if (region1 != null) {
                   b = false;
                }
             }else if(region.equals(region1)){
             }
             String oraUserName = this.getOraUserName();
             String oraUserName1 = mDataSourceR.getOraUserName();
             if (oraUserName == null) {
                if (oraUserName1 != null) {
                   b = false;
                }
             }else if(oraUserName.equals(oraUserName1)){
             }
             String path = this.getPath();
             String path1 = mDataSourceR.getPath();
             if (path == null) {
                if (path1 != null) {
                   b = false;
                }
             }else if(path.equals(path1)){
             }
             b = true;
          }
       }
       return b;
    }
    public LocalDate getCreatedTime(){
       return this.createdTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getHospName(){
       return this.hospName;
    }
    public String getId(){
       return this.id;
    }
    public String getOraUserName(){
       return this.oraUserName;
    }
    public String getPath(){
       return this.path;
    }
    public String getRegion(){
       return this.region;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $hospName = this.getHospName();
       int i2 = result * 59;
       i1 = ($hospName == null)? i: $hospName.hashCode();
       result = i2 + i1;
       String $creator = this.getCreator();
       i2 = result * 59;
       i1 = ($creator == null)? i: $creator.hashCode();
       result = i2 + i1;
       LocalDate $createdTime = this.getCreatedTime();
       i2 = result * 59;
       i1 = ($createdTime == null)? i: $createdTime.hashCode();
       result = i2 + i1;
       String $region = this.getRegion();
       i2 = result * 59;
       i1 = ($region == null)? i: $region.hashCode();
       result = i2 + i1;
       String oraUserName = this.getOraUserName();
       i2 = result * 59;
       i1 = (oraUserName == null)? i: oraUserName.hashCode();
       String path = this.getPath();
       i1 = (i2 + i1) * 59;
       if (path != null) {
          i = path.hashCode();
       }
       return (i1 + i);
    }
    public void setCreatedTime(LocalDate createdTime){
       this.createdTime = createdTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setHospName(String hospName){
       this.hospName = hospName;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setOraUserName(String oraUserName){
       this.oraUserName = oraUserName;
    }
    public void setPath(String path){
       this.path = path;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public String toString(){
       return "MDataSourceResultRespDto\(id="+this.getId()+", hospName="+this.getHospName()+", creator="+this.getCreator()+", createdTime="+this.getCreatedTime()+", region="+this.getRegion()+", oraUserName="+this.getOraUserName()+", path="+this.getPath()+"\)";
    }
}
