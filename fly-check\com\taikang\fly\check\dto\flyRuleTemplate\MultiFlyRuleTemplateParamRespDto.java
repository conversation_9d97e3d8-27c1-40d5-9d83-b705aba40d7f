package com.taikang.fly.check.dto.flyRuleTemplate.MultiFlyRuleTemplateParamRespDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.time.LocalDate;
import java.lang.StringBuilder;

public class MultiFlyRuleTemplateParamRespDto implements Serializable	// class@00011f from classes.dex
{
    private LocalDate createdTime;
    private String creater;
    private String id;
    private LocalDate operateTime;
    private String operator;
    private String paramCode;
    private String paramDesc;
    private String paramName;
    private String paramType;
    private String removed;
    private String templateName;
    private String templateType;
    private static final long serialVersionUID = 0x1;

    public void MultiFlyRuleTemplateParamRespDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MultiFlyRuleTemplateParamRespDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof MultiFlyRuleTemplateParamRespDto){
          b = false;
       }else {
          MultiFlyRuleTemplateParamRespDto multiFlyRule = o;
          if (!multiFlyRule.canEqual(this)) {
             b = false;
          }else {
             String paramCode = this.getParamCode();
             String paramCode1 = multiFlyRule.getParamCode();
             if (paramCode == null) {
                if (paramCode1 != null) {
                   b = false;
                }
             }else if(paramCode.equals(paramCode1)){
             }
             String paramName = this.getParamName();
             String paramName1 = multiFlyRule.getParamName();
             if (paramName == null) {
                if (paramName1 != null) {
                   b = false;
                }
             }else if(paramName.equals(paramName1)){
             }
             String paramDesc = this.getParamDesc();
             String paramDesc1 = multiFlyRule.getParamDesc();
             if (paramDesc == null) {
                if (paramDesc1 != null) {
                   b = false;
                }
             }else if(paramDesc.equals(paramDesc1)){
             }
             String paramType = this.getParamType();
             String paramType1 = multiFlyRule.getParamType();
             if (paramType == null) {
                if (paramType1 != null) {
                   b = false;
                }
             }else if(paramType.equals(paramType1)){
             }
             String creater = this.getCreater();
             String creater1 = multiFlyRule.getCreater();
             if (creater == null) {
                if (creater1 != null) {
                   b = false;
                }
             }else if(creater.equals(creater1)){
             }
             String operator = this.getOperator();
             String operator1 = multiFlyRule.getOperator();
             if (operator == null) {
                if (operator1 != null) {
                   b = false;
                }
             }else if(operator.equals(operator1)){
             }
             LocalDate operateTime = this.getOperateTime();
             LocalDate operateTime1 = multiFlyRule.getOperateTime();
             if (operateTime == null) {
                if (operateTime1 != null) {
                   b = false;
                }
             }else if(operateTime.equals(operateTime1)){
             }
             LocalDate createdTime = this.getCreatedTime();
             LocalDate createdTime1 = multiFlyRule.getCreatedTime();
             if (createdTime == null) {
                if (createdTime1 != null) {
                   b = false;
                }
             }else if(createdTime.equals(createdTime1)){
             }
             String id = this.getId();
             String id1 = multiFlyRule.getId();
             if (id == null) {
                if (id1 != null) {
                label_00e9 :
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String removed = this.getRemoved();
             String removed1 = multiFlyRule.getRemoved();
             if (removed == null) {
                if (removed1 != null) {
                   b = false;
                }
             }else if(removed.equals(removed1)){
             }
             String templateType = this.getTemplateType();
             String templateType1 = multiFlyRule.getTemplateType();
             if (templateType == null) {
                if (templateType1 != null) {
                label_0119 :
                   b = false;
                }
             }else if(templateType.equals(templateType1)){
             }
             String templateName = this.getTemplateName();
             String templateName1 = multiFlyRule.getTemplateName();
             if (templateName == null) {
                if (templateName1 != null) {
                   b = false;
                }
             }else if(templateName.equals(templateName1)){
             }
             b = true;
          }
       }
       return b;
    }
    public LocalDate getCreatedTime(){
       return this.createdTime;
    }
    public String getCreater(){
       return this.creater;
    }
    public String getId(){
       return this.id;
    }
    public LocalDate getOperateTime(){
       return this.operateTime;
    }
    public String getOperator(){
       return this.operator;
    }
    public String getParamCode(){
       return this.paramCode;
    }
    public String getParamDesc(){
       return this.paramDesc;
    }
    public String getParamName(){
       return this.paramName;
    }
    public String getParamType(){
       return this.paramType;
    }
    public String getRemoved(){
       return this.removed;
    }
    public String getTemplateName(){
       return this.templateName;
    }
    public String getTemplateType(){
       return this.templateType;
    }
    public int hashCode(){
       String $paramCode;
       int PRIME = 59;
       int result = 1;
       int i = (($paramCode = this.getParamCode()) == null)? 43: $paramCode.hashCode();
       result = i + 59;
       String $paramName = this.getParamName();
       int i1 = result * 59;
       i = ($paramName == null)? 43: $paramName.hashCode();
       result = i1 + i;
       String $paramDesc = this.getParamDesc();
       i1 = result * 59;
       i = ($paramDesc == null)? 43: $paramDesc.hashCode();
       result = i1 + i;
       String $paramType = this.getParamType();
       i1 = result * 59;
       i = ($paramType == null)? 43: $paramType.hashCode();
       result = i1 + i;
       String $creater = this.getCreater();
       i1 = result * 59;
       i = ($creater == null)? 43: $creater.hashCode();
       result = i1 + i;
       String operator = this.getOperator();
       i1 = result * 59;
       i = (operator == null)? 43: operator.hashCode();
       LocalDate operateTime = this.getOperateTime();
       i1 = (i1 + i) * 59;
       i = (operateTime == null)? 43: operateTime.hashCode();
       LocalDate createdTime = this.getCreatedTime();
       i1 = (i1 + i) * 59;
       i = (createdTime == null)? 43: createdTime.hashCode();
       String id = this.getId();
       i1 = (i1 + i) * 59;
       i = (id == null)? 43: id.hashCode();
       String removed = this.getRemoved();
       i1 = (i1 + i) * 59;
       i = (removed == null)? 43: removed.hashCode();
       String templateType = this.getTemplateType();
       i1 = (i1 + i) * 59;
       i = (templateType == null)? 43: templateType.hashCode();
       String templateName = this.getTemplateName();
       i1 = (i1 + i) * 59;
       i = (templateName == null)? 43: templateName.hashCode();
       return (i1 + i);
    }
    public void setCreatedTime(LocalDate createdTime){
       this.createdTime = createdTime;
    }
    public void setCreater(String creater){
       this.creater = creater;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setOperateTime(LocalDate operateTime){
       this.operateTime = operateTime;
    }
    public void setOperator(String operator){
       this.operator = operator;
    }
    public void setParamCode(String paramCode){
       this.paramCode = paramCode;
    }
    public void setParamDesc(String paramDesc){
       this.paramDesc = paramDesc;
    }
    public void setParamName(String paramName){
       this.paramName = paramName;
    }
    public void setParamType(String paramType){
       this.paramType = paramType;
    }
    public void setRemoved(String removed){
       this.removed = removed;
    }
    public void setTemplateName(String templateName){
       this.templateName = templateName;
    }
    public void setTemplateType(String templateType){
       this.templateType = templateType;
    }
    public String toString(){
       return "MultiFlyRuleTemplateParamRespDto\(paramCode="+this.getParamCode()+", paramName="+this.getParamName()+", paramDesc="+this.getParamDesc()+", paramType="+this.getParamType()+", creater="+this.getCreater()+", operator="+this.getOperator()+", operateTime="+this.getOperateTime()+", createdTime="+this.getCreatedTime()+", id="+this.getId()+", removed="+this.getRemoved()+", templateType="+this.getTemplateType()+", templateName="+this.getTemplateName()+"\)";
    }
}
