package com.taikang.fly.check.vo.drg.DrgRuleStatisticsVO;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import java.lang.StringBuilder;

public class DrgRuleStatisticsVO	// class@000378 from classes.dex
{
    private String drgRuleName;
    private String drgRuleType;
    private Integer setlIdCount;

    public void DrgRuleStatisticsVO(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgRuleStatisticsVO;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DrgRuleStatisticsVO) {
             b = false;
          }else {
             DrgRuleStatisticsVO uDrgRuleStat = o;
             if (!uDrgRuleStat.canEqual(this)) {
                b = false;
             }else {
                String drgRuleName = this.getDrgRuleName();
                String drgRuleName1 = uDrgRuleStat.getDrgRuleName();
                if (drgRuleName == null) {
                   if (drgRuleName1 != null) {
                      b = false;
                   }
                }else if(drgRuleName.equals(drgRuleName1)){
                }
                String drgRuleType = this.getDrgRuleType();
                String drgRuleType1 = uDrgRuleStat.getDrgRuleType();
                if (drgRuleType == null) {
                   if (drgRuleType1 != null) {
                      b = false;
                   }
                }else if(drgRuleType.equals(drgRuleType1)){
                }
                Integer setlIdCount = this.getSetlIdCount();
                Integer setlIdCount1 = uDrgRuleStat.getSetlIdCount();
                if (setlIdCount == null) {
                   if (setlIdCount1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!setlIdCount.equals(setlIdCount1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getDrgRuleName(){
       return this.drgRuleName;
    }
    public String getDrgRuleType(){
       return this.drgRuleType;
    }
    public Integer getSetlIdCount(){
       return this.setlIdCount;
    }
    public int hashCode(){
       String $drgRuleName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($drgRuleName = this.getDrgRuleName()) == null)? i: $drgRuleName.hashCode();
       result = i1 + 59;
       String $drgRuleType = this.getDrgRuleType();
       int i2 = result * 59;
       i1 = ($drgRuleType == null)? i: $drgRuleType.hashCode();
       result = i2 + i1;
       Integer $setlIdCount = this.getSetlIdCount();
       i1 = result * 59;
       if ($setlIdCount != null) {
          i = $setlIdCount.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDrgRuleName(String drgRuleName){
       this.drgRuleName = drgRuleName;
    }
    public void setDrgRuleType(String drgRuleType){
       this.drgRuleType = drgRuleType;
    }
    public void setSetlIdCount(Integer setlIdCount){
       this.setlIdCount = setlIdCount;
    }
    public String toString(){
       return "DrgRuleStatisticsVO\(drgRuleName="+this.getDrgRuleName()+", drgRuleType="+this.getDrgRuleType()+", setlIdCount="+this.getSetlIdCount()+"\)";
    }
}
