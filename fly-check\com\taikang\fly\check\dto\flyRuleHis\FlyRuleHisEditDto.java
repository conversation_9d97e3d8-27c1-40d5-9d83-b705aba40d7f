package com.taikang.fly.check.dto.flyRuleHis.FlyRuleHisEditDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class FlyRuleHisEditDto implements Serializable	// class@000114 from classes.dex
{
    private String SqlName;
    private String id;
    private String newSqlName;
    private String operateTime;
    private String ps;
    private String region;
    private String ruleName;
    private String ruleType;
    private static final long serialVersionUID = 0x1;

    public void FlyRuleHisEditDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRuleHisEditDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof FlyRuleHisEditDto){
          b = false;
       }else {
          FlyRuleHisEditDto uFlyRuleHisE = o;
          if (!uFlyRuleHisE.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = uFlyRuleHisE.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String ruleName = this.getRuleName();
             String ruleName1 = uFlyRuleHisE.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String region = this.getRegion();
             String region1 = uFlyRuleHisE.getRegion();
             if (region == null) {
                if (region1 != null) {
                   b = false;
                }
             }else if(region.equals(region1)){
             }
             String sqlName = this.getSqlName();
             String sqlName1 = uFlyRuleHisE.getSqlName();
             if (sqlName == null) {
                if (sqlName1 != null) {
                   b = false;
                }
             }else if(sqlName.equals(sqlName1)){
             }
             String newSqlName = this.getNewSqlName();
             String newSqlName1 = uFlyRuleHisE.getNewSqlName();
             if (newSqlName == null) {
                if (newSqlName1 != null) {
                   b = false;
                }
             }else if(newSqlName.equals(newSqlName1)){
             }
             String ruleType = this.getRuleType();
             String ruleType1 = uFlyRuleHisE.getRuleType();
             if (ruleType == null) {
                if (ruleType1 != null) {
                label_009d :
                   b = false;
                }
             }else if(ruleType.equals(ruleType1)){
             }
             String operateTime = this.getOperateTime();
             String operateTime1 = uFlyRuleHisE.getOperateTime();
             if (operateTime == null) {
                if (operateTime1 != null) {
                   b = false;
                }
             }else if(operateTime.equals(operateTime1)){
             }
             String ps = this.getPs();
             String ps1 = uFlyRuleHisE.getPs();
             if (ps == null) {
                if (ps1 != null) {
                   b = false;
                }
             }else if(ps.equals(ps1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getId(){
       return this.id;
    }
    public String getNewSqlName(){
       return this.newSqlName;
    }
    public String getOperateTime(){
       return this.operateTime;
    }
    public String getPs(){
       return this.ps;
    }
    public String getRegion(){
       return this.region;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleType(){
       return this.ruleType;
    }
    public String getSqlName(){
       return this.SqlName;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $ruleName = this.getRuleName();
       int i2 = result * 59;
       i1 = ($ruleName == null)? i: $ruleName.hashCode();
       result = i2 + i1;
       String $region = this.getRegion();
       i2 = result * 59;
       i1 = ($region == null)? i: $region.hashCode();
       result = i2 + i1;
       String $SqlName = this.getSqlName();
       i2 = result * 59;
       i1 = ($SqlName == null)? i: $SqlName.hashCode();
       result = i2 + i1;
       String $newSqlName = this.getNewSqlName();
       i2 = result * 59;
       i1 = ($newSqlName == null)? i: $newSqlName.hashCode();
       result = i2 + i1;
       String ruleType = this.getRuleType();
       i2 = result * 59;
       i1 = (ruleType == null)? i: ruleType.hashCode();
       String operateTime = this.getOperateTime();
       i2 = (i2 + i1) * 59;
       i1 = (operateTime == null)? i: operateTime.hashCode();
       String ps = this.getPs();
       i1 = (i2 + i1) * 59;
       if (ps != null) {
          i = ps.hashCode();
       }
       return (i1 + i);
    }
    public void setId(String id){
       this.id = id;
    }
    public void setNewSqlName(String newSqlName){
       this.newSqlName = newSqlName;
    }
    public void setOperateTime(String operateTime){
       this.operateTime = operateTime;
    }
    public void setPs(String ps){
       this.ps = ps;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleType(String ruleType){
       this.ruleType = ruleType;
    }
    public void setSqlName(String SqlName){
       this.SqlName = SqlName;
    }
    public String toString(){
       return "FlyRuleHisEditDto\(id="+this.getId()+", ruleName="+this.getRuleName()+", region="+this.getRegion()+", SqlName="+this.getSqlName()+", newSqlName="+this.getNewSqlName()+", ruleType="+this.getRuleType()+", operateTime="+this.getOperateTime()+", ps="+this.getPs()+"\)";
    }
}
