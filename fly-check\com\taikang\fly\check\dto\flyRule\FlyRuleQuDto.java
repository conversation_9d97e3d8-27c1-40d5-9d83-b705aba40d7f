package com.taikang.fly.check.dto.flyRule.FlyRuleQuDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class FlyRuleQuDto implements Serializable	// class@000107 from classes.dex
{
    private String id;
    private String redField1;
    private String redField2;
    private String region;
    private String resultFlag;
    private String ruleType;
    private String state;
    private static final long serialVersionUID = 0x1;

    public void FlyRuleQuDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRuleQuDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof FlyRuleQuDto){
          b = false;
       }else {
          FlyRuleQuDto uFlyRuleQuDt = o;
          if (!uFlyRuleQuDt.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = uFlyRuleQuDt.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String region = this.getRegion();
             String region1 = uFlyRuleQuDt.getRegion();
             if (region == null) {
                if (region1 != null) {
                   b = false;
                }
             }else if(region.equals(region1)){
             }
             String ruleType = this.getRuleType();
             String ruleType1 = uFlyRuleQuDt.getRuleType();
             if (ruleType == null) {
                if (ruleType1 != null) {
                   b = false;
                }
             }else if(ruleType.equals(ruleType1)){
             }
             String resultFlag = this.getResultFlag();
             String resultFlag1 = uFlyRuleQuDt.getResultFlag();
             if (resultFlag == null) {
                if (resultFlag1 != null) {
                   b = false;
                }
             }else if(resultFlag.equals(resultFlag1)){
             }
             String state = this.getState();
             String state1 = uFlyRuleQuDt.getState();
             if (state == null) {
                if (state1 != null) {
                   b = false;
                }
             }else if(state.equals(state1)){
             }
             String redField1 = this.getRedField1();
             String redField11 = uFlyRuleQuDt.getRedField1();
             if (redField1 == null) {
                if (redField11 != null) {
                label_009a :
                   b = false;
                }
             }else if(redField1.equals(redField11)){
             }
             String redField2 = this.getRedField2();
             String redField21 = uFlyRuleQuDt.getRedField2();
             if (redField2 == null) {
                if (redField21 != null) {
                   b = false;
                }
             }else if(redField2.equals(redField21)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getId(){
       return this.id;
    }
    public String getRedField1(){
       return this.redField1;
    }
    public String getRedField2(){
       return this.redField2;
    }
    public String getRegion(){
       return this.region;
    }
    public String getResultFlag(){
       return this.resultFlag;
    }
    public String getRuleType(){
       return this.ruleType;
    }
    public String getState(){
       return this.state;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $region = this.getRegion();
       int i2 = result * 59;
       i1 = ($region == null)? i: $region.hashCode();
       result = i2 + i1;
       String $ruleType = this.getRuleType();
       i2 = result * 59;
       i1 = ($ruleType == null)? i: $ruleType.hashCode();
       result = i2 + i1;
       String $resultFlag = this.getResultFlag();
       i2 = result * 59;
       i1 = ($resultFlag == null)? i: $resultFlag.hashCode();
       result = i2 + i1;
       String $state = this.getState();
       i2 = result * 59;
       i1 = ($state == null)? i: $state.hashCode();
       result = i2 + i1;
       String redField1 = this.getRedField1();
       i2 = result * 59;
       i1 = (redField1 == null)? i: redField1.hashCode();
       String redField2 = this.getRedField2();
       i1 = (i2 + i1) * 59;
       if (redField2 != null) {
          i = redField2.hashCode();
       }
       return (i1 + i);
    }
    public void setId(String id){
       this.id = id;
    }
    public void setRedField1(String redField1){
       this.redField1 = redField1;
    }
    public void setRedField2(String redField2){
       this.redField2 = redField2;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setResultFlag(String resultFlag){
       this.resultFlag = resultFlag;
    }
    public void setRuleType(String ruleType){
       this.ruleType = ruleType;
    }
    public void setState(String state){
       this.state = state;
    }
    public String toString(){
       return "FlyRuleQuDto\(id="+this.getId()+", region="+this.getRegion()+", ruleType="+this.getRuleType()+", resultFlag="+this.getResultFlag()+", state="+this.getState()+", redField1="+this.getRedField1()+", redField2="+this.getRedField2()+"\)";
    }
}
