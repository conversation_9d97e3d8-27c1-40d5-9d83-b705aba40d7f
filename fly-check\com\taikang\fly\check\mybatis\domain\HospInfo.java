package com.taikang.fly.check.mybatis.domain.HospInfo;
import java.lang.Object;
import java.lang.String;
import java.util.Date;
import java.lang.StringBuilder;

public class HospInfo	// class@00024b from classes.dex
{
    private Date createdTime;
    private String creator;
    private String hospName;
    private String id;
    private String modifier;
    private Date modifyTime;
    private String oraUserName;
    private String path;
    private String region;
    private String status;
    private String tag;

    public void HospInfo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof HospInfo;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof HospInfo){
          b = false;
       }else {
          HospInfo hospInfo = o;
          if (!hospInfo.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = hospInfo.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String region = this.getRegion();
             String region1 = hospInfo.getRegion();
             if (region == null) {
                if (region1 != null) {
                   b = false;
                }
             }else if(region.equals(region1)){
             }
             String hospName = this.getHospName();
             String hospName1 = hospInfo.getHospName();
             if (hospName == null) {
                if (hospName1 != null) {
                   b = false;
                }
             }else if(hospName.equals(hospName1)){
             }
             String oraUserName = this.getOraUserName();
             String oraUserName1 = hospInfo.getOraUserName();
             if (oraUserName == null) {
                if (oraUserName1 != null) {
                   b = false;
                }
             }else if(oraUserName.equals(oraUserName1)){
             }
             String status = this.getStatus();
             String status1 = hospInfo.getStatus();
             if (status == null) {
                if (status1 != null) {
                   b = false;
                }
             }else if(status.equals(status1)){
             }
             String tag = this.getTag();
             String tag1 = hospInfo.getTag();
             if (tag == null) {
                if (tag1 != null) {
                label_00a3 :
                   b = false;
                }
             }else if(tag.equals(tag1)){
             }
             String creator = this.getCreator();
             String creator1 = hospInfo.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             Date createdTime = this.getCreatedTime();
             Date createdTime1 = hospInfo.getCreatedTime();
             if (createdTime == null) {
                if (createdTime1 != null) {
                label_00d1 :
                   b = false;
                }
             }else if(createdTime.equals(createdTime1)){
             }
             String modifier = this.getModifier();
             String modifier1 = hospInfo.getModifier();
             if (modifier == null) {
                if (modifier1 != null) {
                   b = false;
                }
             }else if(modifier.equals(modifier1)){
             }
             Date modifyTime = this.getModifyTime();
             Date modifyTime1 = hospInfo.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                label_00ff :
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String path = this.getPath();
             String path1 = hospInfo.getPath();
             if (path == null) {
                if (path1 != null) {
                   b = false;
                }
             }else if(path.equals(path1)){
             }
             b = true;
          }
       }
       return b;
    }
    public Date getCreatedTime(){
       return this.createdTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getHospName(){
       return this.hospName;
    }
    public String getId(){
       return this.id;
    }
    public String getModifier(){
       return this.modifier;
    }
    public Date getModifyTime(){
       return this.modifyTime;
    }
    public String getOraUserName(){
       return this.oraUserName;
    }
    public String getPath(){
       return this.path;
    }
    public String getRegion(){
       return this.region;
    }
    public String getStatus(){
       return this.status;
    }
    public String getTag(){
       return this.tag;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $region = this.getRegion();
       int i1 = result * 59;
       i = ($region == null)? 43: $region.hashCode();
       result = i1 + i;
       String $hospName = this.getHospName();
       i1 = result * 59;
       i = ($hospName == null)? 43: $hospName.hashCode();
       result = i1 + i;
       String $oraUserName = this.getOraUserName();
       i1 = result * 59;
       i = ($oraUserName == null)? 43: $oraUserName.hashCode();
       result = i1 + i;
       String $status = this.getStatus();
       i1 = result * 59;
       i = ($status == null)? 43: $status.hashCode();
       result = i1 + i;
       String tag = this.getTag();
       i1 = result * 59;
       i = (tag == null)? 43: tag.hashCode();
       String creator = this.getCreator();
       i1 = (i1 + i) * 59;
       i = (creator == null)? 43: creator.hashCode();
       Date createdTime = this.getCreatedTime();
       i1 = (i1 + i) * 59;
       i = (createdTime == null)? 43: createdTime.hashCode();
       String modifier = this.getModifier();
       i1 = (i1 + i) * 59;
       i = (modifier == null)? 43: modifier.hashCode();
       Date modifyTime = this.getModifyTime();
       i1 = (i1 + i) * 59;
       i = (modifyTime == null)? 43: modifyTime.hashCode();
       String path = this.getPath();
       i1 = (i1 + i) * 59;
       i = (path == null)? 43: path.hashCode();
       return (i1 + i);
    }
    public void setCreatedTime(Date createdTime){
       this.createdTime = createdTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setHospName(String hospName){
       this.hospName = hospName;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setModifier(String modifier){
       this.modifier = modifier;
    }
    public void setModifyTime(Date modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setOraUserName(String oraUserName){
       this.oraUserName = oraUserName;
    }
    public void setPath(String path){
       this.path = path;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setStatus(String status){
       this.status = status;
    }
    public void setTag(String tag){
       this.tag = tag;
    }
    public String toString(){
       return "HospInfo\(id="+this.getId()+", region="+this.getRegion()+", hospName="+this.getHospName()+", oraUserName="+this.getOraUserName()+", status="+this.getStatus()+", tag="+this.getTag()+", creator="+this.getCreator()+", createdTime="+this.getCreatedTime()+", modifier="+this.getModifier()+", modifyTime="+this.getModifyTime()+", path="+this.getPath()+"\)";
    }
}
