package com.taikang.fly.check.dto.mapstruct.PlanMappingImpl;
import com.taikang.fly.check.dto.mapstruct.PlanMapping;
import java.lang.Object;
import com.taikang.fly.check.dto.plan.PlanAddDto;
import com.taikang.fly.check.mybatis.domain.Plan;
import java.lang.String;
import com.taikang.fly.check.security.core.DomainContext;
import com.taikang.fly.check.security.core.ThreadLocalContextHolder;
import com.taikang.fly.check.dto.user.UserDto;
import java.time.LocalDateTime;
import com.taikang.fly.check.utils.SequenceGenerator;

public class PlanMappingImpl implements PlanMapping	// class@000174 from classes.dex
{

    public void PlanMappingImpl(){
       super();
    }
    public Plan dtoToEntry(PlanAddDto planAddDto){
       Plan plan;
       if (planAddDto == null) {
          plan = null;
       }else {
          plan = new Plan();
          plan.setPlanName(planAddDto.getPlanName());
          plan.setCreator(ThreadLocalContextHolder.getContext().getUserInfo().getUserCode());
          plan.setModifyTime(LocalDateTime.now());
          plan.setCreateTime(LocalDateTime.now());
          plan.setMissionStatus("1");
          plan.setModby(ThreadLocalContextHolder.getContext().getUserInfo().getUserCode());
          plan.setId(SequenceGenerator.getId());
          plan.setStatus("1");
       }
       return plan;
    }
}
