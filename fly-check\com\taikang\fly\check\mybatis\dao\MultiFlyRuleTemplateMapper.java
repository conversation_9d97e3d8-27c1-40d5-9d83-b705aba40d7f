package com.taikang.fly.check.mybatis.dao.MultiFlyRuleTemplateMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.Map;
import java.util.List;
import java.lang.String;
import java.lang.Integer;
import com.taikang.fly.check.mybatis.domain.MultiFlyRuleTemplate;
import java.lang.Object;

public interface abstract MultiFlyRuleTemplateMapper implements BaseMapper	// class@000212 from classes.dex
{

    List findFlyRuleTemplatePage(Map p0);
    Integer getNumber(String p0);
    List getTemplateNameList();
    int insert(MultiFlyRuleTemplate p0);
    default int insert(Object p0){
       return this.insert(p0);
    }
    List selectAll();
    MultiFlyRuleTemplate selectById(String p0);
    List selectRuleName();
    List templateNameList(String p0);
    Integer updateByPrimaryKey(MultiFlyRuleTemplate p0);
    Integer updateByPrimaryKeySelective(MultiFlyRuleTemplate p0);
}
