package com.taikang.fly.check.dto.csventry.CSVHeaderEntry;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import java.lang.StringBuilder;

public class CSVHeaderEntry	// class@0000d3 from classes.dex
{
    private String dateFormat;
    private Integer fieldAccurary;
    private Integer fieldChecked;
    private Integer fieldLength;
    private String fieldName;
    private String fieldType;
    private Integer oldIndex;
    private List previewFile;
    private String remark;

    public void CSVHeaderEntry(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof CSVHeaderEntry;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof CSVHeaderEntry){
          b = false;
       }else {
          CSVHeaderEntry uCSVHeaderEn = o;
          if (!uCSVHeaderEn.canEqual(this)) {
             b = false;
          }else {
             String fieldName = this.getFieldName();
             String fieldName1 = uCSVHeaderEn.getFieldName();
             if (fieldName == null) {
                if (fieldName1 != null) {
                   b = false;
                }
             }else if(fieldName.equals(fieldName1)){
             }
             String fieldType = this.getFieldType();
             String fieldType1 = uCSVHeaderEn.getFieldType();
             if (fieldType == null) {
                if (fieldType1 != null) {
                   b = false;
                }
             }else if(fieldType.equals(fieldType1)){
             }
             Integer fieldLength = this.getFieldLength();
             Integer fieldLength1 = uCSVHeaderEn.getFieldLength();
             if (fieldLength == null) {
                if (fieldLength1 != null) {
                   b = false;
                }
             }else if(fieldLength.equals(fieldLength1)){
             }
             Integer fieldAccurar = this.getFieldAccurary();
             Integer fieldAccurar1 = uCSVHeaderEn.getFieldAccurary();
             if (fieldAccurar == null) {
                if (fieldAccurar1 != null) {
                   b = false;
                }
             }else if(fieldAccurar.equals(fieldAccurar1)){
             }
             String dateFormat = this.getDateFormat();
             String dateFormat1 = uCSVHeaderEn.getDateFormat();
             if (dateFormat == null) {
                if (dateFormat1 != null) {
                   b = false;
                }
             }else if(dateFormat.equals(dateFormat1)){
             }
             String remark = this.getRemark();
             String remark1 = uCSVHeaderEn.getRemark();
             if (remark == null) {
                if (remark1 != null) {
                   b = false;
                }
             }else if(remark.equals(remark1)){
             }
             Integer fieldChecked = this.getFieldChecked();
             Integer fieldChecked1 = uCSVHeaderEn.getFieldChecked();
             if (fieldChecked == null) {
                if (fieldChecked1 != null) {
                label_00b5 :
                   b = false;
                }
             }else if(fieldChecked.equals(fieldChecked1)){
             }
             List previewFile = this.getPreviewFile();
             List previewFile1 = uCSVHeaderEn.getPreviewFile();
             if (previewFile == null) {
                if (previewFile1 != null) {
                   b = false;
                }
             }else if(previewFile.equals(previewFile1)){
             }
             Integer oldIndex = this.getOldIndex();
             Integer oldIndex1 = uCSVHeaderEn.getOldIndex();
             if (oldIndex == null) {
                if (oldIndex1 != null) {
                label_00e3 :
                   b = false;
                }
             }else if(oldIndex.equals(oldIndex1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getDateFormat(){
       return this.dateFormat;
    }
    public Integer getFieldAccurary(){
       return this.fieldAccurary;
    }
    public Integer getFieldChecked(){
       return this.fieldChecked;
    }
    public Integer getFieldLength(){
       return this.fieldLength;
    }
    public String getFieldName(){
       return this.fieldName;
    }
    public String getFieldType(){
       return this.fieldType;
    }
    public Integer getOldIndex(){
       return this.oldIndex;
    }
    public List getPreviewFile(){
       return this.previewFile;
    }
    public String getRemark(){
       return this.remark;
    }
    public int hashCode(){
       String $fieldName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($fieldName = this.getFieldName()) == null)? i: $fieldName.hashCode();
       result = i1 + 59;
       String $fieldType = this.getFieldType();
       int i2 = result * 59;
       i1 = ($fieldType == null)? i: $fieldType.hashCode();
       result = i2 + i1;
       Integer $fieldLength = this.getFieldLength();
       i2 = result * 59;
       i1 = ($fieldLength == null)? i: $fieldLength.hashCode();
       result = i2 + i1;
       Integer $fieldAccurary = this.getFieldAccurary();
       i2 = result * 59;
       i1 = ($fieldAccurary == null)? i: $fieldAccurary.hashCode();
       result = i2 + i1;
       String $dateFormat = this.getDateFormat();
       i2 = result * 59;
       i1 = ($dateFormat == null)? i: $dateFormat.hashCode();
       result = i2 + i1;
       String remark = this.getRemark();
       i2 = result * 59;
       i1 = (remark == null)? i: remark.hashCode();
       Integer fieldChecked = this.getFieldChecked();
       i2 = (i2 + i1) * 59;
       i1 = (fieldChecked == null)? i: fieldChecked.hashCode();
       List previewFile = this.getPreviewFile();
       i2 = (i2 + i1) * 59;
       i1 = (previewFile == null)? i: previewFile.hashCode();
       Integer oldIndex = this.getOldIndex();
       i1 = (i2 + i1) * 59;
       if (oldIndex != null) {
          i = oldIndex.hashCode();
       }
       return (i1 + i);
    }
    public void setDateFormat(String dateFormat){
       this.dateFormat = dateFormat;
    }
    public void setFieldAccurary(Integer fieldAccurary){
       this.fieldAccurary = fieldAccurary;
    }
    public void setFieldChecked(Integer fieldChecked){
       this.fieldChecked = fieldChecked;
    }
    public void setFieldLength(Integer fieldLength){
       this.fieldLength = fieldLength;
    }
    public void setFieldName(String fieldName){
       this.fieldName = fieldName;
    }
    public void setFieldType(String fieldType){
       this.fieldType = fieldType;
    }
    public void setOldIndex(Integer oldIndex){
       this.oldIndex = oldIndex;
    }
    public void setPreviewFile(List previewFile){
       this.previewFile = previewFile;
    }
    public void setRemark(String remark){
       this.remark = remark;
    }
    public String toString(){
       return "CSVHeaderEntry\(fieldName="+this.getFieldName()+", fieldType="+this.getFieldType()+", fieldLength="+this.getFieldLength()+", fieldAccurary="+this.getFieldAccurary()+", dateFormat="+this.getDateFormat()+", remark="+this.getRemark()+", fieldChecked="+this.getFieldChecked()+", previewFile="+this.getPreviewFile()+", oldIndex="+this.getOldIndex()+"\)";
    }
}
