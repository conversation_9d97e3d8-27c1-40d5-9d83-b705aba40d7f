package com.taikang.fly.check.mybatis.dao.PlanLogMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import java.util.Map;
import java.util.List;
import com.taikang.fly.check.mybatis.domain.PlanLog;

public interface abstract PlanLogMapper implements BaseMapper	// class@000215 from classes.dex
{

    int deleteByPrimaryKey(String p0);
    List findPlanLogList(Map p0);
    PlanLog selectByIdAndStatus(String p0);
    int updateByPrimaryKey(PlanLog p0);
}
