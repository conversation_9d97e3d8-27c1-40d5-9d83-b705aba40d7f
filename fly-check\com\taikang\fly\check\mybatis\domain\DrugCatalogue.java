package com.taikang.fly.check.mybatis.domain.DrugCatalogue;
import java.lang.Object;
import java.lang.String;
import java.math.BigDecimal;
import java.util.Date;
import java.lang.StringBuilder;

public class DrugCatalogue	// class@00023f from classes.dex
{
    private String dosageForm;
    private String drugsCode;
    private String drugsName;
    private Date endTime;
    private BigDecimal hospitalSelfPay;
    private BigDecimal leavePrice;
    private BigDecimal maxPrice;
    private BigDecimal oneMaxPrice;
    private BigDecimal outpatientSelfPay;
    private String payer;
    private String paymentCategory;
    private String remark;
    private Date startTime;
    private BigDecimal threeMaxPrice;
    private BigDecimal twoMaxPrice;
    private BigDecimal workInjurySelfPay;
    private BigDecimal yourSelfPay;

    public void DrugCatalogue(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrugCatalogue;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof DrugCatalogue){
          b = false;
       }else {
          DrugCatalogue uDrugCatalog = o;
          if (!uDrugCatalog.canEqual(this)) {
             b = false;
          }else {
             String drugsCode = this.getDrugsCode();
             String drugsCode1 = uDrugCatalog.getDrugsCode();
             if (drugsCode == null) {
                if (drugsCode1 != null) {
                   b = false;
                }
             }else if(drugsCode.equals(drugsCode1)){
             }
             String drugsName = this.getDrugsName();
             String drugsName1 = uDrugCatalog.getDrugsName();
             if (drugsName == null) {
                if (drugsName1 != null) {
                   b = false;
                }
             }else if(drugsName.equals(drugsName1)){
             }
             String dosageForm = this.getDosageForm();
             String dosageForm1 = uDrugCatalog.getDosageForm();
             if (dosageForm == null) {
                if (dosageForm1 != null) {
                   b = false;
                }
             }else if(dosageForm.equals(dosageForm1)){
             }
             String payer = this.getPayer();
             String payer1 = uDrugCatalog.getPayer();
             if (payer == null) {
                if (payer1 != null) {
                   b = false;
                }
             }else if(payer.equals(payer1)){
             }
             String paymentCateg = this.getPaymentCategory();
             String paymentCateg1 = uDrugCatalog.getPaymentCategory();
             if (paymentCateg == null) {
                if (paymentCateg1 != null) {
                   b = false;
                }
             }else if(paymentCateg.equals(paymentCateg1)){
             }
             String remark = this.getRemark();
             String remark1 = uDrugCatalog.getRemark();
             if (remark == null) {
                if (remark1 != null) {
                label_00a3 :
                   b = false;
                }
             }else if(remark.equals(remark1)){
             }
             BigDecimal maxPrice = this.getMaxPrice();
             BigDecimal maxPrice1 = uDrugCatalog.getMaxPrice();
             if (maxPrice == null) {
                if (maxPrice1 != null) {
                   b = false;
                }
             }else if(maxPrice.equals(maxPrice1)){
             }
             BigDecimal threeMaxPric = this.getThreeMaxPrice();
             BigDecimal threeMaxPric1 = uDrugCatalog.getThreeMaxPrice();
             if (threeMaxPric == null) {
                if (threeMaxPric1 != null) {
                   b = false;
                }
             }else if(threeMaxPric.equals(threeMaxPric1)){
             }
             BigDecimal twoMaxPrice = this.getTwoMaxPrice();
             BigDecimal twoMaxPrice1 = uDrugCatalog.getTwoMaxPrice();
             if (twoMaxPrice == null) {
                if (twoMaxPrice1 != null) {
                   b = false;
                }
             }else if(twoMaxPrice.equals(twoMaxPrice1)){
             }
             BigDecimal oneMaxPrice = this.getOneMaxPrice();
             BigDecimal oneMaxPrice1 = uDrugCatalog.getOneMaxPrice();
             if (oneMaxPrice == null) {
                if (oneMaxPrice1 != null) {
                   b = false;
                }
             }else if(oneMaxPrice.equals(oneMaxPrice1)){
             }
             BigDecimal leavePrice = this.getLeavePrice();
             BigDecimal leavePrice1 = uDrugCatalog.getLeavePrice();
             if (leavePrice == null) {
                if (leavePrice1 != null) {
                   b = false;
                }
             }else if(leavePrice.equals(leavePrice1)){
             }
             BigDecimal outpatientSe = this.getOutpatientSelfPay();
             BigDecimal outpatientSe1 = uDrugCatalog.getOutpatientSelfPay();
             if (outpatientSe == null) {
                if (outpatientSe1 != null) {
                label_0137 :
                   b = false;
                }
             }else if(outpatientSe.equals(outpatientSe1)){
             }
             BigDecimal hospitalSelf = this.getHospitalSelfPay();
             BigDecimal hospitalSelf1 = uDrugCatalog.getHospitalSelfPay();
             if (hospitalSelf == null) {
                if (hospitalSelf1 != null) {
                   b = false;
                }
             }else if(hospitalSelf.equals(hospitalSelf1)){
             }
             BigDecimal workInjurySe = this.getWorkInjurySelfPay();
             BigDecimal workInjurySe1 = uDrugCatalog.getWorkInjurySelfPay();
             if (workInjurySe == null) {
                if (workInjurySe1 != null) {
                   b = false;
                }
             }else if(workInjurySe.equals(workInjurySe1)){
             }
             BigDecimal yourSelfPay = this.getYourSelfPay();
             BigDecimal yourSelfPay1 = uDrugCatalog.getYourSelfPay();
             if (yourSelfPay == null) {
                if (yourSelfPay1 != null) {
                   b = false;
                }
             }else if(yourSelfPay.equals(yourSelfPay1)){
             }
             Date startTime = this.getStartTime();
             Date startTime1 = uDrugCatalog.getStartTime();
             if (startTime == null) {
                if (startTime1 != null) {
                label_019b :
                   b = false;
                }
             }else if(startTime.equals(startTime1)){
             }
             Date endTime = this.getEndTime();
             Date endTime1 = uDrugCatalog.getEndTime();
             if (endTime == null) {
                if (endTime1 != null) {
                   b = false;
                }
             }else if(endTime.equals(endTime1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getDosageForm(){
       return this.dosageForm;
    }
    public String getDrugsCode(){
       return this.drugsCode;
    }
    public String getDrugsName(){
       return this.drugsName;
    }
    public Date getEndTime(){
       return this.endTime;
    }
    public BigDecimal getHospitalSelfPay(){
       return this.hospitalSelfPay;
    }
    public BigDecimal getLeavePrice(){
       return this.leavePrice;
    }
    public BigDecimal getMaxPrice(){
       return this.maxPrice;
    }
    public BigDecimal getOneMaxPrice(){
       return this.oneMaxPrice;
    }
    public BigDecimal getOutpatientSelfPay(){
       return this.outpatientSelfPay;
    }
    public String getPayer(){
       return this.payer;
    }
    public String getPaymentCategory(){
       return this.paymentCategory;
    }
    public String getRemark(){
       return this.remark;
    }
    public Date getStartTime(){
       return this.startTime;
    }
    public BigDecimal getThreeMaxPrice(){
       return this.threeMaxPrice;
    }
    public BigDecimal getTwoMaxPrice(){
       return this.twoMaxPrice;
    }
    public BigDecimal getWorkInjurySelfPay(){
       return this.workInjurySelfPay;
    }
    public BigDecimal getYourSelfPay(){
       return this.yourSelfPay;
    }
    public int hashCode(){
       String $drugsCode;
       int PRIME = 59;
       int result = 1;
       int i = (($drugsCode = this.getDrugsCode()) == null)? 43: $drugsCode.hashCode();
       result = i + 59;
       String $drugsName = this.getDrugsName();
       int i1 = result * 59;
       i = ($drugsName == null)? 43: $drugsName.hashCode();
       result = i1 + i;
       String $dosageForm = this.getDosageForm();
       i1 = result * 59;
       i = ($dosageForm == null)? 43: $dosageForm.hashCode();
       result = i1 + i;
       String $payer = this.getPayer();
       i1 = result * 59;
       i = ($payer == null)? 43: $payer.hashCode();
       result = i1 + i;
       String $paymentCategory = this.getPaymentCategory();
       i1 = result * 59;
       i = ($paymentCategory == null)? 43: $paymentCategory.hashCode();
       result = i1 + i;
       String remark = this.getRemark();
       i1 = result * 59;
       i = (remark == null)? 43: remark.hashCode();
       BigDecimal maxPrice = this.getMaxPrice();
       i1 = (i1 + i) * 59;
       i = (maxPrice == null)? 43: maxPrice.hashCode();
       BigDecimal threeMaxPric = this.getThreeMaxPrice();
       i1 = (i1 + i) * 59;
       i = (threeMaxPric == null)? 43: threeMaxPric.hashCode();
       BigDecimal twoMaxPrice = this.getTwoMaxPrice();
       i1 = (i1 + i) * 59;
       i = (twoMaxPrice == null)? 43: twoMaxPrice.hashCode();
       BigDecimal oneMaxPrice = this.getOneMaxPrice();
       i1 = (i1 + i) * 59;
       i = (oneMaxPrice == null)? 43: oneMaxPrice.hashCode();
       BigDecimal leavePrice = this.getLeavePrice();
       i1 = (i1 + i) * 59;
       i = (leavePrice == null)? 43: leavePrice.hashCode();
       BigDecimal outpatientSe = this.getOutpatientSelfPay();
       i1 = (i1 + i) * 59;
       i = (outpatientSe == null)? 43: outpatientSe.hashCode();
       BigDecimal hospitalSelf = this.getHospitalSelfPay();
       i1 = (i1 + i) * 59;
       i = (hospitalSelf == null)? 43: hospitalSelf.hashCode();
       BigDecimal workInjurySe = this.getWorkInjurySelfPay();
       i1 = (i1 + i) * 59;
       i = (workInjurySe == null)? 43: workInjurySe.hashCode();
       BigDecimal yourSelfPay = this.getYourSelfPay();
       i1 = (i1 + i) * 59;
       i = (yourSelfPay == null)? 43: yourSelfPay.hashCode();
       Date startTime = this.getStartTime();
       i1 = (i1 + i) * 59;
       i = (startTime == null)? 43: startTime.hashCode();
       Date endTime = this.getEndTime();
       i1 = (i1 + i) * 59;
       i = (endTime == null)? 43: endTime.hashCode();
       return (i1 + i);
    }
    public void setDosageForm(String dosageForm){
       this.dosageForm = dosageForm;
    }
    public void setDrugsCode(String drugsCode){
       this.drugsCode = drugsCode;
    }
    public void setDrugsName(String drugsName){
       this.drugsName = drugsName;
    }
    public void setEndTime(Date endTime){
       this.endTime = endTime;
    }
    public void setHospitalSelfPay(BigDecimal hospitalSelfPay){
       this.hospitalSelfPay = hospitalSelfPay;
    }
    public void setLeavePrice(BigDecimal leavePrice){
       this.leavePrice = leavePrice;
    }
    public void setMaxPrice(BigDecimal maxPrice){
       this.maxPrice = maxPrice;
    }
    public void setOneMaxPrice(BigDecimal oneMaxPrice){
       this.oneMaxPrice = oneMaxPrice;
    }
    public void setOutpatientSelfPay(BigDecimal outpatientSelfPay){
       this.outpatientSelfPay = outpatientSelfPay;
    }
    public void setPayer(String payer){
       this.payer = payer;
    }
    public void setPaymentCategory(String paymentCategory){
       this.paymentCategory = paymentCategory;
    }
    public void setRemark(String remark){
       this.remark = remark;
    }
    public void setStartTime(Date startTime){
       this.startTime = startTime;
    }
    public void setThreeMaxPrice(BigDecimal threeMaxPrice){
       this.threeMaxPrice = threeMaxPrice;
    }
    public void setTwoMaxPrice(BigDecimal twoMaxPrice){
       this.twoMaxPrice = twoMaxPrice;
    }
    public void setWorkInjurySelfPay(BigDecimal workInjurySelfPay){
       this.workInjurySelfPay = workInjurySelfPay;
    }
    public void setYourSelfPay(BigDecimal yourSelfPay){
       this.yourSelfPay = yourSelfPay;
    }
    public String toString(){
       return "DrugCatalogue\(drugsCode="+this.getDrugsCode()+", drugsName="+this.getDrugsName()+", dosageForm="+this.getDosageForm()+", payer="+this.getPayer()+", paymentCategory="+this.getPaymentCategory()+", remark="+this.getRemark()+", maxPrice="+this.getMaxPrice()+", threeMaxPrice="+this.getThreeMaxPrice()+", twoMaxPrice="+this.getTwoMaxPrice()+", oneMaxPrice="+this.getOneMaxPrice()+", leavePrice="+this.getLeavePrice()+", outpatientSelfPay="+this.getOutpatientSelfPay()+", hospitalSelfPay="+this.getHospitalSelfPay()+", workInjurySelfPay="+this.getWorkInjurySelfPay()+", yourSelfPay="+this.getYourSelfPay()+", startTime="+this.getStartTime()+", endTime="+this.getEndTime()+"\)";
    }
}
