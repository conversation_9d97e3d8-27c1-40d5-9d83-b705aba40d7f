package com.taikang.fly.check.dto.flyRule.FlyRuleHosCountDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.Long;
import java.util.List;
import java.lang.String;
import java.lang.StringBuilder;

public class FlyRuleHosCountDto implements Serializable	// class@0000ff from classes.dex
{
    private Long count;
    private List hospName;
    private static final long serialVersionUID = 0x1;

    public void FlyRuleHosCountDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRuleHosCountDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof FlyRuleHosCountDto) {
             b = false;
          }else {
             FlyRuleHosCountDto uFlyRuleHosC = o;
             if (!uFlyRuleHosC.canEqual(this)) {
                b = false;
             }else {
                Long count = this.getCount();
                Long count1 = uFlyRuleHosC.getCount();
                if (count == null) {
                   if (count1 != null) {
                      b = false;
                   }
                }else if(count.equals(count1)){
                }
                List hospName = this.getHospName();
                List hospName1 = uFlyRuleHosC.getHospName();
                if (hospName == null) {
                   if (hospName1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!hospName.equals(hospName1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public Long getCount(){
       return this.count;
    }
    public List getHospName(){
       return this.hospName;
    }
    public int hashCode(){
       Long $count;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($count = this.getCount()) == null)? i: $count.hashCode();
       result = i1 + 59;
       List $hospName = this.getHospName();
       i1 = result * 59;
       if ($hospName != null) {
          i = $hospName.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setCount(Long count){
       this.count = count;
    }
    public void setHospName(List hospName){
       this.hospName = hospName;
    }
    public String toString(){
       return "FlyRuleHosCountDto\(count="+this.getCount()+", hospName="+this.getHospName()+"\)";
    }
}
