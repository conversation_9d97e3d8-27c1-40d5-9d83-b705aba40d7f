package com.taikang.fly.check.dto.dictEntry.DictEntryDetailParamDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DictEntryDetailParamDto implements Serializable	// class@0000e4 from classes.dex
{
    private String dictCode;
    private String dictName;
    private String dictTypeCode;
    private String dictTypeName;
    private static final long serialVersionUID = 0x349d06aa3e3fa7a1;

    public void DictEntryDetailParamDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DictEntryDetailParamDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DictEntryDetailParamDto) {
             b = false;
          }else {
             DictEntryDetailParamDto uDictEntryDe = o;
             if (!uDictEntryDe.canEqual(this)) {
                b = false;
             }else {
                String dictTypeCode = this.getDictTypeCode();
                String dictTypeCode1 = uDictEntryDe.getDictTypeCode();
                if (dictTypeCode == null) {
                   if (dictTypeCode1 != null) {
                      b = false;
                   }
                }else if(dictTypeCode.equals(dictTypeCode1)){
                }
                String dictTypeName = this.getDictTypeName();
                String dictTypeName1 = uDictEntryDe.getDictTypeName();
                if (dictTypeName == null) {
                   if (dictTypeName1 != null) {
                      b = false;
                   }
                }else if(dictTypeName.equals(dictTypeName1)){
                }
                String dictCode = this.getDictCode();
                String dictCode1 = uDictEntryDe.getDictCode();
                if (dictCode == null) {
                   if (dictCode1 != null) {
                      b = false;
                   }
                }else if(dictCode.equals(dictCode1)){
                }
                String dictName = this.getDictName();
                String dictName1 = uDictEntryDe.getDictName();
                if (dictName == null) {
                   if (dictName1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!dictName.equals(dictName1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getDictCode(){
       return this.dictCode;
    }
    public String getDictName(){
       return this.dictName;
    }
    public String getDictTypeCode(){
       return this.dictTypeCode;
    }
    public String getDictTypeName(){
       return this.dictTypeName;
    }
    public int hashCode(){
       String $dictTypeCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($dictTypeCode = this.getDictTypeCode()) == null)? i: $dictTypeCode.hashCode();
       result = i1 + 59;
       String $dictTypeName = this.getDictTypeName();
       int i2 = result * 59;
       i1 = ($dictTypeName == null)? i: $dictTypeName.hashCode();
       result = i2 + i1;
       String $dictCode = this.getDictCode();
       i2 = result * 59;
       i1 = ($dictCode == null)? i: $dictCode.hashCode();
       result = i2 + i1;
       String $dictName = this.getDictName();
       i1 = result * 59;
       if ($dictName != null) {
          i = $dictName.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDictCode(String dictCode){
       this.dictCode = dictCode;
    }
    public void setDictName(String dictName){
       this.dictName = dictName;
    }
    public void setDictTypeCode(String dictTypeCode){
       this.dictTypeCode = dictTypeCode;
    }
    public void setDictTypeName(String dictTypeName){
       this.dictTypeName = dictTypeName;
    }
    public String toString(){
       return "DictEntryDetailParamDto\(dictTypeCode="+this.getDictTypeCode()+", dictTypeName="+this.getDictTypeName()+", dictCode="+this.getDictCode()+", dictName="+this.getDictName()+"\)";
    }
}
