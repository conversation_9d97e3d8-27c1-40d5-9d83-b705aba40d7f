package com.taikang.fly.check.mybatis.domain.DictEntry;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import java.util.Date;
import java.lang.StringBuilder;

public class DictEntry	// class@00023c from classes.dex
{
    private Date createTime;
    private String creator;
    private String dictCode;
    private String dictName;
    private String dictNec;
    private String dictType;
    private String dictTypeCode;
    private String id;
    private String modby;
    private Date modifyTime;
    private String signature;
    private Integer sortNo;

    public void DictEntry(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DictEntry;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof DictEntry){
          b = false;
       }else {
          DictEntry uDictEntry = o;
          if (!uDictEntry.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = uDictEntry.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String dictTypeCode = this.getDictTypeCode();
             String dictTypeCode1 = uDictEntry.getDictTypeCode();
             if (dictTypeCode == null) {
                if (dictTypeCode1 != null) {
                   b = false;
                }
             }else if(dictTypeCode.equals(dictTypeCode1)){
             }
             String dictCode = this.getDictCode();
             String dictCode1 = uDictEntry.getDictCode();
             if (dictCode == null) {
                if (dictCode1 != null) {
                   b = false;
                }
             }else if(dictCode.equals(dictCode1)){
             }
             String dictName = this.getDictName();
             String dictName1 = uDictEntry.getDictName();
             if (dictName == null) {
                if (dictName1 != null) {
                   b = false;
                }
             }else if(dictName.equals(dictName1)){
             }
             Integer sortNo = this.getSortNo();
             Integer sortNo1 = uDictEntry.getSortNo();
             if (sortNo == null) {
                if (sortNo1 != null) {
                   b = false;
                }
             }else if(sortNo.equals(sortNo1)){
             }
             String creator = this.getCreator();
             String creator1 = uDictEntry.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                label_00a3 :
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             Date createTime = this.getCreateTime();
             Date createTime1 = uDictEntry.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modby = this.getModby();
             String modby1 = uDictEntry.getModby();
             if (modby == null) {
                if (modby1 != null) {
                label_00d1 :
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             Date modifyTime = this.getModifyTime();
             Date modifyTime1 = uDictEntry.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String signature = this.getSignature();
             String signature1 = uDictEntry.getSignature();
             if (signature == null) {
                if (signature1 != null) {
                label_0101 :
                   b = false;
                }
             }else if(signature.equals(signature1)){
             }
             String dictType = this.getDictType();
             String dictType1 = uDictEntry.getDictType();
             if (dictType == null) {
                if (dictType1 != null) {
                   b = false;
                }
             }else if(dictType.equals(dictType1)){
             }
             String dictNec = this.getDictNec();
             String dictNec1 = uDictEntry.getDictNec();
             if (dictNec == null) {
                if (dictNec1 != null) {
                label_0131 :
                   b = false;
                }
             }else if(dictNec.equals(dictNec1)){
             }
             b = true;
          }
       }
       return b;
    }
    public Date getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getDictCode(){
       return this.dictCode;
    }
    public String getDictName(){
       return this.dictName;
    }
    public String getDictNec(){
       return this.dictNec;
    }
    public String getDictType(){
       return this.dictType;
    }
    public String getDictTypeCode(){
       return this.dictTypeCode;
    }
    public String getId(){
       return this.id;
    }
    public String getModby(){
       return this.modby;
    }
    public Date getModifyTime(){
       return this.modifyTime;
    }
    public String getSignature(){
       return this.signature;
    }
    public Integer getSortNo(){
       return this.sortNo;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $dictTypeCode = this.getDictTypeCode();
       int i1 = result * 59;
       i = ($dictTypeCode == null)? 43: $dictTypeCode.hashCode();
       result = i1 + i;
       String $dictCode = this.getDictCode();
       i1 = result * 59;
       i = ($dictCode == null)? 43: $dictCode.hashCode();
       result = i1 + i;
       String $dictName = this.getDictName();
       i1 = result * 59;
       i = ($dictName == null)? 43: $dictName.hashCode();
       result = i1 + i;
       Integer $sortNo = this.getSortNo();
       i1 = result * 59;
       i = ($sortNo == null)? 43: $sortNo.hashCode();
       result = i1 + i;
       String creator = this.getCreator();
       i1 = result * 59;
       i = (creator == null)? 43: creator.hashCode();
       Date createTime = this.getCreateTime();
       i1 = (i1 + i) * 59;
       i = (createTime == null)? 43: createTime.hashCode();
       String modby = this.getModby();
       i1 = (i1 + i) * 59;
       i = (modby == null)? 43: modby.hashCode();
       Date modifyTime = this.getModifyTime();
       i1 = (i1 + i) * 59;
       i = (modifyTime == null)? 43: modifyTime.hashCode();
       String signature = this.getSignature();
       i1 = (i1 + i) * 59;
       i = (signature == null)? 43: signature.hashCode();
       String dictType = this.getDictType();
       i1 = (i1 + i) * 59;
       i = (dictType == null)? 43: dictType.hashCode();
       String dictNec = this.getDictNec();
       i1 = (i1 + i) * 59;
       i = (dictNec == null)? 43: dictNec.hashCode();
       return (i1 + i);
    }
    public void setCreateTime(Date createTime){
       this.createTime = createTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setDictCode(String dictCode){
       this.dictCode = dictCode;
    }
    public void setDictName(String dictName){
       this.dictName = dictName;
    }
    public void setDictNec(String dictNec){
       this.dictNec = dictNec;
    }
    public void setDictType(String dictType){
       this.dictType = dictType;
    }
    public void setDictTypeCode(String dictTypeCode){
       this.dictTypeCode = dictTypeCode;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setModby(String modby){
       this.modby = modby;
    }
    public void setModifyTime(Date modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setSignature(String signature){
       this.signature = signature;
    }
    public void setSortNo(Integer sortNo){
       this.sortNo = sortNo;
    }
    public String toString(){
       return "DictEntry\(id="+this.getId()+", dictTypeCode="+this.getDictTypeCode()+", dictCode="+this.getDictCode()+", dictName="+this.getDictName()+", sortNo="+this.getSortNo()+", creator="+this.getCreator()+", createTime="+this.getCreateTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+", signature="+this.getSignature()+", dictType="+this.getDictType()+", dictNec="+this.getDictNec()+"\)";
    }
}
