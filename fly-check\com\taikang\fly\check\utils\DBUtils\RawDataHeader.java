package com.taikang.fly.check.utils.DBUtils.RawDataHeader;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import java.lang.StringBuilder;

public class RawDataHeader	// class@000337 from classes.dex
{
    private Integer index;
    private String name;

    public void RawDataHeader(){
       super();
    }
    public void RawDataHeader(String name,Integer index){
       super();
       this.name = name;
       this.index = index;
    }
    protected boolean canEqual(Object other){
       return other instanceof RawDataHeader;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof RawDataHeader) {
             b = false;
          }else {
             RawDataHeader rawDataHeade = o;
             if (!rawDataHeade.canEqual(this)) {
                b = false;
             }else {
                String name = this.getName();
                String name1 = rawDataHeade.getName();
                if (name == null) {
                   if (name1 != null) {
                      b = false;
                   }
                }else if(name.equals(name1)){
                }
                Integer index = this.getIndex();
                Integer index1 = rawDataHeade.getIndex();
                if (index == null) {
                   if (index1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!index.equals(index1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public Integer getIndex(){
       return this.index;
    }
    public String getName(){
       return this.name;
    }
    public int hashCode(){
       String $name;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($name = this.getName()) == null)? i: $name.hashCode();
       result = i1 + 59;
       Integer $index = this.getIndex();
       i1 = result * 59;
       if ($index != null) {
          i = $index.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setIndex(Integer index){
       this.index = index;
    }
    public void setName(String name){
       this.name = name;
    }
    public String toString(){
       return "RawDataHeader\(name="+this.getName()+", index="+this.getIndex()+"\)";
    }
}
