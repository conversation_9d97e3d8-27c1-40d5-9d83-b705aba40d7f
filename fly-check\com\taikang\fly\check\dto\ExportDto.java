package com.taikang.fly.check.dto.ExportDto;
import java.io.Serializable;
import com.taikang.fly.check.dto.flyRule.FlyRuleSearchDto;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ExportDto extends FlyRuleSearchDto implements Serializable	// class@00009f from classes.dex
{
    private String flag;
    private String id;
    private String type;
    private String year;
    private static final long serialVersionUID = 0x7dede0ec71772e8;

    public void ExportDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ExportDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof ExportDto) {
             b = false;
          }else {
             ExportDto uExportDto = o;
             if (!uExportDto.canEqual(this)) {
                b = false;
             }else {
                String id = this.getId();
                String id1 = uExportDto.getId();
                if (id == null) {
                   if (id1 != null) {
                      b = false;
                   }
                }else if(id.equals(id1)){
                }
                String flag = this.getFlag();
                String flag1 = uExportDto.getFlag();
                if (flag == null) {
                   if (flag1 != null) {
                      b = false;
                   }
                }else if(flag.equals(flag1)){
                }
                String year = this.getYear();
                String year1 = uExportDto.getYear();
                if (year == null) {
                   if (year1 != null) {
                      b = false;
                   }
                }else if(year.equals(year1)){
                }
                String type = this.getType();
                String type1 = uExportDto.getType();
                if (type == null) {
                   if (type1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!type.equals(type1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getFlag(){
       return this.flag;
    }
    public String getId(){
       return this.id;
    }
    public String getType(){
       return this.type;
    }
    public String getYear(){
       return this.year;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $flag = this.getFlag();
       int i2 = result * 59;
       i1 = ($flag == null)? i: $flag.hashCode();
       result = i2 + i1;
       String $year = this.getYear();
       i2 = result * 59;
       i1 = ($year == null)? i: $year.hashCode();
       result = i2 + i1;
       String $type = this.getType();
       i1 = result * 59;
       if ($type != null) {
          i = $type.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setFlag(String flag){
       this.flag = flag;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setType(String type){
       this.type = type;
    }
    public void setYear(String year){
       this.year = year;
    }
    public String toString(){
       return "ExportDto\(id="+this.getId()+", flag="+this.getFlag()+", year="+this.getYear()+", type="+this.getType()+"\)";
    }
}
