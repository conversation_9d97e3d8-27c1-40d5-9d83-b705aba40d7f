package com.taikang.fly.check.rest.YbOperativeEncodingController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import java.lang.Integer;
import com.taikang.fly.check.dto.operativeencoding.OperativeSearchDto;
import com.taikang.fly.check.dto.RmpResponse;
import com.taikang.fly.check.comm.NativePage;
import com.taikang.fly.check.service.YbOperativeEncodingService;

public class YbOperativeEncodingController	// class@0002bf from classes.dex
{
    private YbOperativeEncodingService ybOperativeEncodingService;
    private static final Logger log;

    static {
       YbOperativeEncodingController.log = LoggerFactory.getLogger(YbOperativeEncodingController.class);
    }
    public void YbOperativeEncodingController(){
       super();
    }
    public RmpResponse queryByPid(Integer page,Integer size,OperativeSearchDto operativeSearchDto){
       return RmpResponse.success(this.ybOperativeEncodingService.queryList(page, size, operativeSearchDto));
    }
}
