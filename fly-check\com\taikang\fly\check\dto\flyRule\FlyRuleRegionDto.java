package com.taikang.fly.check.dto.flyRule.FlyRuleRegionDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class FlyRuleRegionDto implements Serializable	// class@000108 from classes.dex
{
    private String region;
    private static final long serialVersionUID = 0x1;

    public void FlyRuleRegionDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRuleRegionDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof FlyRuleRegionDto) {
             b = false;
          }else {
             FlyRuleRegionDto uFlyRuleRegi = o;
             if (!uFlyRuleRegi.canEqual(this)) {
                b = false;
             }else {
                String region = this.getRegion();
                String region1 = uFlyRuleRegi.getRegion();
                if (region == null) {
                   if (region1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!region.equals(region1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getRegion(){
       return this.region;
    }
    public int hashCode(){
       String $region;
       int PRIME = 59;
       int result = 1;
       int i = (($region = this.getRegion()) == null)? 43: $region.hashCode();
       result = i + 59;
       return result;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public String toString(){
       return "FlyRuleRegionDto\(region="+this.getRegion()+"\)";
    }
}
