package com.taikang.fly.check.dto.mapstruct.OperativeEncodingMappingImpl;
import com.taikang.fly.check.dto.mapstruct.OperativeEncodingMapping;
import java.lang.Object;
import com.taikang.fly.check.mybatis.domain.GlOperativeEncoding;
import com.taikang.fly.check.dto.operativeencoding.OperativeEncodingDto;
import java.util.Date;
import java.lang.String;
import com.taikang.fly.check.dto.mapper.common.TypeConversionMapper;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import com.taikang.fly.check.mybatis.domain.YbOperativeEncoding;

public class OperativeEncodingMappingImpl implements OperativeEncodingMapping	// class@000170 from classes.dex
{
    private TypeConversionMapper typeConversionMapper;

    public void OperativeEncodingMappingImpl(){
       super();
    }
    public OperativeEncodingDto toGlDto(GlOperativeEncoding glOperativeEncoding){
       OperativeEncodingDto operativeEnc;
       if (glOperativeEncoding == null) {
          operativeEnc = null;
       }else {
          operativeEnc = new OperativeEncodingDto();
          operativeEnc.setCreateTime(this.typeConversionMapper.DateTime2String(glOperativeEncoding.getCreateTime()));
          operativeEnc.setModifyTime(this.typeConversionMapper.DateTime2String(glOperativeEncoding.getModifyTime()));
          operativeEnc.setId(glOperativeEncoding.getId());
          operativeEnc.setSurgeryCode(glOperativeEncoding.getSurgeryCode());
          operativeEnc.setSurgeryName(glOperativeEncoding.getSurgeryName());
          operativeEnc.setCreator(glOperativeEncoding.getCreator());
          operativeEnc.setModby(glOperativeEncoding.getModby());
       }
       return operativeEnc;
    }
    public List toGlDtoList(List glOperativeEncodings){
       List list;
       if (glOperativeEncodings == null) {
          list = null;
       }else {
          list = new ArrayList(glOperativeEncodings.size());
          Iterator iterator = glOperativeEncodings.iterator();
          while (iterator.hasNext()) {
             list.add(this.toGlDto(iterator.next()));
          }
       }
       return list;
    }
    public GlOperativeEncoding toGlEntity(OperativeEncodingDto operativeEncodingDto){
       GlOperativeEncoding glOperativeE;
       if (operativeEncodingDto == null) {
          glOperativeE = null;
       }else {
          glOperativeE = new GlOperativeEncoding();
          glOperativeE.setCreateTime(this.typeConversionMapper.String2Time(operativeEncodingDto.getCreateTime()));
          glOperativeE.setModifyTime(this.typeConversionMapper.String2Time(operativeEncodingDto.getModifyTime()));
          glOperativeE.setId(operativeEncodingDto.getId());
          glOperativeE.setSurgeryCode(operativeEncodingDto.getSurgeryCode());
          glOperativeE.setSurgeryName(operativeEncodingDto.getSurgeryName());
          glOperativeE.setCreator(operativeEncodingDto.getCreator());
          glOperativeE.setModby(operativeEncodingDto.getModby());
       }
       return glOperativeE;
    }
    public List toGlEntityList(List operativeEncodingDtos){
       List list;
       if (operativeEncodingDtos == null) {
          list = null;
       }else {
          list = new ArrayList(operativeEncodingDtos.size());
          Iterator iterator = operativeEncodingDtos.iterator();
          while (iterator.hasNext()) {
             list.add(this.toGlEntity(iterator.next()));
          }
       }
       return list;
    }
    public OperativeEncodingDto toYbDto(YbOperativeEncoding ybOperativeEncoding){
       OperativeEncodingDto operativeEnc;
       if (ybOperativeEncoding == null) {
          operativeEnc = null;
       }else {
          operativeEnc = new OperativeEncodingDto();
          operativeEnc.setCreateTime(this.typeConversionMapper.DateTime2String(ybOperativeEncoding.getCreateTime()));
          operativeEnc.setModifyTime(this.typeConversionMapper.DateTime2String(ybOperativeEncoding.getModifyTime()));
          operativeEnc.setId(ybOperativeEncoding.getId());
          operativeEnc.setSurgeryCode(ybOperativeEncoding.getSurgeryCode());
          operativeEnc.setSurgeryName(ybOperativeEncoding.getSurgeryName());
          operativeEnc.setCreator(ybOperativeEncoding.getCreator());
          operativeEnc.setModby(ybOperativeEncoding.getModby());
       }
       return operativeEnc;
    }
    public List toYbDtoList(List glOperativeEncodings){
       List list;
       if (glOperativeEncodings == null) {
          list = null;
       }else {
          list = new ArrayList(glOperativeEncodings.size());
          Iterator iterator = glOperativeEncodings.iterator();
          while (iterator.hasNext()) {
             list.add(this.toYbDto(iterator.next()));
          }
       }
       return list;
    }
    public YbOperativeEncoding toYbEntity(OperativeEncodingDto operativeEncodingDto){
       YbOperativeEncoding ybOperativeE;
       if (operativeEncodingDto == null) {
          ybOperativeE = null;
       }else {
          ybOperativeE = new YbOperativeEncoding();
          ybOperativeE.setCreateTime(this.typeConversionMapper.String2Time(operativeEncodingDto.getCreateTime()));
          ybOperativeE.setModifyTime(this.typeConversionMapper.String2Time(operativeEncodingDto.getModifyTime()));
          ybOperativeE.setId(operativeEncodingDto.getId());
          ybOperativeE.setSurgeryCode(operativeEncodingDto.getSurgeryCode());
          ybOperativeE.setSurgeryName(operativeEncodingDto.getSurgeryName());
          ybOperativeE.setCreator(operativeEncodingDto.getCreator());
          ybOperativeE.setModby(operativeEncodingDto.getModby());
       }
       return ybOperativeE;
    }
    public List toYbEntityList(List operativeEncodingDtos){
       List list;
       if (operativeEncodingDtos == null) {
          list = null;
       }else {
          list = new ArrayList(operativeEncodingDtos.size());
          Iterator iterator = operativeEncodingDtos.iterator();
          while (iterator.hasNext()) {
             list.add(this.toYbEntity(iterator.next()));
          }
       }
       return list;
    }
}
