package com.taikang.fly.check.dto.user.UserEditDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class UserEditDto implements Serializable	// class@0001c2 from classes.dex
{
    private String id;
    private String name;
    private String region;
    private static final long serialVersionUID = 0x1;

    public void UserEditDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof UserEditDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof UserEditDto) {
             b = false;
          }else {
             UserEditDto userEditDto = o;
             if (!userEditDto.canEqual(this)) {
                b = false;
             }else {
                String id = this.getId();
                String id1 = userEditDto.getId();
                if (id == null) {
                   if (id1 != null) {
                      b = false;
                   }
                }else if(id.equals(id1)){
                }
                String name = this.getName();
                String name1 = userEditDto.getName();
                if (name == null) {
                   if (name1 != null) {
                      b = false;
                   }
                }else if(name.equals(name1)){
                }
                String region = this.getRegion();
                String region1 = userEditDto.getRegion();
                if (region == null) {
                   if (region1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!region.equals(region1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getId(){
       return this.id;
    }
    public String getName(){
       return this.name;
    }
    public String getRegion(){
       return this.region;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $name = this.getName();
       int i2 = result * 59;
       i1 = ($name == null)? i: $name.hashCode();
       result = i2 + i1;
       String $region = this.getRegion();
       i1 = result * 59;
       if ($region != null) {
          i = $region.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setName(String name){
       this.name = name;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public String toString(){
       return "UserEditDto\(id="+this.getId()+", name="+this.getName()+", region="+this.getRegion()+"\)";
    }
}
