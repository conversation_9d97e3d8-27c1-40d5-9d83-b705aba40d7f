package com.taikang.fly.check.mybatis.dao.ModuleMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import com.taikang.fly.check.mybatis.domain.Module;
import java.util.Map;
import java.util.List;
import java.lang.Integer;

public interface abstract ModuleMapper implements BaseMapper	// class@000211 from classes.dex
{

    Module findByKey(String p0);
    List queryListByParams(Map p0);
    List queryListByUserCode(Map p0);
    Integer queryListPageCountByParams(Map p0);
    List selectByCode(String p0);
    void updateByCode(Module p0);
}
