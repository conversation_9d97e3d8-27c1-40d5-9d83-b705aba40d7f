package com.taikang.fly.check.dto.jsoncreate.MatchingDTO;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.lang.StringBuilder;

public class MatchingDTO	// class@000139 from classes.dex
{
    private String dbName;
    private String password;
    private List reTable;
    private String taTable;
    private String url;
    private String username;

    public void MatchingDTO(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MatchingDTO;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof MatchingDTO){
          b = false;
       }else {
          MatchingDTO matchingDTO = o;
          if (!matchingDTO.canEqual(this)) {
             b = false;
          }else {
             String url = this.getUrl();
             String url1 = matchingDTO.getUrl();
             if (url == null) {
                if (url1 != null) {
                   b = false;
                }
             }else if(url.equals(url1)){
             }
             String username = this.getUsername();
             String username1 = matchingDTO.getUsername();
             if (username == null) {
                if (username1 != null) {
                   b = false;
                }
             }else if(username.equals(username1)){
             }
             String password = this.getPassword();
             String password1 = matchingDTO.getPassword();
             if (password == null) {
                if (password1 != null) {
                   b = false;
                }
             }else if(password.equals(password1)){
             }
             String dbName = this.getDbName();
             String dbName1 = matchingDTO.getDbName();
             if (dbName == null) {
                if (dbName1 != null) {
                   b = false;
                }
             }else if(dbName.equals(dbName1)){
             }
             List reTable = this.getReTable();
             List reTable1 = matchingDTO.getReTable();
             if (reTable == null) {
                if (reTable1 != null) {
                   b = false;
                }
             }else if(reTable.equals(reTable1)){
             }
             String taTable = this.getTaTable();
             String taTable1 = matchingDTO.getTaTable();
             if (taTable == null) {
                if (taTable1 != null) {
                label_0085 :
                   b = false;
                }
             }else if(taTable.equals(taTable1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getDbName(){
       return this.dbName;
    }
    public String getPassword(){
       return this.password;
    }
    public List getReTable(){
       return this.reTable;
    }
    public String getTaTable(){
       return this.taTable;
    }
    public String getUrl(){
       return this.url;
    }
    public String getUsername(){
       return this.username;
    }
    public int hashCode(){
       String $url;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($url = this.getUrl()) == null)? i: $url.hashCode();
       result = i1 + 59;
       String $username = this.getUsername();
       int i2 = result * 59;
       i1 = ($username == null)? i: $username.hashCode();
       result = i2 + i1;
       String $password = this.getPassword();
       i2 = result * 59;
       i1 = ($password == null)? i: $password.hashCode();
       result = i2 + i1;
       String $dbName = this.getDbName();
       i2 = result * 59;
       i1 = ($dbName == null)? i: $dbName.hashCode();
       result = i2 + i1;
       List $reTable = this.getReTable();
       i2 = result * 59;
       i1 = ($reTable == null)? i: $reTable.hashCode();
       result = i2 + i1;
       String taTable = this.getTaTable();
       i1 = result * 59;
       if (taTable != null) {
          i = taTable.hashCode();
       }
       return (i1 + i);
    }
    public void setDbName(String dbName){
       this.dbName = dbName;
    }
    public void setPassword(String password){
       this.password = password;
    }
    public void setReTable(List reTable){
       this.reTable = reTable;
    }
    public void setTaTable(String taTable){
       this.taTable = taTable;
    }
    public void setUrl(String url){
       this.url = url;
    }
    public void setUsername(String username){
       this.username = username;
    }
    public String toString(){
       return "MatchingDTO\(url="+this.getUrl()+", username="+this.getUsername()+", password="+this.getPassword()+", dbName="+this.getDbName()+", reTable="+this.getReTable()+", taTable="+this.getTaTable()+"\)";
    }
}
