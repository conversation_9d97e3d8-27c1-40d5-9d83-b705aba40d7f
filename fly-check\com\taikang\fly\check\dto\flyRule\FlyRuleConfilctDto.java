package com.taikang.fly.check.dto.flyRule.FlyRuleConfilctDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class FlyRuleConfilctDto implements Serializable	// class@0000fa from classes.dex
{
    private String a;
    private String b;
    private String tableName;
    private static final long serialVersionUID = 0x1;

    public void FlyRuleConfilctDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRuleConfilctDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof FlyRuleConfilctDto) {
             b = false;
          }else {
             FlyRuleConfilctDto uFlyRuleConf = o;
             if (!uFlyRuleConf.canEqual(this)) {
                b = false;
             }else {
                String a = this.getA();
                String a1 = uFlyRuleConf.getA();
                if (a == null) {
                   if (a1 != null) {
                      b = false;
                   }
                }else if(a.equals(a1)){
                }
                String b1 = this.getB();
                String b2 = uFlyRuleConf.getB();
                if (b1 == null) {
                   if (b2 != null) {
                      b = false;
                   }
                }else if(b1.equals(b2)){
                }
                String tableName = this.getTableName();
                String tableName1 = uFlyRuleConf.getTableName();
                if (tableName == null) {
                   if (tableName1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!tableName.equals(tableName1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getA(){
       return this.a;
    }
    public String getB(){
       return this.b;
    }
    public String getTableName(){
       return this.tableName;
    }
    public int hashCode(){
       String $a;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($a = this.getA()) == null)? i: $a.hashCode();
       result = i1 + 59;
       String $b = this.getB();
       int i2 = result * 59;
       i1 = ($b == null)? i: $b.hashCode();
       result = i2 + i1;
       String $tableName = this.getTableName();
       i1 = result * 59;
       if ($tableName != null) {
          i = $tableName.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setA(String a){
       this.a = a;
    }
    public void setB(String b){
       this.b = b;
    }
    public void setTableName(String tableName){
       this.tableName = tableName;
    }
    public String toString(){
       return "FlyRuleConfilctDto\(a="+this.getA()+", b="+this.getB()+", tableName="+this.getTableName()+"\)";
    }
}
