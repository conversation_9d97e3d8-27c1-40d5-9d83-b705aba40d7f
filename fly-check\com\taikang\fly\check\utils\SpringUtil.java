package com.taikang.fly.check.utils.SpringUtil;
import java.lang.Object;
import org.springframework.context.ApplicationContext;
import java.lang.Class;
import java.lang.String;

public class SpringUtil	// class@00034c from classes.dex
{
    private static ApplicationContext applicationContext;

    static {
       SpringUtil.applicationContext = null;
    }
    public void SpringUtil(){
       super();
    }
    public static ApplicationContext getApplicationContext(){
       return SpringUtil.applicationContext;
    }
    public static Object getBean(Class clazz){
       return SpringUtil.getApplicationContext().getBean(clazz);
    }
    public static Object getBean(String name){
       return SpringUtil.getApplicationContext().getBean(name);
    }
    public static Object getBean(String name,Class clazz){
       return SpringUtil.getApplicationContext().getBean(name, clazz);
    }
    public static void setApplicationContext(ApplicationContext applicationContext){
       if (SpringUtil.applicationContext == null) {
          SpringUtil.applicationContext = applicationContext;
       }
       return;
    }
}
