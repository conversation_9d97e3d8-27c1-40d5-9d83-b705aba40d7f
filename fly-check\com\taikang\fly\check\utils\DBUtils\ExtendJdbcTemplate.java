package com.taikang.fly.check.utils.DBUtils.ExtendJdbcTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.sql.DataSource;
import java.lang.String;
import com.github.pagehelper.Page;
import com.taikang.fly.check.comm.NativePage;
import java.lang.Object;
import java.lang.Integer;
import java.lang.Long;
import java.util.List;

public class ExtendJdbcTemplate extends JdbcTemplate	// class@000336 from classes.dex
{
    private final Logger log;

    public void ExtendJdbcTemplate(){
       super();
       this.log = LoggerFactory.getLogger(ExtendJdbcTemplate.class);
    }
    public void ExtendJdbcTemplate(DataSource dataSource){
       super(dataSource);
       this.log = LoggerFactory.getLogger(ExtendJdbcTemplate.class);
    }
    public void ExtendJdbcTemplate(DataSource dataSource,boolean lazyInit){
       super(dataSource, lazyInit);
       this.log = LoggerFactory.getLogger(ExtendJdbcTemplate.class);
    }
    public NativePage queryForPage(String sql,Page pagination){
       NativePage result = new NativePage();
       int pageNum = pagination.getPageNum();
       int pageSize = pagination.getPageSize();
       Object[] objArray = new Object[]{sql};
       String countSql = String.format("select count\(1\) as count from \( %s \) temp", objArray);
       Integer integer = super.queryForObject(countSql, Integer.class);
       result.setAllRow(Long.valueOf((long)integer.intValue()));
       result.setCurrentPage(Integer.valueOf(pageNum));
       result.setSize(Integer.valueOf(pageSize));
       result.setTotalPage(Integer.valueOf(((integer.intValue() / pagination.getPageSize()) + 1)));
       Integer currentPage = Integer.valueOf(((pageNum - 1) * pageSize));
       Integer currentSize = Integer.valueOf((pageNum * pageSize));
       objArray = new Object[]{sql,currentSize,currentPage};
       String pageQuerySql = String.format("SELECT * FROM \(SELECT tt.*, ROWNUM AS rowno  FROM \( %s \) tt WHERE ROWNUM <= %s \) table_alias WHERE table_alias.rowno > %s", objArray);
       List pageResult = super.queryForList(pageQuerySql);
       result.setDataList(pageResult);
       return result;
    }
}
