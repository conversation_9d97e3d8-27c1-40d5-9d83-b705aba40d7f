package com.taikang.fly.check.config.MyWebConfig;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import java.lang.Object;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import java.lang.String;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistration;

public class MyWebConfig implements WebMvcConfigurer	// class@00008e from classes.dex
{

    public void MyWebConfig(){
       super();
    }
    public void addResourceHandlers(ResourceHandlerRegistry registry){
       String[] stringArray = new String[]{"/mlh/**"};
       String[] stringArray1 = new String[]{"classpath:static/page/"};
       registry.addResourceHandler(stringArray).addResourceLocations(stringArray1);
       stringArray = new String[]{"/jmreport/**"};
       stringArray1 = new String[]{"classpath:static/jmreport/"};
       registry.addResourceHandler(stringArray).addResourceLocations(stringArray1);
    }
}
