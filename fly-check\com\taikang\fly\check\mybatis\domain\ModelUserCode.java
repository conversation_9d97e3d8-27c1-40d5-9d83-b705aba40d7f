package com.taikang.fly.check.mybatis.domain.ModelUserCode;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ModelUserCode	// class@00025d from classes.dex
{
    private int code;
    private String userId;

    public void ModelUserCode(){
       super();
    }
    public void ModelUserCode(String userId,int code){
       super();
       this.userId = userId;
       this.code = code;
    }
    protected boolean canEqual(Object other){
       return other instanceof ModelUserCode;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof ModelUserCode) {
             b = false;
          }else {
             ModelUserCode modelUserCod = o;
             if (!modelUserCod.canEqual(this)) {
                b = false;
             }else {
                String userId = this.getUserId();
                String userId1 = modelUserCod.getUserId();
                if (userId == null) {
                   if (userId1 != null) {
                      b = false;
                   }
                }else if(userId.equals(userId1)){
                }
                if (this.getCode() != modelUserCod.getCode()) {
                   b = false;
                }
             }
          }
       }
       return b;
    }
    public int getCode(){
       return this.code;
    }
    public String getUserId(){
       return this.userId;
    }
    public int hashCode(){
       String $userId;
       int PRIME = 59;
       int result = 1;
       int i = (($userId = this.getUserId()) == null)? 43: $userId.hashCode();
       result = i + 59;
       result = (result * 59) + this.getCode();
       return result;
    }
    public void setCode(int code){
       this.code = code;
    }
    public void setUserId(String userId){
       this.userId = userId;
    }
    public String toString(){
       return "ModelUserCode\(userId="+this.getUserId()+", code="+this.getCode()+"\)";
    }
}
