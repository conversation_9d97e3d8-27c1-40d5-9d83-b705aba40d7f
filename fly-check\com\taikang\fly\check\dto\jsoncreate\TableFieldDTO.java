package com.taikang.fly.check.dto.jsoncreate.TableFieldDTO;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import java.lang.StringBuilder;

public class TableFieldDTO	// class@00013b from classes.dex
{
    public List oraFieldInfo;
    public String tableName;

    public void TableFieldDTO(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof TableFieldDTO;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof TableFieldDTO) {
             b = false;
          }else {
             TableFieldDTO tableFieldDT = o;
             if (!tableFieldDT.canEqual(this)) {
                b = false;
             }else {
                String tableName = this.getTableName();
                String tableName1 = tableFieldDT.getTableName();
                if (tableName == null) {
                   if (tableName1 != null) {
                      b = false;
                   }
                }else if(tableName.equals(tableName1)){
                }
                List oraFieldInfo = this.getOraFieldInfo();
                List oraFieldInfo1 = tableFieldDT.getOraFieldInfo();
                if (oraFieldInfo == null) {
                   if (oraFieldInfo1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!oraFieldInfo.equals(oraFieldInfo1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public List getOraFieldInfo(){
       return this.oraFieldInfo;
    }
    public String getTableName(){
       return this.tableName;
    }
    public int hashCode(){
       String $tableName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($tableName = this.getTableName()) == null)? i: $tableName.hashCode();
       result = i1 + 59;
       List $oraFieldInfo = this.getOraFieldInfo();
       i1 = result * 59;
       if ($oraFieldInfo != null) {
          i = $oraFieldInfo.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setOraFieldInfo(List oraFieldInfo){
       this.oraFieldInfo = oraFieldInfo;
    }
    public void setTableName(String tableName){
       this.tableName = tableName;
    }
    public String toString(){
       return "TableFieldDTO\(tableName="+this.getTableName()+", oraFieldInfo="+this.getOraFieldInfo()+"\)";
    }
}
