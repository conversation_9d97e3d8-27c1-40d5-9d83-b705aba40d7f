package com.taikang.fly.check.dto.TableListDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class TableListDto implements Serializable	// class@0000b2 from classes.dex
{
    private String newTableName;
    private String oldTableName;
    private static final long serialVersionUID = 0x349d06aa3e3fa7a7;

    public void TableListDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof TableListDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof TableListDto) {
             b = false;
          }else {
             TableListDto tableListDto = o;
             if (!tableListDto.canEqual(this)) {
                b = false;
             }else {
                String oldTableName = this.getOldTableName();
                String oldTableName1 = tableListDto.getOldTableName();
                if (oldTableName == null) {
                   if (oldTableName1 != null) {
                      b = false;
                   }
                }else if(oldTableName.equals(oldTableName1)){
                }
                String newTableName = this.getNewTableName();
                String newTableName1 = tableListDto.getNewTableName();
                if (newTableName == null) {
                   if (newTableName1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!newTableName.equals(newTableName1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getNewTableName(){
       return this.newTableName;
    }
    public String getOldTableName(){
       return this.oldTableName;
    }
    public int hashCode(){
       String $oldTableName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($oldTableName = this.getOldTableName()) == null)? i: $oldTableName.hashCode();
       result = i1 + 59;
       String $newTableName = this.getNewTableName();
       i1 = result * 59;
       if ($newTableName != null) {
          i = $newTableName.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setNewTableName(String newTableName){
       this.newTableName = newTableName;
    }
    public void setOldTableName(String oldTableName){
       this.oldTableName = oldTableName;
    }
    public String toString(){
       return "TableListDto\(oldTableName="+this.getOldTableName()+", newTableName="+this.getNewTableName()+"\)";
    }
}
