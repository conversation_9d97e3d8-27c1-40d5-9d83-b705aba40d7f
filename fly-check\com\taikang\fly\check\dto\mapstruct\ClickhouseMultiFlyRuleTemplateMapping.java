package com.taikang.fly.check.dto.mapstruct.ClickhouseMultiFlyRuleTemplateMapping;
import com.taikang.fly.check.mybatis.domain.ClickhouseMultiFlyRuleTemplate;
import com.taikang.fly.check.dto.clickhouseFlyRule.ClickhouseMultiFlyRuleTemplateRespDto;
import com.taikang.fly.check.dto.clickhouseFlyRule.ClickhouseMultiTemplateParamXRespDto;
import com.taikang.fly.check.mybatis.domain.ClickhouseFlyRuleMulti;

public interface abstract ClickhouseMultiFlyRuleTemplateMapping	// class@000144 from classes.dex
{

    ClickhouseMultiFlyRuleTemplateRespDto domainToInfoDto(ClickhouseMultiFlyRuleTemplate p0);
    ClickhouseFlyRuleMulti getFlyRuleMulti(ClickhouseMultiTemplateParamXRespDto p0);
}
