package org.springframework.boot.loader.jar.JarEntry;
import org.springframework.boot.loader.jar.FileHeader;
import java.util.jar.JarEntry;
import org.springframework.boot.loader.jar.JarFile;
import org.springframework.boot.loader.jar.CentralDirectoryFileHeader;
import org.springframework.boot.loader.jar.AsciiBytes;
import java.lang.String;
import java.util.jar.Attributes;
import java.util.jar.Manifest;
import java.security.cert.Certificate;
import java.security.CodeSigner;
import java.net.URL;
import java.lang.CharSequence;

class JarEntry extends JarEntry implements FileHeader	// class@000551 from classes.dex
{
    private Certificate[] certificates;
    private CodeSigner[] codeSigners;
    private final AsciiBytes headerName;
    private final JarFile jarFile;
    private long localHeaderOffset;
    private final AsciiBytes name;

    void JarEntry(JarFile jarFile,CentralDirectoryFileHeader header,AsciiBytes nameAlias){
       String str = (nameAlias != null)? nameAlias.toString(): header.getName().toString();
       super(str);
       if (nameAlias == null) {
          nameAlias = header.getName();
       }
       this.name = nameAlias;
       this.headerName = header.getName();
       this.jarFile = jarFile;
       this.localHeaderOffset = header.getLocalHeaderOffset();
       this.setCompressedSize(header.getCompressedSize());
       this.setMethod(header.getMethod());
       this.setCrc(header.getCrc());
       this.setComment(header.getComment().toString());
       this.setSize(header.getSize());
       this.setTime(header.getTime());
       this.setExtra(header.getExtra());
       return;
    }
    AsciiBytes getAsciiBytesName(){
       return this.name;
    }
    public Attributes getAttributes(){
       Manifest manifest;
       Attributes attributes = ((manifest = this.jarFile.getManifest()) != null)? manifest.getAttributes(this.getName()): null;
       return attributes;
    }
    public Certificate[] getCertificates(){
       if (this.jarFile.isSigned() && this.certificates == null) {
          this.jarFile.setupEntryCertificates(this);
       }
       return this.certificates;
    }
    public CodeSigner[] getCodeSigners(){
       if (this.jarFile.isSigned() && this.codeSigners == null) {
          this.jarFile.setupEntryCertificates(this);
       }
       return this.codeSigners;
    }
    public long getLocalHeaderOffset(){
       return this.localHeaderOffset;
    }
    URL getUrl(){
       return new URL(this.jarFile.getUrl(), this.getName());
    }
    public boolean hasName(CharSequence name,char suffix){
       return this.headerName.matches(name, suffix);
    }
    void setCertificates(JarEntry entry){
       this.certificates = entry.getCertificates();
       this.codeSigners = entry.getCodeSigners();
    }
}
