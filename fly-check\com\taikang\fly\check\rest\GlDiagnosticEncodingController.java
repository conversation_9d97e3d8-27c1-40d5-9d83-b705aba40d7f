package com.taikang.fly.check.rest.GlDiagnosticEncodingController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import java.lang.Integer;
import com.taikang.fly.check.dto.diagnosticencoding.DiagnosticSearchDto;
import com.taikang.fly.check.dto.RmpResponse;
import com.taikang.fly.check.comm.NativePage;
import com.taikang.fly.check.service.GlDiagnosticEncodingService;

public class GlDiagnosticEncodingController	// class@000296 from classes.dex
{
    private GlDiagnosticEncodingService glDiagnosticEncodingService;
    private static final Logger log;

    static {
       GlDiagnosticEncodingController.log = LoggerFactory.getLogger(GlDiagnosticEncodingController.class);
    }
    public void GlDiagnosticEncodingController(){
       super();
    }
    public RmpResponse queryByPid(Integer page,Integer size,DiagnosticSearchDto diagnosticSearchDto){
       return RmpResponse.success(this.glDiagnosticEncodingService.queryList(page, size, diagnosticSearchDto));
    }
}
