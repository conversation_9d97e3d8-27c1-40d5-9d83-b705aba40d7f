package com.taikang.fly.check.dto.ybDiagnosisTreatmentList.YbDiagnosisTreatmentListSearchDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class YbDiagnosisTreatmentListSearchDto implements Serializable	// class@0001cf from classes.dex
{
    private String projectCode;
    private String projectName;
    private static final long serialVersionUID = 0x1;

    public void YbDiagnosisTreatmentListSearchDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof YbDiagnosisTreatmentListSearchDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof YbDiagnosisTreatmentListSearchDto) {
             b = false;
          }else {
             YbDiagnosisTreatmentListSearchDto ybDiagnosisT = o;
             if (!ybDiagnosisT.canEqual(this)) {
                b = false;
             }else {
                String projectCode = this.getProjectCode();
                String projectCode1 = ybDiagnosisT.getProjectCode();
                if (projectCode == null) {
                   if (projectCode1 != null) {
                      b = false;
                   }
                }else if(projectCode.equals(projectCode1)){
                }
                String projectName = this.getProjectName();
                String projectName1 = ybDiagnosisT.getProjectName();
                if (projectName == null) {
                   if (projectName1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!projectName.equals(projectName1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getProjectCode(){
       return this.projectCode;
    }
    public String getProjectName(){
       return this.projectName;
    }
    public int hashCode(){
       String $projectCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($projectCode = this.getProjectCode()) == null)? i: $projectCode.hashCode();
       result = i1 + 59;
       String $projectName = this.getProjectName();
       i1 = result * 59;
       if ($projectName != null) {
          i = $projectName.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setProjectCode(String projectCode){
       this.projectCode = projectCode;
    }
    public void setProjectName(String projectName){
       this.projectName = projectName;
    }
    public String toString(){
       return "YbDiagnosisTreatmentListSearchDto\(projectCode="+this.getProjectCode()+", projectName="+this.getProjectName()+"\)";
    }
}
