package com.taikang.fly.check.vo.drg.DrgDepartmentsProfitAndLossVo;
import java.lang.Object;
import java.lang.String;
import java.math.BigDecimal;
import java.lang.StringBuilder;

public class DrgDepartmentsProfitAndLossVo	// class@000365 from classes.dex
{
    private String departmentName;
    private String desgDeptCode;
    private BigDecimal totalProfitAndLoss;

    public void DrgDepartmentsProfitAndLossVo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgDepartmentsProfitAndLossVo;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DrgDepartmentsProfitAndLossVo) {
             b = false;
          }else {
             DrgDepartmentsProfitAndLossVo uDrgDepartme = o;
             if (!uDrgDepartme.canEqual(this)) {
                b = false;
             }else {
                String desgDeptCode = this.getDesgDeptCode();
                String desgDeptCode1 = uDrgDepartme.getDesgDeptCode();
                if (desgDeptCode == null) {
                   if (desgDeptCode1 != null) {
                      b = false;
                   }
                }else if(desgDeptCode.equals(desgDeptCode1)){
                }
                String departmentNa = this.getDepartmentName();
                String departmentNa1 = uDrgDepartme.getDepartmentName();
                if (departmentNa == null) {
                   if (departmentNa1 != null) {
                      b = false;
                   }
                }else if(departmentNa.equals(departmentNa1)){
                }
                BigDecimal totalProfitA = this.getTotalProfitAndLoss();
                BigDecimal totalProfitA1 = uDrgDepartme.getTotalProfitAndLoss();
                if (totalProfitA == null) {
                   if (totalProfitA1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!totalProfitA.equals(totalProfitA1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getDepartmentName(){
       return this.departmentName;
    }
    public String getDesgDeptCode(){
       return this.desgDeptCode;
    }
    public BigDecimal getTotalProfitAndLoss(){
       return this.totalProfitAndLoss;
    }
    public int hashCode(){
       String $desgDeptCode;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($desgDeptCode = this.getDesgDeptCode()) == null)? i: $desgDeptCode.hashCode();
       result = i1 + 59;
       String $departmentName = this.getDepartmentName();
       int i2 = result * 59;
       i1 = ($departmentName == null)? i: $departmentName.hashCode();
       result = i2 + i1;
       BigDecimal $totalProfitAndLoss = this.getTotalProfitAndLoss();
       i1 = result * 59;
       if ($totalProfitAndLoss != null) {
          i = $totalProfitAndLoss.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDepartmentName(String departmentName){
       this.departmentName = departmentName;
    }
    public void setDesgDeptCode(String desgDeptCode){
       this.desgDeptCode = desgDeptCode;
    }
    public void setTotalProfitAndLoss(BigDecimal totalProfitAndLoss){
       this.totalProfitAndLoss = totalProfitAndLoss;
    }
    public String toString(){
       return "DrgDepartmentsProfitAndLossVo\(desgDeptCode="+this.getDesgDeptCode()+", departmentName="+this.getDepartmentName()+", totalProfitAndLoss="+this.getTotalProfitAndLoss()+"\)";
    }
}
