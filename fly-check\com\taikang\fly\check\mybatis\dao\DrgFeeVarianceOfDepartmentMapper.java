package com.taikang.fly.check.mybatis.dao.DrgFeeVarianceOfDepartmentMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import java.util.List;
import com.taikang.fly.check.vo.drg.DrgQueryFeeVarianceOfDepartmentVo;

public interface abstract DrgFeeVarianceOfDepartmentMapper implements BaseMapper	// class@0001e6 from classes.dex
{

    List getDrgDateOfDepartmentList(String p0);
    List getDrgDateOfDepartmentNumRWList(String p0);
    List getDrgDateOfDepartmentRWList(String p0);
    List getDrgFeeOfDepartmentDetail(String p0,String p1,String p2,String p3,String p4,String p5,String p6);
    List getdrgFeeVarianceOfDepartmentList(String p0,DrgQueryFeeVarianceOfDepartmentVo p1);
}
