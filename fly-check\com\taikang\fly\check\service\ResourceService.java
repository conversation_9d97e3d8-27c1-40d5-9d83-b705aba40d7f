package com.taikang.fly.check.service.ResourceService;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.taikang.fly.check.mybatis.dao.ResourceMapper;
import java.util.List;
import com.taikang.fly.check.mybatis.domain.Resource;
import java.util.Iterator;
import java.util.ArrayList;
import com.taikang.fly.check.dto.resource.ResourceTreeResDto;
import com.taikang.fly.check.mybatis.domain.Menu;
import com.taikang.fly.check.dto.resource.ResourceIndexDto;
import com.taikang.fly.check.dto.mapstruct.ResourceMapping;
import com.taikang.fly.check.dto.resource.ResourceIndexRDto;
import com.taikang.fly.check.dto.mapstruct.MenuMapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.taikang.fly.check.dto.resource.ResourceAddDto;
import java.lang.CharSequence;
import org.apache.commons.lang3.StringUtils;
import com.taikang.fly.check.comm.BizException;
import com.taikang.fly.check.comm.enums.ResponseCodeEnum;
import com.taikang.fly.check.dto.resource.ResourceEditDto;
import com.taikang.fly.check.mybatis.dao.MenuDao;
import java.util.HashSet;
import com.taikang.fly.check.security.core.DomainContext;
import com.taikang.fly.check.security.core.ThreadLocalContextHolder;
import com.taikang.fly.check.dto.user.UserDto;
import com.taikang.fly.check.service.UserRoleService;
import com.taikang.fly.check.mybatis.dao.RoleResourceMapper;
import java.util.Collection;
import java.util.Set;
import org.springframework.util.CollectionUtils;

public class ResourceService	// class@0002f9 from classes.dex
{
    private MenuDao menuDao;
    private MenuMapper menuMapper;
    private ResourceMapper resourceMapper;
    private ResourceMapping resourceMapping;
    private RoleResourceMapper roleResourceMapper;
    private UserRoleService userRoleService;
    private static final String PARENT_ID = "parent_id";
    private static final String RESOURCE_ID = "resource_id";

    public void ResourceService(){
       super();
    }
    private Integer deleteResourceById(String id){
       QueryWrapper queryWrapper = new QueryWrapper();
       queryWrapper.eq("resource_id", id);
       return Integer.valueOf(this.resourceMapper.delete(queryWrapper));
    }
    private List selectAllResourceList(){
       QueryWrapper queryWrapper = new QueryWrapper();
       Object[] objArray = new Object[]{"resource_order"};
       queryWrapper.orderByAsc(objArray);
       return this.resourceMapper.selectList(queryWrapper);
    }
    private List selectListByParentId(String parentId){
       QueryWrapper queryWrapper = new QueryWrapper();
       queryWrapper.eq("parent_id", parentId);
       return this.resourceMapper.selectList(queryWrapper);
    }
    private Resource selectResourceById(String id){
       QueryWrapper queryWrapper = new QueryWrapper();
       queryWrapper.eq("resource_id", id);
       return this.resourceMapper.selectOne(queryWrapper);
    }
    private List transResourceTreeJson(List resources,String parentId){
       List menuTreeList = null;
       Iterator iterator = resources.iterator();
       while (iterator.hasNext()) {
          Resource resource = iterator.next();
          if (parentId.equals(resource.getParentId())) {
             if (menuTreeList == null) {
                menuTreeList = new ArrayList();
             }
             ResourceTreeResDto resourceTree = new ResourceTreeResDto();
             resourceTree.setId(resource.getResourceId());
             resourceTree.setText(resource.getResourceName());
             resourceTree.setUrl(resource.getUrl());
             resourceTree.setIcon(resource.getIcon());
             resourceTree.setIclass(resource.getIclass());
             resourceTree.setChildren(this.transResourceTreeJson(resources, resource.getResourceId()));
             menuTreeList.add(resourceTree);
          }
       }
       return menuTreeList;
    }
    private List transResourceTreeJsonList(List menuList,List resourceList,String parentId){
       List menuTreeList = null;
       Iterator iterator = menuList.iterator();
       while (iterator.hasNext()) {
          Menu menu = iterator.next();
          if (parentId.equals(menu.getParentId())) {
             if (menuTreeList == null) {
                menuTreeList = new ArrayList();
             }
             ResourceTreeResDto resourceTree = new ResourceTreeResDto();
             resourceTree.setId(menu.getMenuId());
             resourceTree.setText(menu.getMenuName());
             resourceTree.setUrl(menu.getUrl());
             resourceTree.setIcon(menu.getIcon());
             resourceTree.setIclass(menu.getIclass());
             resourceTree.setIsMenuORresource("1");
             if (!"1".equals(menu.getIsLeaf())) {
                resourceTree.setChildren(this.transResourceTreeJsonList(menuList, resourceList, menu.getMenuId()));
             }else {
                List list = null;
                Iterator iterator1 = resourceList.iterator();
                while (iterator1.hasNext()) {
                   Resource resource = iterator1.next();
                   if (menu.getMenuId().equals(resource.getParentId()) && "0".equals(resource.getResourceType())) {
                      if (list == null) {
                         list = new ArrayList();
                      }
                      ResourceTreeResDto resourceTree1 = new ResourceTreeResDto();
                      resourceTree1.setId(resource.getResourceId());
                      resourceTree1.setText(resource.getResourceName());
                      resourceTree1.setUrl(resource.getUrl());
                      resourceTree1.setIcon(resource.getIcon());
                      resourceTree1.setIclass(resource.getIclass());
                      resourceTree1.setIsMenuORresource("2");
                      list.add(resourceTree1);
                   }else {
                      continue ;
                   }
                }
                resourceTree.setChildren(list);
             }
             menuTreeList.add(resourceTree);
          }
       }
       return menuTreeList;
    }
    private void transTreeResourceList(List resourceIndexDtoList,List resourceList,int level,String parentId){
       Iterator iterator = resourceList.iterator();
       while (iterator.hasNext()) {
          Resource resource = iterator.next();
          if (parentId.equals(resource.getParentId())) {
             resourceIndexDtoList.add(this.resourceMapping.toIndexDto(resource, level, parentId));
             if (!"1".equals(resource.getIsLeaf())) {
                int i = level + 1;
                this.transTreeResourceList(resourceIndexDtoList, resourceList, i, resource.getResourceId());
             }
          }
       }
       return;
    }
    private void transTreeResourceRList(List resourceIndexRDtoList,List menuList,List resourceList,int level,String parentId){
       Iterator iterator = menuList.iterator();
       while (iterator.hasNext()) {
          Menu menux = iterator.next();
          if (parentId.equals(menux.getParentId())) {
             ResourceIndexRDto resourceIndexRDto = this.menuMapper.menuToResourceIndexRDto(menux, level, parentId);
             resourceIndexRDtoList.add(resourceIndexRDto);
             if (!"1".equals(menux.getIsLeaf())) {
                int i = level + 1;
                this.transTreeResourceRList(resourceIndexRDtoList, menuList, resourceList, i, menux.getMenuId());
             }else {
                Iterator iterator1 = resourceList.iterator();
                while (iterator1.hasNext()) {
                   Resource resource = iterator1.next();
                   if (menux.getMenuId().equals(resource.getParentId())) {
                      int i1 = level + 1;
                      resourceIndexRDtoList.add(this.resourceMapping.toResourceIndexRDto(resource, i1, resource.getParentId()));
                   }else {
                      continue ;
                   }
                }
             }
          }
       }
       return;
    }
    private Integer updateResourceById1(Resource resource){
       UpdateWrapper updateWrapper = new UpdateWrapper();
       updateWrapper.eq("resource_id", resource.getResourceId());
       return Integer.valueOf(this.resourceMapper.update(resource, updateWrapper));
    }
    public void addResource(ResourceAddDto resourceAddDto){
       Resource resource = this.resourceMapping.addDto2Entity(resourceAddDto);
       if (StringUtils.isNotBlank(resourceAddDto.getUrl())) {
          resource.setAclass("J_menuItem");
       }
       this.resourceMapper.insert(resource);
       return;
    }
    public void delResource1(String resourceId){
       Resource resource;
       List list;
       if ((resource = this.selectResourceById(resourceId)) == null) {
          throw new BizException(ResponseCodeEnum.DATA_NOT_EXISTS);
       }
       if ((list = this.selectListByParentId(resourceId)) != null && !list.isEmpty()) {
          throw new BizException(ResponseCodeEnum.MENU_CHILDREN_EXISTS);
       }
       this.deleteResourceById(resourceId);
       return;
    }
    public void editResource1(ResourceEditDto resourceEditDto){
       Resource resource;
       if ((resource = this.selectResourceById(resourceEditDto.getResourceId())) == null) {
          throw new BizException(ResponseCodeEnum.DATA_NOT_EXISTS);
       }
       if (StringUtils.isNotBlank(resourceEditDto.getUrl())) {
          resource.setAclass("J_menuItem");
       }else {
          resource.setAclass(null);
       }
       this.updateResourceById1(this.resourceMapping.editDto2Entity(resourceEditDto, resource));
       return;
    }
    public List getAllResourceTrees(){
       List resources = this.selectAllResourceList();
       return this.transResourceTreeJson(resources, "0");
    }
    public List getAllResourceTreesList(){
       List menuList = this.menuDao.findByOrderByMenuOrder();
       List resourceList = this.selectAllResourceList();
       List resourceTreeResDtoList = this.transResourceTreeJsonList(menuList, resourceList, "0");
       return resourceTreeResDtoList;
    }
    public List getResourceRTreeList(){
       List menuList = this.menuDao.findByOrderByMenuOrder();
       List resourceList = this.selectAllResourceList();
       List resourceIndexRDtoList = new ArrayList();
       this.transTreeResourceRList(resourceIndexRDtoList, menuList, resourceList, 1, "0");
       return resourceIndexRDtoList;
    }
    public List getResourceTreeList(){
       List resourceList = this.selectAllResourceList();
       List resourceIndexDtoList = new ArrayList();
       this.transTreeResourceList(resourceIndexDtoList, resourceList, 1, "0");
       return resourceIndexDtoList;
    }
    public List getUserResources(){
       Set resourceIdList = new HashSet();
       List roleIds = this.userRoleService.roleIdsByUserCode(ThreadLocalContextHolder.getContext().getUserInfo().getUserCode());
       Iterator iterator = roleIds.iterator();
       while (iterator.hasNext()) {
          List resourceIds = this.roleResourceMapper.getResourceIdsByRoleCode(iterator.next());
          resourceIdList.addAll(resourceIds);
       }
       ArrayList uArrayList = new ArrayList();
       if (!CollectionUtils.isEmpty(resourceIdList)) {
          QueryWrapper queryWrapper = new QueryWrapper();
          queryWrapper.in("resource_id", resourceIdList);
          uArrayList = this.resourceMapper.selectList(queryWrapper);
       }
       return this.resourceMapping.toResDtoList(uArrayList);
    }
    public boolean getUserRoleResources(String path){
       List resourceUrlList = new ArrayList();
       List roleIds = this.userRoleService.roleIdsByUserCode(ThreadLocalContextHolder.getContext().getUserInfo().getUserCode());
       Iterator iterator = roleIds.iterator();
       while (iterator.hasNext()) {
          List resourceUrls = this.roleResourceMapper.getUserRoleResources(iterator.next());
          resourceUrlList.addAll(resourceUrls);
       }
       boolean b = (resourceUrlList.contains(path))? true: false;
       return b;
    }
}
