package com.taikang.fly.check.dto.menu.MenuAddDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class MenuAddDto implements Serializable	// class@00018d from classes.dex
{
    private String iclass;
    private String isMenu;
    private String menuDesc;
    private String menuName;
    private String menuOrder;
    private String moduleCode;
    private String parentId;
    private String url;
    private static final long serialVersionUID = 0x21e31acb44c4c6a7;

    public void MenuAddDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MenuAddDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof MenuAddDto){
          b = false;
       }else {
          MenuAddDto menuAddDto = o;
          if (!menuAddDto.canEqual(this)) {
             b = false;
          }else {
             String menuName = this.getMenuName();
             String menuName1 = menuAddDto.getMenuName();
             if (menuName == null) {
                if (menuName1 != null) {
                   b = false;
                }
             }else if(menuName.equals(menuName1)){
             }
             String menuDesc = this.getMenuDesc();
             String menuDesc1 = menuAddDto.getMenuDesc();
             if (menuDesc == null) {
                if (menuDesc1 != null) {
                   b = false;
                }
             }else if(menuDesc.equals(menuDesc1)){
             }
             String url = this.getUrl();
             String url1 = menuAddDto.getUrl();
             if (url == null) {
                if (url1 != null) {
                   b = false;
                }
             }else if(url.equals(url1)){
             }
             String iclass = this.getIclass();
             String iclass1 = menuAddDto.getIclass();
             if (iclass == null) {
                if (iclass1 != null) {
                   b = false;
                }
             }else if(iclass.equals(iclass1)){
             }
             String menuOrder = this.getMenuOrder();
             String menuOrder1 = menuAddDto.getMenuOrder();
             if (menuOrder == null) {
                if (menuOrder1 != null) {
                   b = false;
                }
             }else if(menuOrder.equals(menuOrder1)){
             }
             String parentId = this.getParentId();
             String parentId1 = menuAddDto.getParentId();
             if (parentId == null) {
                if (parentId1 != null) {
                label_009a :
                   b = false;
                }
             }else if(parentId.equals(parentId1)){
             }
             String isMenu = this.getIsMenu();
             String isMenu1 = menuAddDto.getIsMenu();
             if (isMenu == null) {
                if (isMenu1 != null) {
                   b = false;
                }
             }else if(isMenu.equals(isMenu1)){
             }
             String moduleCode = this.getModuleCode();
             String moduleCode1 = menuAddDto.getModuleCode();
             if (moduleCode == null) {
                if (moduleCode1 != null) {
                label_00c8 :
                   b = false;
                }
             }else if(moduleCode.equals(moduleCode1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getIclass(){
       return this.iclass;
    }
    public String getIsMenu(){
       return this.isMenu;
    }
    public String getMenuDesc(){
       return this.menuDesc;
    }
    public String getMenuName(){
       return this.menuName;
    }
    public String getMenuOrder(){
       return this.menuOrder;
    }
    public String getModuleCode(){
       return this.moduleCode;
    }
    public String getParentId(){
       return this.parentId;
    }
    public String getUrl(){
       return this.url;
    }
    public int hashCode(){
       String $menuName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($menuName = this.getMenuName()) == null)? i: $menuName.hashCode();
       result = i1 + 59;
       String $menuDesc = this.getMenuDesc();
       int i2 = result * 59;
       i1 = ($menuDesc == null)? i: $menuDesc.hashCode();
       result = i2 + i1;
       String $url = this.getUrl();
       i2 = result * 59;
       i1 = ($url == null)? i: $url.hashCode();
       result = i2 + i1;
       String $iclass = this.getIclass();
       i2 = result * 59;
       i1 = ($iclass == null)? i: $iclass.hashCode();
       result = i2 + i1;
       String $menuOrder = this.getMenuOrder();
       i2 = result * 59;
       i1 = ($menuOrder == null)? i: $menuOrder.hashCode();
       result = i2 + i1;
       String parentId = this.getParentId();
       i2 = result * 59;
       i1 = (parentId == null)? i: parentId.hashCode();
       String isMenu = this.getIsMenu();
       i2 = (i2 + i1) * 59;
       i1 = (isMenu == null)? i: isMenu.hashCode();
       String moduleCode = this.getModuleCode();
       i1 = (i2 + i1) * 59;
       if (moduleCode != null) {
          i = moduleCode.hashCode();
       }
       return (i1 + i);
    }
    public void setIclass(String iclass){
       this.iclass = iclass;
    }
    public void setIsMenu(String isMenu){
       this.isMenu = isMenu;
    }
    public void setMenuDesc(String menuDesc){
       this.menuDesc = menuDesc;
    }
    public void setMenuName(String menuName){
       this.menuName = menuName;
    }
    public void setMenuOrder(String menuOrder){
       this.menuOrder = menuOrder;
    }
    public void setModuleCode(String moduleCode){
       this.moduleCode = moduleCode;
    }
    public void setParentId(String parentId){
       this.parentId = parentId;
    }
    public void setUrl(String url){
       this.url = url;
    }
    public String toString(){
       return "MenuAddDto\(menuName="+this.getMenuName()+", menuDesc="+this.getMenuDesc()+", url="+this.getUrl()+", iclass="+this.getIclass()+", menuOrder="+this.getMenuOrder()+", parentId="+this.getParentId()+", isMenu="+this.getIsMenu()+", moduleCode="+this.getModuleCode()+"\)";
    }
}
