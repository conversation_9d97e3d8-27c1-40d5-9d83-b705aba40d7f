package com.taikang.fly.check.mybatis.dao.MfromtOfficeConfigMapper;
import java.lang.String;
import java.lang.Long;
import com.taikang.fly.check.mybatis.domain.MfromtOfficeConfig;
import java.util.Map;
import java.util.List;

public interface abstract MfromtOfficeConfigMapper	// class@000204 from classes.dex
{

    void deleteByBusinessKey(String p0);
    int deleteByPrimaryKey(Long p0);
    int insert(MfromtOfficeConfig p0);
    int insertSelective(MfromtOfficeConfig p0);
    List queryList(Map p0);
    MfromtOfficeConfig selectByPrimaryKey(Long p0);
    int updateByPrimaryKey(MfromtOfficeConfig p0);
    int updateByPrimaryKeySelective(MfromtOfficeConfig p0);
}
