package com.taikang.fly.check.mybatis.dao.DrgAnalysisSumMapper;
import java.lang.String;
import java.util.List;
import com.taikang.fly.check.vo.drg.DrgRuleStatisticsAllVO;

public interface abstract DrgAnalysisSumMapper	// class@0001e5 from classes.dex
{

    List getFocusDepartment(String p0);
    List getFocusDiseaseGroup(String p0);
    List getRuleStatistics(String p0,String p1);
    DrgRuleStatisticsAllVO getRuleStatisticsAll(String p0,String p1);
}
