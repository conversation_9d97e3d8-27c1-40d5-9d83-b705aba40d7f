package com.taikang.fly.check.dto.businesscolconfig.ColConfigQueryDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ColConfigQueryDto implements Serializable	// class@0000b9 from classes.dex
{
    private String businessKey;
    private String businessName;
    private String columnComment;
    private String columnName;
    private static final long serialVersionUID = 0x1;

    public void ColConfigQueryDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ColConfigQueryDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof ColConfigQueryDto) {
             b = false;
          }else {
             ColConfigQueryDto uColConfigQu = o;
             if (!uColConfigQu.canEqual(this)) {
                b = false;
             }else {
                String businessKey = this.getBusinessKey();
                String businessKey1 = uColConfigQu.getBusinessKey();
                if (businessKey == null) {
                   if (businessKey1 != null) {
                      b = false;
                   }
                }else if(businessKey.equals(businessKey1)){
                }
                String businessName = this.getBusinessName();
                String businessName1 = uColConfigQu.getBusinessName();
                if (businessName == null) {
                   if (businessName1 != null) {
                      b = false;
                   }
                }else if(businessName.equals(businessName1)){
                }
                String columnName = this.getColumnName();
                String columnName1 = uColConfigQu.getColumnName();
                if (columnName == null) {
                   if (columnName1 != null) {
                      b = false;
                   }
                }else if(columnName.equals(columnName1)){
                }
                String columnCommen = this.getColumnComment();
                String columnCommen1 = uColConfigQu.getColumnComment();
                if (columnCommen == null) {
                   if (columnCommen1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!columnCommen.equals(columnCommen1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getBusinessKey(){
       return this.businessKey;
    }
    public String getBusinessName(){
       return this.businessName;
    }
    public String getColumnComment(){
       return this.columnComment;
    }
    public String getColumnName(){
       return this.columnName;
    }
    public int hashCode(){
       String $businessKey;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($businessKey = this.getBusinessKey()) == null)? i: $businessKey.hashCode();
       result = i1 + 59;
       String $businessName = this.getBusinessName();
       int i2 = result * 59;
       i1 = ($businessName == null)? i: $businessName.hashCode();
       result = i2 + i1;
       String $columnName = this.getColumnName();
       i2 = result * 59;
       i1 = ($columnName == null)? i: $columnName.hashCode();
       result = i2 + i1;
       String $columnComment = this.getColumnComment();
       i1 = result * 59;
       if ($columnComment != null) {
          i = $columnComment.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setBusinessKey(String businessKey){
       this.businessKey = businessKey;
    }
    public void setBusinessName(String businessName){
       this.businessName = businessName;
    }
    public void setColumnComment(String columnComment){
       this.columnComment = columnComment;
    }
    public void setColumnName(String columnName){
       this.columnName = columnName;
    }
    public String toString(){
       return "ColConfigQueryDto\(businessKey="+this.getBusinessKey()+", businessName="+this.getBusinessName()+", columnName="+this.getColumnName()+", columnComment="+this.getColumnComment()+"\)";
    }
}
