package com.taikang.fly.check.mybatis.dao.HospitalDataPoolMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import com.taikang.fly.check.dto.hospitaldatapool.DataSummaryDto;
import java.util.List;

public interface abstract HospitalDataPoolMapper implements BaseMapper	// class@0001fc from classes.dex
{

    DataSummaryDto getDataMzSummary(String p0,String p1);
    DataSummaryDto getDataZySummary(String p0,String p1);
    String getMaxYear();
    List getMzCategory(String p0,String p1);
    List getMzCategoryExpenses(String p0,String p1);
    List getMzDepartment(String p0,String p1);
    List getMzDepartmentCategory(String p0,String p1);
    List getMzDiagnosisOfDayList(String p0,String p1);
    List getMzWholeSubjectList(String p0,String p1,String p2);
    List getYear(String p0);
    List getZhAbnormalDataList(String p0,String p1);
    List getZhCategoryExpensesList(String p0,String p1);
    List getZhComprehensiveCostList(String p0,String p1);
    DataSummaryDto getZhDataSummary(String p0,String p1);
    List getZhDiagnosisReimbursementList(String p0,String p1);
    List getZhDrugsReimbursementList(String p0,String p1);
    List getZhHighReimbursementList(String p0,String p1);
    List getZhLowReimbursementList(String p0,String p1);
    List getZhNursingDtoReimbursementList(String p0,String p1);
    List getZyCategory(String p0,String p1);
    List getZyCategoryExpenses(String p0,String p1);
    List getZyDepartment(String p0,String p1);
    List getZyDepartmentCategory(String p0,String p1);
    List getZyDiagnosisOfDayList(String p0,String p1);
    List getZyWholeSubjectList(String p0,String p1,String p2);
}
