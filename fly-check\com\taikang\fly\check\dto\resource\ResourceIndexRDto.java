package com.taikang.fly.check.dto.resource.ResourceIndexRDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ResourceIndexRDto implements Serializable	// class@0001af from classes.dex
{
    private String aclass;
    private String description;
    private String expanded;
    private String iclass;
    private String icon;
    private String id;
    private String isLeaf;
    private String isMenu;
    private String isMenuORresource;
    private int level;
    private String name;
    private String order;
    private String parentId;
    private String permission;
    private String resourceType;
    private String signature;
    private String url;
    private static final long serialVersionUID = 0x21e31acb44c4c6a7;

    public void ResourceIndexRDto(){
       super();
       this.expanded = "false";
       this.isLeaf = "false";
    }
    protected boolean canEqual(Object other){
       return other instanceof ResourceIndexRDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ResourceIndexRDto){
          b = false;
       }else {
          ResourceIndexRDto resourceInde = o;
          if (!resourceInde.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = resourceInde.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String name = this.getName();
             String name1 = resourceInde.getName();
             if (name == null) {
                if (name1 != null) {
                   b = false;
                }
             }else if(name.equals(name1)){
             }
             String description = this.getDescription();
             String description1 = resourceInde.getDescription();
             if (description == null) {
                if (description1 != null) {
                   b = false;
                }
             }else if(description.equals(description1)){
             }
             String url = this.getUrl();
             String url1 = resourceInde.getUrl();
             if (url == null) {
                if (url1 != null) {
                   b = false;
                }
             }else if(url.equals(url1)){
             }
             String iclass = this.getIclass();
             String iclass1 = resourceInde.getIclass();
             if (iclass == null) {
                if (iclass1 != null) {
                   b = false;
                }
             }else if(iclass.equals(iclass1)){
             }
             String aclass = this.getAclass();
             String aclass1 = resourceInde.getAclass();
             if (aclass == null) {
                if (aclass1 != null) {
                label_00a5 :
                   b = false;
                }
             }else if(aclass.equals(aclass1)){
             }
             String order = this.getOrder();
             String order1 = resourceInde.getOrder();
             if (order == null) {
                if (order1 != null) {
                   b = false;
                }
             }else if(order.equals(order1)){
             }
             String expanded = this.getExpanded();
             String expanded1 = resourceInde.getExpanded();
             if (expanded == null) {
                if (expanded1 != null) {
                   b = false;
                }
             }else if(expanded.equals(expanded1)){
             }
             String isLeaf = this.getIsLeaf();
             String isLeaf1 = resourceInde.getIsLeaf();
             if (isLeaf == null) {
                if (isLeaf1 != null) {
                label_00ed :
                   b = false;
                }
             }else if(isLeaf.equals(isLeaf1)){
             }
             if (this.getLevel() != resourceInde.getLevel()) {
                b = false;
             }else {
                String parentId = this.getParentId();
                String parentId1 = resourceInde.getParentId();
                if (parentId == null) {
                   if (parentId1 != null) {
                      b = false;
                   }
                }else if(parentId.equals(parentId1)){
                }
                String isMenuORreso = this.getIsMenuORresource();
                String isMenuORreso1 = resourceInde.getIsMenuORresource();
                if (isMenuORreso == null) {
                   if (isMenuORreso1 != null) {
                   label_012f :
                      b = false;
                   }
                }else if(isMenuORreso.equals(isMenuORreso1)){
                }
                String permission = this.getPermission();
                String permission1 = resourceInde.getPermission();
                if (permission == null) {
                   if (permission1 != null) {
                      b = false;
                   }
                }else if(permission.equals(permission1)){
                }
                String icon = this.getIcon();
                String icon1 = resourceInde.getIcon();
                if (icon == null) {
                   if (icon1 != null) {
                   label_015f :
                      b = false;
                   }
                }else if(icon.equals(icon1)){
                }
                String signature = this.getSignature();
                String signature1 = resourceInde.getSignature();
                if (signature == null) {
                   if (signature1 != null) {
                      b = false;
                   }
                }else if(signature.equals(signature1)){
                }
                String resourceType = this.getResourceType();
                String resourceType1 = resourceInde.getResourceType();
                if (resourceType == null) {
                   if (resourceType1 != null) {
                   label_0191 :
                      b = false;
                   }
                }else if(resourceType.equals(resourceType1)){
                }
                String isMenu = this.getIsMenu();
                String isMenu1 = resourceInde.getIsMenu();
                if (isMenu == null) {
                   if (isMenu1 != null) {
                      b = false;
                   }
                }else if(isMenu.equals(isMenu1)){
                }
                b = true;
             }
          }
       }
       return b;
    }
    public String getAclass(){
       return this.aclass;
    }
    public String getDescription(){
       return this.description;
    }
    public String getExpanded(){
       return this.expanded;
    }
    public String getIclass(){
       return this.iclass;
    }
    public String getIcon(){
       return this.icon;
    }
    public String getId(){
       return this.id;
    }
    public String getIsLeaf(){
       return this.isLeaf;
    }
    public String getIsMenu(){
       return this.isMenu;
    }
    public String getIsMenuORresource(){
       return this.isMenuORresource;
    }
    public int getLevel(){
       return this.level;
    }
    public String getName(){
       return this.name;
    }
    public String getOrder(){
       return this.order;
    }
    public String getParentId(){
       return this.parentId;
    }
    public String getPermission(){
       return this.permission;
    }
    public String getResourceType(){
       return this.resourceType;
    }
    public String getSignature(){
       return this.signature;
    }
    public String getUrl(){
       return this.url;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $name = this.getName();
       int i1 = result * 59;
       i = ($name == null)? 43: $name.hashCode();
       result = i1 + i;
       String $description = this.getDescription();
       i1 = result * 59;
       i = ($description == null)? 43: $description.hashCode();
       result = i1 + i;
       String $url = this.getUrl();
       i1 = result * 59;
       i = ($url == null)? 43: $url.hashCode();
       result = i1 + i;
       String $iclass = this.getIclass();
       i1 = result * 59;
       i = ($iclass == null)? 43: $iclass.hashCode();
       result = i1 + i;
       String aclass = this.getAclass();
       i1 = result * 59;
       i = (aclass == null)? 43: aclass.hashCode();
       String order = this.getOrder();
       i1 = (i1 + i) * 59;
       i = (order == null)? 43: order.hashCode();
       String expanded = this.getExpanded();
       i1 = (i1 + i) * 59;
       i = (expanded == null)? 43: expanded.hashCode();
       String isLeaf = this.getIsLeaf();
       i1 = (i1 + i) * 59;
       i = (isLeaf == null)? 43: isLeaf.hashCode();
       String parentId = this.getParentId();
       i1 = (((i1 + i) * 59) + this.getLevel()) * 59;
       i = (parentId == null)? 43: parentId.hashCode();
       String isMenuORreso = this.getIsMenuORresource();
       i1 = (i1 + i) * 59;
       i = (isMenuORreso == null)? 43: isMenuORreso.hashCode();
       String permission = this.getPermission();
       i1 = (i1 + i) * 59;
       i = (permission == null)? 43: permission.hashCode();
       String icon = this.getIcon();
       i1 = (i1 + i) * 59;
       i = (icon == null)? 43: icon.hashCode();
       String signature = this.getSignature();
       i1 = (i1 + i) * 59;
       i = (signature == null)? 43: signature.hashCode();
       String resourceType = this.getResourceType();
       i1 = (i1 + i) * 59;
       i = (resourceType == null)? 43: resourceType.hashCode();
       String isMenu = this.getIsMenu();
       i1 = (i1 + i) * 59;
       i = (isMenu == null)? 43: isMenu.hashCode();
       return (i1 + i);
    }
    public void setAclass(String aclass){
       this.aclass = aclass;
    }
    public void setDescription(String description){
       this.description = description;
    }
    public void setExpanded(String expanded){
       this.expanded = expanded;
    }
    public void setIclass(String iclass){
       this.iclass = iclass;
    }
    public void setIcon(String icon){
       this.icon = icon;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setIsLeaf(String isLeaf){
       this.isLeaf = isLeaf;
    }
    public void setIsMenu(String isMenu){
       this.isMenu = isMenu;
    }
    public void setIsMenuORresource(String isMenuORresource){
       this.isMenuORresource = isMenuORresource;
    }
    public void setLevel(int level){
       this.level = level;
    }
    public void setName(String name){
       this.name = name;
    }
    public void setOrder(String order){
       this.order = order;
    }
    public void setParentId(String parentId){
       this.parentId = parentId;
    }
    public void setPermission(String permission){
       this.permission = permission;
    }
    public void setResourceType(String resourceType){
       this.resourceType = resourceType;
    }
    public void setSignature(String signature){
       this.signature = signature;
    }
    public void setUrl(String url){
       this.url = url;
    }
    public String toString(){
       return "ResourceIndexRDto\(id="+this.getId()+", name="+this.getName()+", description="+this.getDescription()+", url="+this.getUrl()+", iclass="+this.getIclass()+", aclass="+this.getAclass()+", order="+this.getOrder()+", expanded="+this.getExpanded()+", isLeaf="+this.getIsLeaf()+", level="+this.getLevel()+", parentId="+this.getParentId()+", isMenuORresource="+this.getIsMenuORresource()+", permission="+this.getPermission()+", icon="+this.getIcon()+", signature="+this.getSignature()+", resourceType="+this.getResourceType()+", isMenu="+this.getIsMenu()+"\)";
    }
}
