package com.taikang.fly.check.mybatis.domain.ModelVerfData;
import java.lang.Object;
import java.lang.String;
import java.time.LocalDateTime;
import java.lang.StringBuilder;

public class ModelVerfData	// class@00025e from classes.dex
{
    private String id;
    private char status;
    private String userId;
    private LocalDateTime verfTime;

    public void ModelVerfData(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ModelVerfData;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof ModelVerfData) {
             b = false;
          }else {
             ModelVerfData modelVerfDat = o;
             if (!modelVerfDat.canEqual(this)) {
                b = false;
             }else {
                String id = this.getId();
                String id1 = modelVerfDat.getId();
                if (id == null) {
                   if (id1 != null) {
                      b = false;
                   }
                }else if(id.equals(id1)){
                }
                String userId = this.getUserId();
                String userId1 = modelVerfDat.getUserId();
                if (userId == null) {
                   if (userId1 != null) {
                      b = false;
                   }
                }else if(userId.equals(userId1)){
                }
                if (this.getStatus() != modelVerfDat.getStatus()) {
                   b = false;
                }else {
                   LocalDateTime verfTime = this.getVerfTime();
                   LocalDateTime verfTime1 = modelVerfDat.getVerfTime();
                   if (verfTime == null) {
                      if (verfTime1 == null) {
                      label_0004 :
                         return b;
                      }
                   }else if(!verfTime.equals(verfTime1)){
                   }
                   b = false;
                   goto label_0004 ;
                }
             }
          }
       }
    }
    public String getId(){
       return this.id;
    }
    public char getStatus(){
       return this.status;
    }
    public String getUserId(){
       return this.userId;
    }
    public LocalDateTime getVerfTime(){
       return this.verfTime;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $userId = this.getUserId();
       int i2 = result * 59;
       i1 = ($userId == null)? i: $userId.hashCode();
       result = i2 + i1;
       result = (result * 59) + this.getStatus();
       LocalDateTime $verfTime = this.getVerfTime();
       i1 = result * 59;
       if ($verfTime != null) {
          i = $verfTime.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setStatus(char status){
       this.status = status;
    }
    public void setUserId(String userId){
       this.userId = userId;
    }
    public void setVerfTime(LocalDateTime verfTime){
       this.verfTime = verfTime;
    }
    public String toString(){
       return "ModelVerfData\(id="+this.getId()+", userId="+this.getUserId()+", status="+this.getStatus()+", verfTime="+this.getVerfTime()+"\)";
    }
}
