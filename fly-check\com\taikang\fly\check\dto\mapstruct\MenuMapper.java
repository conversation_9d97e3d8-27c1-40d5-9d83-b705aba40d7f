package com.taikang.fly.check.dto.mapstruct.MenuMapper;
import com.taikang.fly.check.dto.menu.MenuAddDto;
import com.taikang.fly.check.mybatis.domain.Menu;
import com.taikang.fly.check.dto.menu.MenuDto;
import com.taikang.fly.check.dto.menu.MenuEditDto;
import java.lang.String;
import com.taikang.fly.check.dto.menu.MenuIndexDto;
import com.taikang.fly.check.dto.resource.ResourceIndexRDto;

public interface abstract MenuMapper	// class@000165 from classes.dex
{

    Menu addMenuToMenu(MenuAddDto p0);
    MenuDto addToList(Menu p0,int p1);
    Menu editMenuToMenu(MenuEditDto p0,Menu p1);
    MenuIndexDto menuToDto(Menu p0,int p1,String p2);
    ResourceIndexRDto menuToResourceIndexRDto(Menu p0,int p1,String p2);
}
