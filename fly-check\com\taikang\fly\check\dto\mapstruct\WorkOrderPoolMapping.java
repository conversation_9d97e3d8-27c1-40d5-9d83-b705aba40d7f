package com.taikang.fly.check.dto.mapstruct.WorkOrderPoolMapping;
import com.taikang.fly.check.dto.workorderpool.WorkOrderPoolAddDto;
import com.taikang.fly.check.mybatis.domain.WorkOrderPool;
import com.taikang.fly.check.dto.workorderpool.WorkOrderPoolImportDto;
import java.util.List;
import com.taikang.fly.check.dto.workorderpool.WorkOrderPoolEditDto;
import com.taikang.fly.check.dto.workorderpool.WorkOrderPoolRespDto;
import com.taikang.fly.check.mybatis.domain.FlyRule;

public interface abstract WorkOrderPoolMapping	// class@000181 from classes.dex
{

    WorkOrderPool addToEntry(WorkOrderPoolAddDto p0);
    WorkOrderPool dtoToDomain(WorkOrderPoolImportDto p0);
    List dtosToDomains(List p0);
    WorkOrderPool editToEntry(WorkOrderPoolEditDto p0);
    List entityToDtos(List p0);
    WorkOrderPoolRespDto entryToDto(WorkOrderPool p0);
    FlyRule workOrderPoolToFlyRule(WorkOrderPool p0);
}
