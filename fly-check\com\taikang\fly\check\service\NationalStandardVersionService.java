package com.taikang.fly.check.service.NationalStandardVersionService;
import java.lang.Integer;
import com.taikang.fly.check.mybatis.domain.NationalStandardVersionDto;
import com.taikang.fly.check.comm.NativePage;

public interface abstract NationalStandardVersionService	// class@0002f2 from classes.dex
{

    NativePage queryHerbalList(Integer p0,Integer p1,NationalStandardVersionDto p2);
    NativePage queryICD10List(Integer p0,Integer p1,NationalStandardVersionDto p2);
    NativePage queryICD9List(Integer p0,Integer p1,NationalStandardVersionDto p2);
    NativePage queryPatentList(Integer p0,Integer p1,NationalStandardVersionDto p2);
    NativePage queryTreatmentList(Integer p0,Integer p1,NationalStandardVersionDto p2);
    NativePage queryWesternList(Integer p0,Integer p1,NationalStandardVersionDto p2);
}
