package org.springframework.boot.loader.Launcher;
import java.lang.Object;
import org.springframework.boot.loader.archive.Archive;
import java.lang.Class;
import java.security.ProtectionDomain;
import java.security.CodeSource;
import java.net.URL;
import java.net.URI;
import java.lang.String;
import java.lang.IllegalStateException;
import java.io.File;
import java.lang.StringBuilder;
import org.springframework.boot.loader.archive.ExplodedArchive;
import org.springframework.boot.loader.archive.JarFileArchive;
import java.util.List;
import java.lang.ClassLoader;
import java.util.ArrayList;
import java.util.Iterator;
import org.springframework.boot.loader.LaunchedURLClassLoader;
import org.springframework.boot.loader.MainMethodRunner;
import org.springframework.boot.loader.jar.JarFile;
import java.lang.Thread;

public abstract class Launcher	// class@000532 from classes.dex
{

    public void Launcher(){
       super();
    }
    protected final Archive createArchive(){
       CodeSource codeSource;
       ExplodedArchive uExplodedArc;
       String str = null;
       ProtectionDomain protectionDomain = this.getClass().getProtectionDomain();
       URI location = ((codeSource = protectionDomain.getCodeSource()) != null)? codeSource.getLocation().toURI(): str;
       if (location != null) {
          str = location.getSchemeSpecificPart();
       }
       if (str == null) {
          throw new IllegalStateException("Unable to determine code source archive");
       }else {
          File uFile = new File(str);
          if (!uFile.exists()) {
             throw new IllegalStateException("Unable to determine code source archive from "+uFile);
          }else if(uFile.isDirectory()){
             uExplodedArc = new ExplodedArchive(uFile);
          }else {
             uExplodedArc = new JarFileArchive(uFile);
          }
          return uExplodedArc;
       }
    }
    protected ClassLoader createClassLoader(List archives){
       List urls = new ArrayList(archives.size());
       Iterator iterator = archives.iterator();
       while (iterator.hasNext()) {
          urls.add(iterator.next().getUrl());
       }
       URL[] uRLArray = new URL[0];
       return this.createClassLoader(urls.toArray(uRLArray));
    }
    protected ClassLoader createClassLoader(URL[] urls){
       return new LaunchedURLClassLoader(urls, this.getClass().getClassLoader());
    }
    protected MainMethodRunner createMainMethodRunner(String mainClass,String[] args,ClassLoader classLoader){
       return new MainMethodRunner(mainClass, args);
    }
    protected abstract List getClassPathArchives();
    protected abstract String getMainClass();
    protected void launch(String[] args){
       JarFile.registerUrlProtocolHandler();
       ClassLoader classLoader = this.createClassLoader(this.getClassPathArchives());
       this.launch(args, this.getMainClass(), classLoader);
    }
    protected void launch(String[] args,String mainClass,ClassLoader classLoader){
       Thread.currentThread().setContextClassLoader(classLoader);
       this.createMainMethodRunner(mainClass, args, classLoader).run();
    }
}
