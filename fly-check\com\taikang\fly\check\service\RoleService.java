package com.taikang.fly.check.service.RoleService;
import java.lang.Object;
import com.taikang.fly.check.dto.system.role.RoleAddDto;
import java.lang.String;
import com.taikang.fly.check.mybatis.domain.Role;
import com.taikang.fly.check.mybatis.dao.RoleMapper;
import com.taikang.fly.check.comm.BizException;
import com.taikang.fly.check.comm.enums.ResponseCodeEnum;
import com.taikang.fly.check.dto.mapstruct.RoleMapping;
import java.lang.Integer;
import java.lang.CharSequence;
import org.apache.commons.lang3.StringUtils;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import com.taikang.fly.check.mybatis.dao.UserRoleMapper;
import java.util.Collection;
import org.springframework.util.CollectionUtils;
import com.taikang.fly.check.mybatis.dao.RoleMenuMapper;
import com.taikang.fly.check.dto.system.role.RoleEditDto;
import com.taikang.fly.check.dto.system.role.RoleSearchDto;
import com.taikang.fly.check.comm.Page;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;

public class RoleService	// class@0002fc from classes.dex
{
    private RoleMapper roleMapper;
    private RoleMapping roleMapping;
    private RoleMenuMapper roleMenuMapper;
    private UserRoleMapper userRoleMapper;

    public void RoleService(){
       super();
    }
    public void addRole(RoleAddDto roleAddDto){
       Role role;
       if ((role = this.roleMapper.findByRoleCode(roleAddDto.getRoleCode())) != null) {
          throw new BizException(ResponseCodeEnum.DATA_CODE_EXISTS);
       }
       this.roleMapper.save(this.roleMapping.roleAddDtoToRole(roleAddDto));
       return;
    }
    public void deleteByRoleId(String roleId){
       Role role;
       if (StringUtils.isBlank(roleId) || !roleId.trim().length()) {
          throw new BizException(ResponseCodeEnum.PARAMETER_ISNULL_ERROR);
       }
       if ((role = this.roleMapper.getByRoleId(roleId)) == null) {
          throw new BizException(ResponseCodeEnum.DATA_NOT_EXISTS);
       }
       HashMap hashMap = new HashMap(1);
       hashMap.put("roleCode", role.getRoleCode());
       if (!CollectionUtils.isEmpty(this.userRoleMapper.getUserListByRoleCode(hashMap))) {
          throw new BizException(ResponseCodeEnum.ROLE_USER_EXISTS);
       }
       this.roleMapper.deleteByRoleId(roleId);
       this.roleMenuMapper.deleteByRoleCode(hashMap);
       return;
    }
    public void editRole(RoleEditDto roleEditDto){
       Role role = this.roleMapping.roleEditDtoToRole(roleEditDto);
       this.roleMapper.updateRoleById(role);
    }
    public Page queryPage(Integer page,Integer pageSize,RoleSearchDto roleSearchDto){
       Map paramMap = new HashMap(4);
       paramMap.put("start", Integer.valueOf((pageSize.intValue() * (page.intValue() - 1))));
       paramMap.put("pageSize", pageSize);
       if (roleSearchDto != null) {
          if (StringUtils.isNotBlank(roleSearchDto.getRoleCode())) {
             paramMap.put("roleCode", roleSearchDto.getRoleCode());
          }
          if (StringUtils.isNotBlank(roleSearchDto.getName())) {
             paramMap.put("name", roleSearchDto.getName());
          }
       }
       PageMethod.startPage(page.intValue(), pageSize.intValue());
       return new Page(this.roleMapping.listRoleMapToListDto(this.roleMapper.queryListByParams(paramMap)), this.roleMapper.getRolePageCount(paramMap), pageSize, page);
    }
    public List registerRoleList(){
       List roleList = this.roleMapper.selectList(new QueryWrapper().ne("ROLE_CODE", "system"));
       return this.roleMapping.rolesToRolesDto(roleList);
    }
}
