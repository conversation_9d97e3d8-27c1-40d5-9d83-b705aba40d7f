package com.taikang.fly.check.utils.DBUtils.ColumnDetailsAttribute;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import java.lang.StringBuilder;

public class ColumnDetailsAttribute implements Serializable	// class@000332 from classes.dex
{
    private String columnComment;
    private String columnName;
    private String columnType;
    private String datascale;
    private Integer excelNum;
    private String isNullable;
    private static final long serialVersionUID = 0xcf1f28defc0b8f6c;

    public void ColumnDetailsAttribute(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ColumnDetailsAttribute;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ColumnDetailsAttribute){
          b = false;
       }else {
          ColumnDetailsAttribute uColumnDetai = o;
          if (!uColumnDetai.canEqual(this)) {
             b = false;
          }else {
             String columnName = this.getColumnName();
             String columnName1 = uColumnDetai.getColumnName();
             if (columnName == null) {
                if (columnName1 != null) {
                   b = false;
                }
             }else if(columnName.equals(columnName1)){
             }
             String columnType = this.getColumnType();
             String columnType1 = uColumnDetai.getColumnType();
             if (columnType == null) {
                if (columnType1 != null) {
                   b = false;
                }
             }else if(columnType.equals(columnType1)){
             }
             String isNullable = this.getIsNullable();
             String isNullable1 = uColumnDetai.getIsNullable();
             if (isNullable == null) {
                if (isNullable1 != null) {
                   b = false;
                }
             }else if(isNullable.equals(isNullable1)){
             }
             String columnCommen = this.getColumnComment();
             String columnCommen1 = uColumnDetai.getColumnComment();
             if (columnCommen == null) {
                if (columnCommen1 != null) {
                   b = false;
                }
             }else if(columnCommen.equals(columnCommen1)){
             }
             String datascale = this.getDatascale();
             String datascale1 = uColumnDetai.getDatascale();
             if (datascale == null) {
                if (datascale1 != null) {
                   b = false;
                }
             }else if(datascale.equals(datascale1)){
             }
             Integer excelNum = this.getExcelNum();
             Integer excelNum1 = uColumnDetai.getExcelNum();
             if (excelNum == null) {
                if (excelNum1 != null) {
                   b = false;
                }
             }else if(excelNum.equals(excelNum1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getColumnComment(){
       return this.columnComment;
    }
    public String getColumnName(){
       return this.columnName;
    }
    public String getColumnType(){
       return this.columnType;
    }
    public String getDatascale(){
       return this.datascale;
    }
    public Integer getExcelNum(){
       return this.excelNum;
    }
    public String getIsNullable(){
       return this.isNullable;
    }
    public int hashCode(){
       String $columnName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($columnName = this.getColumnName()) == null)? i: $columnName.hashCode();
       result = i1 + 59;
       String $columnType = this.getColumnType();
       int i2 = result * 59;
       i1 = ($columnType == null)? i: $columnType.hashCode();
       result = i2 + i1;
       String $isNullable = this.getIsNullable();
       i2 = result * 59;
       i1 = ($isNullable == null)? i: $isNullable.hashCode();
       result = i2 + i1;
       String $columnComment = this.getColumnComment();
       i2 = result * 59;
       i1 = ($columnComment == null)? i: $columnComment.hashCode();
       result = i2 + i1;
       String $datascale = this.getDatascale();
       i2 = result * 59;
       i1 = ($datascale == null)? i: $datascale.hashCode();
       result = i2 + i1;
       Integer excelNum = this.getExcelNum();
       i1 = result * 59;
       if (excelNum != null) {
          i = excelNum.hashCode();
       }
       return (i1 + i);
    }
    public void setColumnComment(String columnComment){
       this.columnComment = columnComment;
    }
    public void setColumnName(String columnName){
       this.columnName = columnName;
    }
    public void setColumnType(String columnType){
       this.columnType = columnType;
    }
    public void setDatascale(String datascale){
       this.datascale = datascale;
    }
    public void setExcelNum(Integer excelNum){
       this.excelNum = excelNum;
    }
    public void setIsNullable(String isNullable){
       this.isNullable = isNullable;
    }
    public String toString(){
       return "ColumnDetailsAttribute\(columnName="+this.getColumnName()+", columnType="+this.getColumnType()+", isNullable="+this.getIsNullable()+", columnComment="+this.getColumnComment()+", datascale="+this.getDatascale()+", excelNum="+this.getExcelNum()+"\)";
    }
}
