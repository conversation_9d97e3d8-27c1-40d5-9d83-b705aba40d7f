package com.taikang.fly.check.rest.WorkOrderPoolController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import com.taikang.fly.check.dto.workorderpool.WorkOrderPoolAddDto;
import com.taikang.fly.check.comm.CommResponse;
import com.taikang.fly.check.service.WorkOrderPoolService;
import java.lang.String;
import java.lang.Integer;
import javax.servlet.http.HttpServletResponse;
import com.taikang.fly.check.dto.workorderpool.WorkOrderPoolEditDto;
import com.taikang.fly.check.dto.RmpResponse;
import java.util.Map;
import com.taikang.fly.check.dto.workorderpool.WorkOrderPoolRespDto;
import org.springframework.web.multipart.MultipartFile;
import com.taikang.fly.check.dto.workorderpool.WorkOrderPoolQueryDto;
import com.taikang.fly.check.comm.NativePage;
import com.taikang.fly.check.dto.workorderpool.WorkOrderPolStatusDto;

public class WorkOrderPoolController	// class@0002b9 from classes.dex
{
    private WorkOrderPoolService workOrderPoolService;
    private static final Logger log;

    static {
       WorkOrderPoolController.log = LoggerFactory.getLogger(WorkOrderPoolController.class);
    }
    public void WorkOrderPoolController(){
       super();
    }
    public CommResponse addWorkOrderPool(WorkOrderPoolAddDto workOrderPoolAddDto){
       return this.workOrderPoolService.addWorkOrderPool(workOrderPoolAddDto);
    }
    public CommResponse deleteWorkOrderPool(String id){
       return CommResponse.success(this.workOrderPoolService.deleteWorkOrderPool(id));
    }
    public void downloadTemplate(HttpServletResponse response){
       this.workOrderPoolService.downloadTemplate(response);
    }
    public RmpResponse editWorkOrderPool(WorkOrderPoolEditDto workOrderPoolEditDto){
       this.workOrderPoolService.editWorkOrderPool(workOrderPoolEditDto);
       return RmpResponse.success();
    }
    public void exportInvalidInfoExcel(HttpServletResponse response,Map duringWRInvalidInfoMap){
       this.workOrderPoolService.exportInvalidInfoExcel(response, duringWRInvalidInfoMap);
    }
    public CommResponse getWorkOrderPoolById(String id){
       return CommResponse.success(this.workOrderPoolService.getWorkOrderPoolById(id));
    }
    public Map importWorkOrderPoolTemplate(MultipartFile file){
       Map stringObjectMap = this.workOrderPoolService.importWorkOrderPoolTemplate(file);
       return stringObjectMap;
    }
    public CommResponse listPage(Integer page,Integer size,WorkOrderPoolQueryDto workOrderPoolQueryDto){
       return CommResponse.success(this.workOrderPoolService.listForWorkOrederPoool(page, size, workOrderPoolQueryDto));
    }
    public RmpResponse submitWorkOrderPol(WorkOrderPolStatusDto workOrderPolStatusDto){
       Integer integer = this.workOrderPoolService.submitWorkOrderPol(workOrderPolStatusDto);
       return RmpResponse.success(integer);
    }
    public Map templateImport(MultipartFile file){
       Map stringObjectMap = this.workOrderPoolService.templateImportData(file);
       return stringObjectMap;
    }
}
