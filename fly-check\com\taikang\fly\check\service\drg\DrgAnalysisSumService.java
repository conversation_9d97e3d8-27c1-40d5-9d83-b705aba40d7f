package com.taikang.fly.check.service.drg.DrgAnalysisSumService;
import java.lang.String;
import javax.servlet.http.HttpServletResponse;
import com.taikang.fly.check.vo.drg.DrgAnalysisVO;
import java.lang.Integer;
import com.taikang.fly.check.comm.NativePage;
import com.taikang.fly.check.vo.drg.DrgRuleStatisticsAllVO;

public interface abstract DrgAnalysisSumService	// class@000311 from classes.dex
{

    void downRegularDimensionResults(String p0,HttpServletResponse p1);
    DrgAnalysisVO getDrgAnalysisCount();
    NativePage getFocusDepartment(Integer p0,Integer p1);
    NativePage getFocusDiseaseGroup(Integer p0,Integer p1);
    DrgRuleStatisticsAllVO getRuleStatistics(String p0);
}
