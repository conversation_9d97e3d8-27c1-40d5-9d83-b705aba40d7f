package com.taikang.fly.check.config.WebMVCConfig;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import java.lang.String;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistration;

public class WebMVCConfig extends WebMvcConfigurerAdapter	// class@000091 from classes.dex
{

    public void WebMVCConfig(){
       super();
    }
    public void addResourceHandlers(ResourceHandlerRegistry registry){
       String[] stringArray = new String[]{"/swagger-ui.html"};
       String[] stringArray1 = new String[]{"classpath:/META-INF/resources/swagger-ui.html"};
       registry.addResourceHandler(stringArray).addResourceLocations(stringArray1);
       stringArray = new String[]{"/webjars/**"};
       stringArray1 = new String[]{"classpath:/META-INF/resources/webjars/"};
       registry.addResourceHandler(stringArray).addResourceLocations(stringArray1);
    }
}
