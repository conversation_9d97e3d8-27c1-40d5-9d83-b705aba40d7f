package com.taikang.fly.check.dto.jsoncreate.DatasourceDTO;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DatasourceDTO	// class@000138 from classes.dex
{
    private String dbName;
    private String password;
    private String url;
    private String username;

    public void DatasourceDTO(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DatasourceDTO;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DatasourceDTO) {
             b = false;
          }else {
             DatasourceDTO uDatasourceD = o;
             if (!uDatasourceD.canEqual(this)) {
                b = false;
             }else {
                String url = this.getUrl();
                String url1 = uDatasourceD.getUrl();
                if (url == null) {
                   if (url1 != null) {
                      b = false;
                   }
                }else if(url.equals(url1)){
                }
                String username = this.getUsername();
                String username1 = uDatasourceD.getUsername();
                if (username == null) {
                   if (username1 != null) {
                      b = false;
                   }
                }else if(username.equals(username1)){
                }
                String password = this.getPassword();
                String password1 = uDatasourceD.getPassword();
                if (password == null) {
                   if (password1 != null) {
                      b = false;
                   }
                }else if(password.equals(password1)){
                }
                String dbName = this.getDbName();
                String dbName1 = uDatasourceD.getDbName();
                if (dbName == null) {
                   if (dbName1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!dbName.equals(dbName1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getDbName(){
       return this.dbName;
    }
    public String getPassword(){
       return this.password;
    }
    public String getUrl(){
       return this.url;
    }
    public String getUsername(){
       return this.username;
    }
    public int hashCode(){
       String $url;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($url = this.getUrl()) == null)? i: $url.hashCode();
       result = i1 + 59;
       String $username = this.getUsername();
       int i2 = result * 59;
       i1 = ($username == null)? i: $username.hashCode();
       result = i2 + i1;
       String $password = this.getPassword();
       i2 = result * 59;
       i1 = ($password == null)? i: $password.hashCode();
       result = i2 + i1;
       String $dbName = this.getDbName();
       i1 = result * 59;
       if ($dbName != null) {
          i = $dbName.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDbName(String dbName){
       this.dbName = dbName;
    }
    public void setPassword(String password){
       this.password = password;
    }
    public void setUrl(String url){
       this.url = url;
    }
    public void setUsername(String username){
       this.username = username;
    }
    public String toString(){
       return "DatasourceDTO\(url="+this.getUrl()+", username="+this.getUsername()+", password="+this.getPassword()+", dbName="+this.getDbName()+"\)";
    }
}
