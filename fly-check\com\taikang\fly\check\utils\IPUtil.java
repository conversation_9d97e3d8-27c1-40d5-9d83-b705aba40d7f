package com.taikang.fly.check.utils.IPUtil;
import java.lang.Object;
import java.lang.String;
import java.util.Enumeration;
import java.net.NetworkInterface;
import java.lang.CharSequence;
import java.net.InetAddress;
import javax.servlet.http.HttpServletRequest;
import org.springframework.util.StringUtils;
import java.lang.System;

public class IPUtil	// class@00033e from classes.dex
{
    private static String UNKNOWN;
    private static String localhost;

    static {
       IPUtil.UNKNOWN = "unknown";
    }
    public void IPUtil(){
       super();
    }
    public static String getLinuxLocalIp(){
       String ip = "";
       boolean flag = false;
       Enumeration networkInterfaces = NetworkInterface.getNetworkInterfaces();
       while (networkInterfaces.hasMoreElements() && !flag) {
          NetworkInterface networkInter = networkInterfaces.nextElement();
          if (!networkInter.getName().contains("docker") && !networkInter.getName().contains("lo")) {
             Enumeration ips = networkInter.getInetAddresses();
             while (ips.hasMoreElements()) {
                InetAddress inetAddressx = ips.nextElement();
                if (!inetAddressx.isLoopbackAddress()) {
                   String hostAddress = inetAddressx.getHostAddress().toString();
                   if (!hostAddress.contains("::") && (!hostAddress.contains("0:0:") && !hostAddress.contains("fe80"))) {
                      ip = hostAddress;
                      flag = true;
                   }else {
                      continue ;
                   }
                }else {
                   continue ;
                }
             }
          }
       }
       return ip;
    }
    public static String getLocalIp(){
       String windowsLocal = (IPUtil.isWindowsOS())? IPUtil.getWindowsLocalIp(): IPUtil.getLinuxLocalIp();
       return windowsLocal;
    }
    public static String getRemoteHost(HttpServletRequest request){
       String ip = request.getHeader("X-Forwarded-For");
       if (!StringUtils.isEmpty(ip) && !IPUtil.UNKNOWN.equalsIgnoreCase(ip)) {
          ip = ip.split(",")[0];
       }
       if (ip == null || (!ip.length() || IPUtil.UNKNOWN.equalsIgnoreCase(ip))) {
          ip = request.getHeader("Proxy-Client-IP");
       }
       if (ip == null || (!ip.length() || IPUtil.UNKNOWN.equalsIgnoreCase(ip))) {
          ip = request.getHeader("WL-Proxy-Client-IP");
       }
       if (ip == null || (!ip.length() || IPUtil.UNKNOWN.equalsIgnoreCase(ip))) {
          ip = request.getRemoteAddr();
       }
       if ("0:0:0:0:0:0:0:1".equals(ip)) {
          ip = IPUtil.localhost;
       }
       return ip;
    }
    public static String getWindowsLocalIp(){
       return InetAddress.getLocalHost().getHostAddress();
    }
    public static boolean isWindowsOS(){
       String osName;
       boolean isWindosOS = false;
       if ((osName = System.getProperty("os.name")) != null && osName.toLowerCase().indexOf("windows") > -1) {
          isWindosOS = true;
       }
       return isWindosOS;
    }
}
