package com.taikang.fly.check.comm.ScheduledFutureHolder;
import java.lang.Object;
import java.util.concurrent.ScheduledFuture;
import java.lang.Class;
import java.lang.String;
import java.lang.StringBuilder;

public class ScheduledFutureHolder	// class@000078 from classes.dex
{
    private String cron;
    private Class runnableClass;
    private ScheduledFuture scheduledFuture;

    public void ScheduledFutureHolder(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ScheduledFutureHolder;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof ScheduledFutureHolder) {
             b = false;
          }else {
             ScheduledFutureHolder scheduledFut = o;
             if (!scheduledFut.canEqual(this)) {
                b = false;
             }else {
                ScheduledFuture scheduledFut1 = this.getScheduledFuture();
                ScheduledFuture scheduledFut2 = scheduledFut.getScheduledFuture();
                if (scheduledFut1 == null) {
                   if (scheduledFut2 != null) {
                      b = false;
                   }
                }else if(scheduledFut1.equals(scheduledFut2)){
                }
                Class runnableClas = this.getRunnableClass();
                Class runnableClas1 = scheduledFut.getRunnableClass();
                if (runnableClas == null) {
                   if (runnableClas1 != null) {
                      b = false;
                   }
                }else if(runnableClas.equals(runnableClas1)){
                }
                String cron = this.getCron();
                String cron1 = scheduledFut.getCron();
                if (cron == null) {
                   if (cron1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!cron.equals(cron1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getCron(){
       return this.cron;
    }
    public Class getRunnableClass(){
       return this.runnableClass;
    }
    public ScheduledFuture getScheduledFuture(){
       return this.scheduledFuture;
    }
    public int hashCode(){
       ScheduledFuture $scheduledFuture;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($scheduledFuture = this.getScheduledFuture()) == null)? i: $scheduledFuture.hashCode();
       result = i1 + 59;
       Class $runnableClass = this.getRunnableClass();
       int i2 = result * 59;
       i1 = ($runnableClass == null)? i: $runnableClass.hashCode();
       result = i2 + i1;
       String $cron = this.getCron();
       i1 = result * 59;
       if ($cron != null) {
          i = $cron.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setCron(String cron){
       this.cron = cron;
    }
    public void setRunnableClass(Class runnableClass){
       this.runnableClass = runnableClass;
    }
    public void setScheduledFuture(ScheduledFuture scheduledFuture){
       this.scheduledFuture = scheduledFuture;
    }
    public String toString(){
       return "ScheduledFutureHolder\(scheduledFuture="+this.getScheduledFuture()+", runnableClass="+this.getRunnableClass()+", cron="+this.getCron()+"\)";
    }
}
