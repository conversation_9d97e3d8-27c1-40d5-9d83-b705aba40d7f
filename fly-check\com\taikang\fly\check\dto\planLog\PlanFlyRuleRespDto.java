package com.taikang.fly.check.dto.planLog.PlanFlyRuleRespDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.lang.StringBuilder;

public class PlanFlyRuleRespDto implements Serializable	// class@0001a8 from classes.dex
{
    private LocalDate createdTime;
    private String creater;
    private String dataSources;
    private String diagnosisType;
    private LocalDateTime executionDate;
    private String id;
    private String newSqlName;
    private LocalDate operateTime;
    private String operator;
    private String policyBasis;
    private String ps;
    private String redField3;
    private String region;
    private String removed;
    private String resultFlag;
    private String resultsEnforcement;
    private String ruleCategory1;
    private String ruleCategory2;
    private String ruleClassify;
    private String ruleIntension;
    private String ruleLevel;
    private String ruleLogic;
    private String ruleName;
    private String ruleParameter;
    private String ruleType;
    private String sign;
    private String sourceOfRule;
    private String sqlName;
    private String state;
    private String submitState;
    private String totalCount;
    private String vioLCount;
    private String vioLPrice;
    private String writeRuleName;
    private static final long serialVersionUID = 0x1;

    public void PlanFlyRuleRespDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof PlanFlyRuleRespDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof PlanFlyRuleRespDto){
          b = false;
       }else {
          PlanFlyRuleRespDto planFlyRuleR = o;
          if (!planFlyRuleR.canEqual(this)) {
             b = false;
          }else {
             String ruleName = this.getRuleName();
             String ruleName1 = planFlyRuleR.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String writeRuleNam = this.getWriteRuleName();
             String writeRuleNam1 = planFlyRuleR.getWriteRuleName();
             if (writeRuleNam == null) {
                if (writeRuleNam1 != null) {
                   b = false;
                }
             }else if(writeRuleNam.equals(writeRuleNam1)){
             }
             String region = this.getRegion();
             String region1 = planFlyRuleR.getRegion();
             if (region == null) {
                if (region1 != null) {
                   b = false;
                }
             }else if(region.equals(region1)){
             }
             String ruleType = this.getRuleType();
             String ruleType1 = planFlyRuleR.getRuleType();
             if (ruleType == null) {
                if (ruleType1 != null) {
                   b = false;
                }
             }else if(ruleType.equals(ruleType1)){
             }
             String ps = this.getPs();
             String ps1 = planFlyRuleR.getPs();
             if (ps == null) {
                if (ps1 != null) {
                   b = false;
                }
             }else if(ps.equals(ps1)){
             }
             String sqlName = this.getSqlName();
             String sqlName1 = planFlyRuleR.getSqlName();
             if (sqlName == null) {
                if (sqlName1 != null) {
                   b = false;
                }
             }else if(sqlName.equals(sqlName1)){
             }
             String newSqlName = this.getNewSqlName();
             String newSqlName1 = planFlyRuleR.getNewSqlName();
             if (newSqlName == null) {
                if (newSqlName1 != null) {
                label_00c3 :
                   b = false;
                }
             }else if(newSqlName.equals(newSqlName1)){
             }
             String id = this.getId();
             String id1 = planFlyRuleR.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String operator = this.getOperator();
             String operator1 = planFlyRuleR.getOperator();
             if (operator == null) {
                if (operator1 != null) {
                label_00f3 :
                   b = false;
                }
             }else if(operator.equals(operator1)){
             }
             String resultFlag = this.getResultFlag();
             String resultFlag1 = planFlyRuleR.getResultFlag();
             if (resultFlag == null) {
                if (resultFlag1 != null) {
                   b = false;
                }
             }else if(resultFlag.equals(resultFlag1)){
             }
             String removed = this.getRemoved();
             String removed1 = planFlyRuleR.getRemoved();
             if (removed == null) {
                if (removed1 != null) {
                label_0125 :
                   b = false;
                }
             }else if(removed.equals(removed1)){
             }
             LocalDate operateTime = this.getOperateTime();
             LocalDate operateTime1 = planFlyRuleR.getOperateTime();
             if (operateTime == null) {
                if (operateTime1 != null) {
                   b = false;
                }
             }else if(operateTime.equals(operateTime1)){
             }
             LocalDate createdTime = this.getCreatedTime();
             LocalDate createdTime1 = planFlyRuleR.getCreatedTime();
             if (createdTime == null) {
                if (createdTime1 != null) {
                   b = false;
                }
             }else if(createdTime.equals(createdTime1)){
             }
             String state = this.getState();
             String state1 = planFlyRuleR.getState();
             if (state == null) {
                if (state1 != null) {
                label_016f :
                   b = false;
                }
             }else if(state.equals(state1)){
             }
             String creater = this.getCreater();
             String creater1 = planFlyRuleR.getCreater();
             if (creater == null) {
                if (creater1 != null) {
                   b = false;
                }
             }else if(creater.equals(creater1)){
             }
             String dataSources = this.getDataSources();
             String dataSources1 = planFlyRuleR.getDataSources();
             if (dataSources == null) {
                if (dataSources1 != null) {
                label_01a1 :
                   b = false;
                }
             }else if(dataSources.equals(dataSources1)){
             }
             String submitState = this.getSubmitState();
             String submitState1 = planFlyRuleR.getSubmitState();
             if (submitState == null) {
                if (submitState1 != null) {
                   b = false;
                }
             }else if(submitState.equals(submitState1)){
             }
             String resultsEnfor = this.getResultsEnforcement();
             String resultsEnfor1 = planFlyRuleR.getResultsEnforcement();
             if (resultsEnfor == null) {
                if (resultsEnfor1 != null) {
                label_01d3 :
                   b = false;
                }
             }else if(resultsEnfor.equals(resultsEnfor1)){
             }
             LocalDateTime executionDat = this.getExecutionDate();
             LocalDateTime executionDat1 = planFlyRuleR.getExecutionDate();
             if (executionDat == null) {
                if (executionDat1 != null) {
                   b = false;
                }
             }else if(executionDat.equals(executionDat1)){
             }
             String ruleLevel = this.getRuleLevel();
             String ruleLevel1 = planFlyRuleR.getRuleLevel();
             if (ruleLevel == null) {
                if (ruleLevel1 != null) {
                   b = false;
                }
             }else if(ruleLevel.equals(ruleLevel1)){
             }
             String ruleCategory = this.getRuleCategory1();
             String ruleCategory1 = planFlyRuleR.getRuleCategory1();
             if (ruleCategory == null) {
                if (ruleCategory1 != null) {
                   b = false;
                }
             }else if(ruleCategory.equals(ruleCategory1)){
             }
             String ruleCategory2 = this.getRuleCategory2();
             String ruleCategory3 = planFlyRuleR.getRuleCategory2();
             if (ruleCategory2 == null) {
                if (ruleCategory3 != null) {
                label_0239 :
                   b = false;
                }
             }else if(ruleCategory2.equals(ruleCategory3)){
             }
             String diagnosisTyp = this.getDiagnosisType();
             String diagnosisTyp1 = planFlyRuleR.getDiagnosisType();
             if (diagnosisTyp == null) {
                if (diagnosisTyp1 != null) {
                   b = false;
                }
             }else if(diagnosisTyp.equals(diagnosisTyp1)){
             }
             String ruleIntensio = this.getRuleIntension();
             String ruleIntensio1 = planFlyRuleR.getRuleIntension();
             if (ruleIntensio == null) {
                if (ruleIntensio1 != null) {
                label_026b :
                   b = false;
                }
             }else if(ruleIntensio.equals(ruleIntensio1)){
             }
             String sourceOfRule = this.getSourceOfRule();
             String sourceOfRule1 = planFlyRuleR.getSourceOfRule();
             if (sourceOfRule == null) {
                if (sourceOfRule1 != null) {
                   b = false;
                }
             }else if(sourceOfRule.equals(sourceOfRule1)){
             }
             String ruleLogic = this.getRuleLogic();
             String ruleLogic1 = planFlyRuleR.getRuleLogic();
             if (ruleLogic == null) {
                if (ruleLogic1 != null) {
                label_029f :
                   b = false;
                }
             }else if(ruleLogic.equals(ruleLogic1)){
             }
             String ruleParamete = this.getRuleParameter();
             String ruleParamete1 = planFlyRuleR.getRuleParameter();
             if (ruleParamete == null) {
                if (ruleParamete1 != null) {
                   b = false;
                }
             }else if(ruleParamete.equals(ruleParamete1)){
             }
             String redField3 = this.getRedField3();
             String redField31 = planFlyRuleR.getRedField3();
             if (redField3 == null) {
                if (redField31 != null) {
                label_02d3 :
                   b = false;
                }
             }else if(redField3.equals(redField31)){
             }
             String totalCount = this.getTotalCount();
             String totalCount1 = planFlyRuleR.getTotalCount();
             if (totalCount == null) {
                if (totalCount1 != null) {
                   b = false;
                }
             }else if(totalCount.equals(totalCount1)){
             }
             String ruleClassify = this.getRuleClassify();
             String ruleClassify1 = planFlyRuleR.getRuleClassify();
             if (ruleClassify == null) {
                if (ruleClassify1 != null) {
                label_0305 :
                   b = false;
                }
             }else if(ruleClassify.equals(ruleClassify1)){
             }
             String vioLCount = this.getVioLCount();
             String vioLCount1 = planFlyRuleR.getVioLCount();
             if (vioLCount == null) {
                if (vioLCount1 != null) {
                   b = false;
                }
             }else if(vioLCount.equals(vioLCount1)){
             }
             String vioLPrice = this.getVioLPrice();
             String vioLPrice1 = planFlyRuleR.getVioLPrice();
             if (vioLPrice == null) {
                if (vioLPrice1 != null) {
                   b = false;
                }
             }else if(vioLPrice.equals(vioLPrice1)){
             }
             String policyBasis = this.getPolicyBasis();
             String policyBasis1 = planFlyRuleR.getPolicyBasis();
             if (policyBasis == null) {
                if (policyBasis1 != null) {
                label_0353 :
                   b = false;
                }
             }else if(policyBasis.equals(policyBasis1)){
             }
             String sign = this.getSign();
             String sign1 = planFlyRuleR.getSign();
             if (sign == null) {
                if (sign1 != null) {
                   b = false;
                }
             }else if(sign.equals(sign1)){
             }
             b = true;
          }
       }
       return b;
    }
    public LocalDate getCreatedTime(){
       return this.createdTime;
    }
    public String getCreater(){
       return this.creater;
    }
    public String getDataSources(){
       return this.dataSources;
    }
    public String getDiagnosisType(){
       return this.diagnosisType;
    }
    public LocalDateTime getExecutionDate(){
       return this.executionDate;
    }
    public String getId(){
       return this.id;
    }
    public String getNewSqlName(){
       return this.newSqlName;
    }
    public LocalDate getOperateTime(){
       return this.operateTime;
    }
    public String getOperator(){
       return this.operator;
    }
    public String getPolicyBasis(){
       return this.policyBasis;
    }
    public String getPs(){
       return this.ps;
    }
    public String getRedField3(){
       return this.redField3;
    }
    public String getRegion(){
       return this.region;
    }
    public String getRemoved(){
       return this.removed;
    }
    public String getResultFlag(){
       return this.resultFlag;
    }
    public String getResultsEnforcement(){
       return this.resultsEnforcement;
    }
    public String getRuleCategory1(){
       return this.ruleCategory1;
    }
    public String getRuleCategory2(){
       return this.ruleCategory2;
    }
    public String getRuleClassify(){
       return this.ruleClassify;
    }
    public String getRuleIntension(){
       return this.ruleIntension;
    }
    public String getRuleLevel(){
       return this.ruleLevel;
    }
    public String getRuleLogic(){
       return this.ruleLogic;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleParameter(){
       return this.ruleParameter;
    }
    public String getRuleType(){
       return this.ruleType;
    }
    public String getSign(){
       return this.sign;
    }
    public String getSourceOfRule(){
       return this.sourceOfRule;
    }
    public String getSqlName(){
       return this.sqlName;
    }
    public String getState(){
       return this.state;
    }
    public String getSubmitState(){
       return this.submitState;
    }
    public String getTotalCount(){
       return this.totalCount;
    }
    public String getVioLCount(){
       return this.vioLCount;
    }
    public String getVioLPrice(){
       return this.vioLPrice;
    }
    public String getWriteRuleName(){
       return this.writeRuleName;
    }
    public int hashCode(){
       String $ruleName;
       int PRIME = 59;
       int result = 1;
       int i = (($ruleName = this.getRuleName()) == null)? 43: $ruleName.hashCode();
       result = i + 59;
       String $writeRuleName = this.getWriteRuleName();
       int i1 = result * 59;
       i = ($writeRuleName == null)? 43: $writeRuleName.hashCode();
       result = i1 + i;
       String $region = this.getRegion();
       i1 = result * 59;
       i = ($region == null)? 43: $region.hashCode();
       result = i1 + i;
       String $ruleType = this.getRuleType();
       i1 = result * 59;
       i = ($ruleType == null)? 43: $ruleType.hashCode();
       result = i1 + i;
       String $ps = this.getPs();
       i1 = result * 59;
       i = ($ps == null)? 43: $ps.hashCode();
       result = i1 + i;
       String sqlName = this.getSqlName();
       i1 = result * 59;
       i = (sqlName == null)? 43: sqlName.hashCode();
       String newSqlName = this.getNewSqlName();
       i1 = (i1 + i) * 59;
       i = (newSqlName == null)? 43: newSqlName.hashCode();
       String id = this.getId();
       i1 = (i1 + i) * 59;
       i = (id == null)? 43: id.hashCode();
       String operator = this.getOperator();
       i1 = (i1 + i) * 59;
       i = (operator == null)? 43: operator.hashCode();
       String resultFlag = this.getResultFlag();
       i1 = (i1 + i) * 59;
       i = (resultFlag == null)? 43: resultFlag.hashCode();
       String removed = this.getRemoved();
       i1 = (i1 + i) * 59;
       i = (removed == null)? 43: removed.hashCode();
       LocalDate operateTime = this.getOperateTime();
       i1 = (i1 + i) * 59;
       i = (operateTime == null)? 43: operateTime.hashCode();
       LocalDate createdTime = this.getCreatedTime();
       i1 = (i1 + i) * 59;
       i = (createdTime == null)? 43: createdTime.hashCode();
       String state = this.getState();
       i1 = (i1 + i) * 59;
       i = (state == null)? 43: state.hashCode();
       String creater = this.getCreater();
       i1 = (i1 + i) * 59;
       i = (creater == null)? 43: creater.hashCode();
       String dataSources = this.getDataSources();
       i1 = (i1 + i) * 59;
       i = (dataSources == null)? 43: dataSources.hashCode();
       String submitState = this.getSubmitState();
       i1 = (i1 + i) * 59;
       i = (submitState == null)? 43: submitState.hashCode();
       String resultsEnfor = this.getResultsEnforcement();
       i1 = (i1 + i) * 59;
       i = (resultsEnfor == null)? 43: resultsEnfor.hashCode();
       LocalDateTime executionDat = this.getExecutionDate();
       i1 = (i1 + i) * 59;
       i = (executionDat == null)? 43: executionDat.hashCode();
       String ruleLevel = this.getRuleLevel();
       i1 = (i1 + i) * 59;
       i = (ruleLevel == null)? 43: ruleLevel.hashCode();
       String ruleCategory = this.getRuleCategory1();
       i1 = (i1 + i) * 59;
       i = (ruleCategory == null)? 43: ruleCategory.hashCode();
       String ruleCategory1 = this.getRuleCategory2();
       i1 = (i1 + i) * 59;
       i = (ruleCategory1 == null)? 43: ruleCategory1.hashCode();
       String diagnosisTyp = this.getDiagnosisType();
       i1 = (i1 + i) * 59;
       i = (diagnosisTyp == null)? 43: diagnosisTyp.hashCode();
       String ruleIntensio = this.getRuleIntension();
       i1 = (i1 + i) * 59;
       i = (ruleIntensio == null)? 43: ruleIntensio.hashCode();
       String sourceOfRule = this.getSourceOfRule();
       i1 = (i1 + i) * 59;
       i = (sourceOfRule == null)? 43: sourceOfRule.hashCode();
       String ruleLogic = this.getRuleLogic();
       i1 = (i1 + i) * 59;
       i = (ruleLogic == null)? 43: ruleLogic.hashCode();
       String ruleParamete = this.getRuleParameter();
       i1 = (i1 + i) * 59;
       i = (ruleParamete == null)? 43: ruleParamete.hashCode();
       String redField3 = this.getRedField3();
       i1 = (i1 + i) * 59;
       i = (redField3 == null)? 43: redField3.hashCode();
       String totalCount = this.getTotalCount();
       i1 = (i1 + i) * 59;
       i = (totalCount == null)? 43: totalCount.hashCode();
       String ruleClassify = this.getRuleClassify();
       i1 = (i1 + i) * 59;
       i = (ruleClassify == null)? 43: ruleClassify.hashCode();
       String vioLCount = this.getVioLCount();
       i1 = (i1 + i) * 59;
       i = (vioLCount == null)? 43: vioLCount.hashCode();
       String vioLPrice = this.getVioLPrice();
       i1 = (i1 + i) * 59;
       i = (vioLPrice == null)? 43: vioLPrice.hashCode();
       String policyBasis = this.getPolicyBasis();
       i1 = (i1 + i) * 59;
       i = (policyBasis == null)? 43: policyBasis.hashCode();
       String sign = this.getSign();
       i1 = (i1 + i) * 59;
       i = (sign == null)? 43: sign.hashCode();
       return (i1 + i);
    }
    public void setCreatedTime(LocalDate createdTime){
       this.createdTime = createdTime;
    }
    public void setCreater(String creater){
       this.creater = creater;
    }
    public void setDataSources(String dataSources){
       this.dataSources = dataSources;
    }
    public void setDiagnosisType(String diagnosisType){
       this.diagnosisType = diagnosisType;
    }
    public void setExecutionDate(LocalDateTime executionDate){
       this.executionDate = executionDate;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setNewSqlName(String newSqlName){
       this.newSqlName = newSqlName;
    }
    public void setOperateTime(LocalDate operateTime){
       this.operateTime = operateTime;
    }
    public void setOperator(String operator){
       this.operator = operator;
    }
    public void setPolicyBasis(String policyBasis){
       this.policyBasis = policyBasis;
    }
    public void setPs(String ps){
       this.ps = ps;
    }
    public void setRedField3(String redField3){
       this.redField3 = redField3;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setRemoved(String removed){
       this.removed = removed;
    }
    public void setResultFlag(String resultFlag){
       this.resultFlag = resultFlag;
    }
    public void setResultsEnforcement(String resultsEnforcement){
       this.resultsEnforcement = resultsEnforcement;
    }
    public void setRuleCategory1(String ruleCategory1){
       this.ruleCategory1 = ruleCategory1;
    }
    public void setRuleCategory2(String ruleCategory2){
       this.ruleCategory2 = ruleCategory2;
    }
    public void setRuleClassify(String ruleClassify){
       this.ruleClassify = ruleClassify;
    }
    public void setRuleIntension(String ruleIntension){
       this.ruleIntension = ruleIntension;
    }
    public void setRuleLevel(String ruleLevel){
       this.ruleLevel = ruleLevel;
    }
    public void setRuleLogic(String ruleLogic){
       this.ruleLogic = ruleLogic;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleParameter(String ruleParameter){
       this.ruleParameter = ruleParameter;
    }
    public void setRuleType(String ruleType){
       this.ruleType = ruleType;
    }
    public void setSign(String sign){
       this.sign = sign;
    }
    public void setSourceOfRule(String sourceOfRule){
       this.sourceOfRule = sourceOfRule;
    }
    public void setSqlName(String sqlName){
       this.sqlName = sqlName;
    }
    public void setState(String state){
       this.state = state;
    }
    public void setSubmitState(String submitState){
       this.submitState = submitState;
    }
    public void setTotalCount(String totalCount){
       this.totalCount = totalCount;
    }
    public void setVioLCount(String vioLCount){
       this.vioLCount = vioLCount;
    }
    public void setVioLPrice(String vioLPrice){
       this.vioLPrice = vioLPrice;
    }
    public void setWriteRuleName(String writeRuleName){
       this.writeRuleName = writeRuleName;
    }
    public String toString(){
       return "PlanFlyRuleRespDto\(ruleName="+this.getRuleName()+", writeRuleName="+this.getWriteRuleName()+", region="+this.getRegion()+", ruleType="+this.getRuleType()+", ps="+this.getPs()+", sqlName="+this.getSqlName()+", newSqlName="+this.getNewSqlName()+", id="+this.getId()+", operator="+this.getOperator()+", resultFlag="+this.getResultFlag()+", removed="+this.getRemoved()+", operateTime="+this.getOperateTime()+", createdTime="+this.getCreatedTime()+", state="+this.getState()+", creater="+this.getCreater()+", dataSources="+this.getDataSources()+", submitState="+this.getSubmitState()+", resultsEnforcement="+this.getResultsEnforcement()+", executionDate="+this.getExecutionDate()+", ruleLevel="+this.getRuleLevel()+", ruleCategory1="+this.getRuleCategory1()+", ruleCategory2="+this.getRuleCategory2()+", diagnosisType="+this.getDiagnosisType()+", ruleIntension="+this.getRuleIntension()+", sourceOfRule="+this.getSourceOfRule()+", ruleLogic="+this.getRuleLogic()+", ruleParameter="+this.getRuleParameter()+", redField3="+this.getRedField3()+", totalCount="+this.getTotalCount()+", ruleClassify="+this.getRuleClassify()+", vioLCount="+this.getVioLCount()+", vioLPrice="+this.getVioLPrice()+", policyBasis="+this.getPolicyBasis()+", sign="+this.getSign()+"\)";
    }
}
