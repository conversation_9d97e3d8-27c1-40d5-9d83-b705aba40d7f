package com.taikang.fly.check.dto.threeOrder.ThreeOrderDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ThreeOrderDto implements Serializable	// class@0001be from classes.dex
{
    private String HospitalProjectCode;
    private String HospitalProjectName;
    private String chargingItemLevel;
    private String costType;
    private String hospitalProjectC;
    private String hospitalProjectN;
    private String typesOfCharges;
    private static final long serialVersionUID = 0x7dede0ec71772e8;

    public void ThreeOrderDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ThreeOrderDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ThreeOrderDto){
          b = false;
       }else {
          ThreeOrderDto threeOrderDt = o;
          if (!threeOrderDt.canEqual(this)) {
             b = false;
          }else {
             String hospitalProj = this.getHospitalProjectName();
             String hospitalProj1 = threeOrderDt.getHospitalProjectName();
             if (hospitalProj == null) {
                if (hospitalProj1 != null) {
                   b = false;
                }
             }else if(hospitalProj.equals(hospitalProj1)){
             }
             String hospitalProj2 = this.getHospitalProjectCode();
             String hospitalProj3 = threeOrderDt.getHospitalProjectCode();
             if (hospitalProj2 == null) {
                if (hospitalProj3 != null) {
                   b = false;
                }
             }else if(hospitalProj2.equals(hospitalProj3)){
             }
             String typesOfCharg = this.getTypesOfCharges();
             String typesOfCharg1 = threeOrderDt.getTypesOfCharges();
             if (typesOfCharg == null) {
                if (typesOfCharg1 != null) {
                   b = false;
                }
             }else if(typesOfCharg.equals(typesOfCharg1)){
             }
             String chargingItem = this.getChargingItemLevel();
             String chargingItem1 = threeOrderDt.getChargingItemLevel();
             if (chargingItem == null) {
                if (chargingItem1 != null) {
                   b = false;
                }
             }else if(chargingItem.equals(chargingItem1)){
             }
             String hospitalProj4 = this.getHospitalProjectC();
             String hospitalProj5 = threeOrderDt.getHospitalProjectC();
             if (hospitalProj4 == null) {
                if (hospitalProj5 != null) {
                   b = false;
                }
             }else if(hospitalProj4.equals(hospitalProj5)){
             }
             String hospitalProj6 = this.getHospitalProjectN();
             String hospitalProj7 = threeOrderDt.getHospitalProjectN();
             if (hospitalProj6 == null) {
                if (hospitalProj7 != null) {
                label_009a :
                   b = false;
                }
             }else if(hospitalProj6.equals(hospitalProj7)){
             }
             String costType = this.getCostType();
             String costType1 = threeOrderDt.getCostType();
             if (costType == null) {
                if (costType1 != null) {
                   b = false;
                }
             }else if(costType.equals(costType1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getChargingItemLevel(){
       return this.chargingItemLevel;
    }
    public String getCostType(){
       return this.costType;
    }
    public String getHospitalProjectC(){
       return this.hospitalProjectC;
    }
    public String getHospitalProjectCode(){
       return this.HospitalProjectCode;
    }
    public String getHospitalProjectN(){
       return this.hospitalProjectN;
    }
    public String getHospitalProjectName(){
       return this.HospitalProjectName;
    }
    public String getTypesOfCharges(){
       return this.typesOfCharges;
    }
    public int hashCode(){
       String $HospitalProjectName;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($HospitalProjectName = this.getHospitalProjectName()) == null)? i: $HospitalProjectName.hashCode();
       result = i1 + 59;
       String $HospitalProjectCode = this.getHospitalProjectCode();
       int i2 = result * 59;
       i1 = ($HospitalProjectCode == null)? i: $HospitalProjectCode.hashCode();
       result = i2 + i1;
       String $typesOfCharges = this.getTypesOfCharges();
       i2 = result * 59;
       i1 = ($typesOfCharges == null)? i: $typesOfCharges.hashCode();
       result = i2 + i1;
       String $chargingItemLevel = this.getChargingItemLevel();
       i2 = result * 59;
       i1 = ($chargingItemLevel == null)? i: $chargingItemLevel.hashCode();
       result = i2 + i1;
       String $hospitalProjectC = this.getHospitalProjectC();
       i2 = result * 59;
       i1 = ($hospitalProjectC == null)? i: $hospitalProjectC.hashCode();
       result = i2 + i1;
       String hospitalProj = this.getHospitalProjectN();
       i2 = result * 59;
       i1 = (hospitalProj == null)? i: hospitalProj.hashCode();
       String costType = this.getCostType();
       i1 = (i2 + i1) * 59;
       if (costType != null) {
          i = costType.hashCode();
       }
       return (i1 + i);
    }
    public void setChargingItemLevel(String chargingItemLevel){
       this.chargingItemLevel = chargingItemLevel;
    }
    public void setCostType(String costType){
       this.costType = costType;
    }
    public void setHospitalProjectC(String hospitalProjectC){
       this.hospitalProjectC = hospitalProjectC;
    }
    public void setHospitalProjectCode(String HospitalProjectCode){
       this.HospitalProjectCode = HospitalProjectCode;
    }
    public void setHospitalProjectN(String hospitalProjectN){
       this.hospitalProjectN = hospitalProjectN;
    }
    public void setHospitalProjectName(String HospitalProjectName){
       this.HospitalProjectName = HospitalProjectName;
    }
    public void setTypesOfCharges(String typesOfCharges){
       this.typesOfCharges = typesOfCharges;
    }
    public String toString(){
       return "ThreeOrderDto\(HospitalProjectName="+this.getHospitalProjectName()+", HospitalProjectCode="+this.getHospitalProjectCode()+", typesOfCharges="+this.getTypesOfCharges()+", chargingItemLevel="+this.getChargingItemLevel()+", hospitalProjectC="+this.getHospitalProjectC()+", hospitalProjectN="+this.getHospitalProjectN()+", costType="+this.getCostType()+"\)";
    }
}
