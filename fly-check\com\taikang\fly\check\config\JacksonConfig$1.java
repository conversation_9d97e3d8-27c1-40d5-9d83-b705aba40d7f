package com.taikang.fly.check.config.JacksonConfig$1;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.taikang.fly.check.config.JacksonConfig;
import java.lang.Object;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import java.lang.String;

class JacksonConfig$1 extends JsonSerializer	// class@00008b from classes.dex
{
    final JacksonConfig this$0;

    void JacksonConfig$1(JacksonConfig this$0){
       this.this$0 = this$0;
       super();
    }
    public void serialize(Object o,JsonGenerator jsonGenerator,SerializerProvider serializerProvider){
       jsonGenerator.writeString("");
    }
}
