package com.taikang.fly.check.dto.flyRule.FlyRuleNewSearchDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class FlyRuleNewSearchDto implements Serializable	// class@000104 from classes.dex
{
    private String region;
    private String ruleName;
    private String ruleScopeApply;
    private String state;
    private static final long serialVersionUID = 0x1;

    public void FlyRuleNewSearchDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FlyRuleNewSearchDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof FlyRuleNewSearchDto) {
             b = false;
          }else {
             FlyRuleNewSearchDto uFlyRuleNewS = o;
             if (!uFlyRuleNewS.canEqual(this)) {
                b = false;
             }else {
                String region = this.getRegion();
                String region1 = uFlyRuleNewS.getRegion();
                if (region == null) {
                   if (region1 != null) {
                      b = false;
                   }
                }else if(region.equals(region1)){
                }
                String state = this.getState();
                String state1 = uFlyRuleNewS.getState();
                if (state == null) {
                   if (state1 != null) {
                      b = false;
                   }
                }else if(state.equals(state1)){
                }
                String ruleName = this.getRuleName();
                String ruleName1 = uFlyRuleNewS.getRuleName();
                if (ruleName == null) {
                   if (ruleName1 != null) {
                      b = false;
                   }
                }else if(ruleName.equals(ruleName1)){
                }
                String ruleScopeApp = this.getRuleScopeApply();
                String ruleScopeApp1 = uFlyRuleNewS.getRuleScopeApply();
                if (ruleScopeApp == null) {
                   if (ruleScopeApp1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!ruleScopeApp.equals(ruleScopeApp1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getRegion(){
       return this.region;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleScopeApply(){
       return this.ruleScopeApply;
    }
    public String getState(){
       return this.state;
    }
    public int hashCode(){
       String $region;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($region = this.getRegion()) == null)? i: $region.hashCode();
       result = i1 + 59;
       String $state = this.getState();
       int i2 = result * 59;
       i1 = ($state == null)? i: $state.hashCode();
       result = i2 + i1;
       String $ruleName = this.getRuleName();
       i2 = result * 59;
       i1 = ($ruleName == null)? i: $ruleName.hashCode();
       result = i2 + i1;
       String $ruleScopeApply = this.getRuleScopeApply();
       i1 = result * 59;
       if ($ruleScopeApply != null) {
          i = $ruleScopeApply.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleScopeApply(String ruleScopeApply){
       this.ruleScopeApply = ruleScopeApply;
    }
    public void setState(String state){
       this.state = state;
    }
    public String toString(){
       return "FlyRuleNewSearchDto\(region="+this.getRegion()+", state="+this.getState()+", ruleName="+this.getRuleName()+", ruleScopeApply="+this.getRuleScopeApply()+"\)";
    }
}
