package org.springframework.boot.loader.JarLauncher;
import org.springframework.boot.loader.ExecutableArchiveLauncher;
import org.springframework.boot.loader.archive.Archive;
import java.lang.String;
import org.springframework.boot.loader.archive.Archive$Entry;
import java.lang.Object;

public class Jar<PERSON>auncher extends ExecutableArchiveLauncher	// class@00052f from classes.dex
{
    static final String BOOT_INF_CLASSES = "BOOT-INF/classes/";
    static final String BOOT_INF_LIB = "BOOT-INF/lib/";

    public void JarLauncher(){
       super();
    }
    protected void JarLauncher(Archive archive){
       super(archive);
    }
    public static void main(String[] args){
       new JarLauncher().launch(args);
    }
    protected boolean isNestedArchive(Archive$Entry entry){
       boolean b = (entry.isDirectory())? entry.getName().equals("BOOT-INF/classes/"): entry.getName().startsWith("BOOT-INF/lib/");
       return b;
    }
}
