package com.taikang.fly.check.dto.flyRuleTemplate.MultiFlyRuleTemplateUpdateDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class MultiFlyRuleTemplateUpdateDto implements Serializable	// class@000125 from classes.dex
{
    private String id;
    private String ruleName;
    private String sqlExample;
    private String sqlTemplate;
    private static final long serialVersionUID = 0x1;

    public void MultiFlyRuleTemplateUpdateDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MultiFlyRuleTemplateUpdateDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof MultiFlyRuleTemplateUpdateDto) {
             b = false;
          }else {
             MultiFlyRuleTemplateUpdateDto multiFlyRule = o;
             if (!multiFlyRule.canEqual(this)) {
                b = false;
             }else {
                String id = this.getId();
                String id1 = multiFlyRule.getId();
                if (id == null) {
                   if (id1 != null) {
                      b = false;
                   }
                }else if(id.equals(id1)){
                }
                String ruleName = this.getRuleName();
                String ruleName1 = multiFlyRule.getRuleName();
                if (ruleName == null) {
                   if (ruleName1 != null) {
                      b = false;
                   }
                }else if(ruleName.equals(ruleName1)){
                }
                String sqlTemplate = this.getSqlTemplate();
                String sqlTemplate1 = multiFlyRule.getSqlTemplate();
                if (sqlTemplate == null) {
                   if (sqlTemplate1 != null) {
                      b = false;
                   }
                }else if(sqlTemplate.equals(sqlTemplate1)){
                }
                String sqlExample = this.getSqlExample();
                String sqlExample1 = multiFlyRule.getSqlExample();
                if (sqlExample == null) {
                   if (sqlExample1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!sqlExample.equals(sqlExample1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getId(){
       return this.id;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getSqlExample(){
       return this.sqlExample;
    }
    public String getSqlTemplate(){
       return this.sqlTemplate;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $ruleName = this.getRuleName();
       int i2 = result * 59;
       i1 = ($ruleName == null)? i: $ruleName.hashCode();
       result = i2 + i1;
       String $sqlTemplate = this.getSqlTemplate();
       i2 = result * 59;
       i1 = ($sqlTemplate == null)? i: $sqlTemplate.hashCode();
       result = i2 + i1;
       String $sqlExample = this.getSqlExample();
       i1 = result * 59;
       if ($sqlExample != null) {
          i = $sqlExample.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setSqlExample(String sqlExample){
       this.sqlExample = sqlExample;
    }
    public void setSqlTemplate(String sqlTemplate){
       this.sqlTemplate = sqlTemplate;
    }
    public String toString(){
       return "MultiFlyRuleTemplateUpdateDto\(id="+this.getId()+", ruleName="+this.getRuleName()+", sqlTemplate="+this.getSqlTemplate()+", sqlExample="+this.getSqlExample()+"\)";
    }
}
