package com.taikang.fly.check.config.DataSourceConfig;
import java.lang.Object;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.boot.jdbc.DataSourceBuilder;
import java.lang.Class;
import javax.sql.DataSource;
import org.springframework.jdbc.core.JdbcTemplate;

public class DataSourceConfig	// class@00008a from classes.dex
{

    public void DataSourceConfig(){
       super();
    }
    public HikariDataSource dataSource(DataSourceProperties properties){
       return properties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
    }
    public DataSourceProperties dataSourceProperties(){
       return new DataSourceProperties();
    }
    public JdbcTemplate primaryJdbcTemplate(DataSource dataSource){
       return new JdbcTemplate(dataSource);
    }
}
