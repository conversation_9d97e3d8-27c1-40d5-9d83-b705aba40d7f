package com.taikang.fly.check.mybatis.domain.drg.DrgCheckTasks;
import java.lang.Object;
import java.lang.String;
import java.time.LocalDateTime;
import java.lang.StringBuilder;

public class DrgCheckTasks	// class@00027c from classes.dex
{
    private String chkTaskBchno;
    private LocalDateTime createTime;
    private String datasource;
    private String setlBegndate;
    private String setlEnddate;
    private String taskExeCrtStg;

    public void DrgCheckTasks(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgCheckTasks;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof DrgCheckTasks){
          b = false;
       }else {
          DrgCheckTasks uDrgCheckTas = o;
          if (!uDrgCheckTas.canEqual(this)) {
             b = false;
          }else {
             String chkTaskBchno = this.getChkTaskBchno();
             String chkTaskBchno1 = uDrgCheckTas.getChkTaskBchno();
             if (chkTaskBchno == null) {
                if (chkTaskBchno1 != null) {
                   b = false;
                }
             }else if(chkTaskBchno.equals(chkTaskBchno1)){
             }
             String setlBegndate = this.getSetlBegndate();
             String setlBegndate1 = uDrgCheckTas.getSetlBegndate();
             if (setlBegndate == null) {
                if (setlBegndate1 != null) {
                   b = false;
                }
             }else if(setlBegndate.equals(setlBegndate1)){
             }
             String setlEnddate = this.getSetlEnddate();
             String setlEnddate1 = uDrgCheckTas.getSetlEnddate();
             if (setlEnddate == null) {
                if (setlEnddate1 != null) {
                   b = false;
                }
             }else if(setlEnddate.equals(setlEnddate1)){
             }
             String taskExeCrtSt = this.getTaskExeCrtStg();
             String taskExeCrtSt1 = uDrgCheckTas.getTaskExeCrtStg();
             if (taskExeCrtSt == null) {
                if (taskExeCrtSt1 != null) {
                   b = false;
                }
             }else if(taskExeCrtSt.equals(taskExeCrtSt1)){
             }
             LocalDateTime createTime = this.getCreateTime();
             LocalDateTime createTime1 = uDrgCheckTas.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String datasource = this.getDatasource();
             String datasource1 = uDrgCheckTas.getDatasource();
             if (datasource == null) {
                if (datasource1 != null) {
                   b = false;
                }
             }else if(datasource.equals(datasource1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getChkTaskBchno(){
       return this.chkTaskBchno;
    }
    public LocalDateTime getCreateTime(){
       return this.createTime;
    }
    public String getDatasource(){
       return this.datasource;
    }
    public String getSetlBegndate(){
       return this.setlBegndate;
    }
    public String getSetlEnddate(){
       return this.setlEnddate;
    }
    public String getTaskExeCrtStg(){
       return this.taskExeCrtStg;
    }
    public int hashCode(){
       String $chkTaskBchno;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($chkTaskBchno = this.getChkTaskBchno()) == null)? i: $chkTaskBchno.hashCode();
       result = i1 + 59;
       String $setlBegndate = this.getSetlBegndate();
       int i2 = result * 59;
       i1 = ($setlBegndate == null)? i: $setlBegndate.hashCode();
       result = i2 + i1;
       String $setlEnddate = this.getSetlEnddate();
       i2 = result * 59;
       i1 = ($setlEnddate == null)? i: $setlEnddate.hashCode();
       result = i2 + i1;
       String $taskExeCrtStg = this.getTaskExeCrtStg();
       i2 = result * 59;
       i1 = ($taskExeCrtStg == null)? i: $taskExeCrtStg.hashCode();
       result = i2 + i1;
       LocalDateTime $createTime = this.getCreateTime();
       i2 = result * 59;
       i1 = ($createTime == null)? i: $createTime.hashCode();
       result = i2 + i1;
       String datasource = this.getDatasource();
       i1 = result * 59;
       if (datasource != null) {
          i = datasource.hashCode();
       }
       return (i1 + i);
    }
    public void setChkTaskBchno(String chkTaskBchno){
       this.chkTaskBchno = chkTaskBchno;
    }
    public void setCreateTime(LocalDateTime createTime){
       this.createTime = createTime;
    }
    public void setDatasource(String datasource){
       this.datasource = datasource;
    }
    public void setSetlBegndate(String setlBegndate){
       this.setlBegndate = setlBegndate;
    }
    public void setSetlEnddate(String setlEnddate){
       this.setlEnddate = setlEnddate;
    }
    public void setTaskExeCrtStg(String taskExeCrtStg){
       this.taskExeCrtStg = taskExeCrtStg;
    }
    public String toString(){
       return "DrgCheckTasks\(chkTaskBchno="+this.getChkTaskBchno()+", setlBegndate="+this.getSetlBegndate()+", setlEnddate="+this.getSetlEnddate()+", taskExeCrtStg="+this.getTaskExeCrtStg()+", createTime="+this.getCreateTime()+", datasource="+this.getDatasource()+"\)";
    }
}
