package com.taikang.fly.check.mybatis.domain.Model;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class Model	// class@000254 from classes.dex
{
    private String id;
    private String modelLogSrc;
    private String modelName;
    private String modelScriptSrc;
    private String modelType;
    private String statisticsSql;

    public void Model(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof Model;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof Model){
          b = false;
       }else {
          Model model = o;
          if (!model.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = model.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String modelType = this.getModelType();
             String modelType1 = model.getModelType();
             if (modelType == null) {
                if (modelType1 != null) {
                   b = false;
                }
             }else if(modelType.equals(modelType1)){
             }
             String modelName = this.getModelName();
             String modelName1 = model.getModelName();
             if (modelName == null) {
                if (modelName1 != null) {
                   b = false;
                }
             }else if(modelName.equals(modelName1)){
             }
             String statisticsSq = this.getStatisticsSql();
             String statisticsSq1 = model.getStatisticsSql();
             if (statisticsSq == null) {
                if (statisticsSq1 != null) {
                   b = false;
                }
             }else if(statisticsSq.equals(statisticsSq1)){
             }
             String modelScriptS = this.getModelScriptSrc();
             String modelScriptS1 = model.getModelScriptSrc();
             if (modelScriptS == null) {
                if (modelScriptS1 != null) {
                label_0071 :
                   b = false;
                }
             }else if(modelScriptS.equals(modelScriptS1)){
             }
             String modelLogSrc = this.getModelLogSrc();
             String modelLogSrc1 = model.getModelLogSrc();
             if (modelLogSrc == null) {
                if (modelLogSrc1 != null) {
                   b = false;
                }
             }else if(modelLogSrc.equals(modelLogSrc1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getId(){
       return this.id;
    }
    public String getModelLogSrc(){
       return this.modelLogSrc;
    }
    public String getModelName(){
       return this.modelName;
    }
    public String getModelScriptSrc(){
       return this.modelScriptSrc;
    }
    public String getModelType(){
       return this.modelType;
    }
    public String getStatisticsSql(){
       return this.statisticsSql;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $modelType = this.getModelType();
       int i2 = result * 59;
       i1 = ($modelType == null)? i: $modelType.hashCode();
       result = i2 + i1;
       String $modelName = this.getModelName();
       i2 = result * 59;
       i1 = ($modelName == null)? i: $modelName.hashCode();
       result = i2 + i1;
       String $statisticsSql = this.getStatisticsSql();
       i2 = result * 59;
       i1 = ($statisticsSql == null)? i: $statisticsSql.hashCode();
       result = i2 + i1;
       String $modelScriptSrc = this.getModelScriptSrc();
       i2 = result * 59;
       i1 = ($modelScriptSrc == null)? i: $modelScriptSrc.hashCode();
       result = i2 + i1;
       String modelLogSrc = this.getModelLogSrc();
       i1 = result * 59;
       if (modelLogSrc != null) {
          i = modelLogSrc.hashCode();
       }
       return (i1 + i);
    }
    public void setId(String id){
       this.id = id;
    }
    public void setModelLogSrc(String modelLogSrc){
       this.modelLogSrc = modelLogSrc;
    }
    public void setModelName(String modelName){
       this.modelName = modelName;
    }
    public void setModelScriptSrc(String modelScriptSrc){
       this.modelScriptSrc = modelScriptSrc;
    }
    public void setModelType(String modelType){
       this.modelType = modelType;
    }
    public void setStatisticsSql(String statisticsSql){
       this.statisticsSql = statisticsSql;
    }
    public String toString(){
       return "Model\(id="+this.getId()+", modelType="+this.getModelType()+", modelName="+this.getModelName()+", statisticsSql="+this.getStatisticsSql()+", modelScriptSrc="+this.getModelScriptSrc()+", modelLogSrc="+this.getModelLogSrc()+"\)";
    }
}
