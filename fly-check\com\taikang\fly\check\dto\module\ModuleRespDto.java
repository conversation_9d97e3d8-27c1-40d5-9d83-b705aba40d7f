package com.taikang.fly.check.dto.module.ModuleRespDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ModuleRespDto implements Serializable	// class@000199 from classes.dex
{
    private String createdTime;
    private String creator;
    private String description;
    private String icon;
    private String isValid;
    private String modifier;
    private String modifyTime;
    private String moduleCode;
    private String moduleName;
    private String moduleOrder;
    private String moduleType;
    private String url;
    private static final long serialVersionUID = 0x349d06aa3e3fa7be;

    public void ModuleRespDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ModuleRespDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ModuleRespDto){
          b = false;
       }else {
          ModuleRespDto moduleRespDt = o;
          if (!moduleRespDt.canEqual(this)) {
             b = false;
          }else {
             String moduleCode = this.getModuleCode();
             String moduleCode1 = moduleRespDt.getModuleCode();
             if (moduleCode == null) {
                if (moduleCode1 != null) {
                   b = false;
                }
             }else if(moduleCode.equals(moduleCode1)){
             }
             String moduleName = this.getModuleName();
             String moduleName1 = moduleRespDt.getModuleName();
             if (moduleName == null) {
                if (moduleName1 != null) {
                   b = false;
                }
             }else if(moduleName.equals(moduleName1)){
             }
             String moduleOrder = this.getModuleOrder();
             String moduleOrder1 = moduleRespDt.getModuleOrder();
             if (moduleOrder == null) {
                if (moduleOrder1 != null) {
                   b = false;
                }
             }else if(moduleOrder.equals(moduleOrder1)){
             }
             String moduleType = this.getModuleType();
             String moduleType1 = moduleRespDt.getModuleType();
             if (moduleType == null) {
                if (moduleType1 != null) {
                   b = false;
                }
             }else if(moduleType.equals(moduleType1)){
             }
             String description = this.getDescription();
             String description1 = moduleRespDt.getDescription();
             if (description == null) {
                if (description1 != null) {
                   b = false;
                }
             }else if(description.equals(description1)){
             }
             String icon = this.getIcon();
             String icon1 = moduleRespDt.getIcon();
             if (icon == null) {
                if (icon1 != null) {
                label_00a3 :
                   b = false;
                }
             }else if(icon.equals(icon1)){
             }
             String url = this.getUrl();
             String url1 = moduleRespDt.getUrl();
             if (url == null) {
                if (url1 != null) {
                   b = false;
                }
             }else if(url.equals(url1)){
             }
             String creator = this.getCreator();
             String creator1 = moduleRespDt.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                label_00d3 :
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             String createdTime = this.getCreatedTime();
             String createdTime1 = moduleRespDt.getCreatedTime();
             if (createdTime == null) {
                if (createdTime1 != null) {
                   b = false;
                }
             }else if(createdTime.equals(createdTime1)){
             }
             String modifier = this.getModifier();
             String modifier1 = moduleRespDt.getModifier();
             if (modifier == null) {
                if (modifier1 != null) {
                label_0101 :
                   b = false;
                }
             }else if(modifier.equals(modifier1)){
             }
             String modifyTime = this.getModifyTime();
             String modifyTime1 = moduleRespDt.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String isValid = this.getIsValid();
             String isValid1 = moduleRespDt.getIsValid();
             if (isValid == null) {
                if (isValid1 != null) {
                   b = false;
                }
             }else if(isValid.equals(isValid1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getCreatedTime(){
       return this.createdTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getDescription(){
       return this.description;
    }
    public String getIcon(){
       return this.icon;
    }
    public String getIsValid(){
       return this.isValid;
    }
    public String getModifier(){
       return this.modifier;
    }
    public String getModifyTime(){
       return this.modifyTime;
    }
    public String getModuleCode(){
       return this.moduleCode;
    }
    public String getModuleName(){
       return this.moduleName;
    }
    public String getModuleOrder(){
       return this.moduleOrder;
    }
    public String getModuleType(){
       return this.moduleType;
    }
    public String getUrl(){
       return this.url;
    }
    public int hashCode(){
       String $moduleCode;
       int PRIME = 59;
       int result = 1;
       int i = (($moduleCode = this.getModuleCode()) == null)? 43: $moduleCode.hashCode();
       result = i + 59;
       String $moduleName = this.getModuleName();
       int i1 = result * 59;
       i = ($moduleName == null)? 43: $moduleName.hashCode();
       result = i1 + i;
       String $moduleOrder = this.getModuleOrder();
       i1 = result * 59;
       i = ($moduleOrder == null)? 43: $moduleOrder.hashCode();
       result = i1 + i;
       String $moduleType = this.getModuleType();
       i1 = result * 59;
       i = ($moduleType == null)? 43: $moduleType.hashCode();
       result = i1 + i;
       String $description = this.getDescription();
       i1 = result * 59;
       i = ($description == null)? 43: $description.hashCode();
       result = i1 + i;
       String icon = this.getIcon();
       i1 = result * 59;
       i = (icon == null)? 43: icon.hashCode();
       String url = this.getUrl();
       i1 = (i1 + i) * 59;
       i = (url == null)? 43: url.hashCode();
       String creator = this.getCreator();
       i1 = (i1 + i) * 59;
       i = (creator == null)? 43: creator.hashCode();
       String createdTime = this.getCreatedTime();
       i1 = (i1 + i) * 59;
       i = (createdTime == null)? 43: createdTime.hashCode();
       String modifier = this.getModifier();
       i1 = (i1 + i) * 59;
       i = (modifier == null)? 43: modifier.hashCode();
       String modifyTime = this.getModifyTime();
       i1 = (i1 + i) * 59;
       i = (modifyTime == null)? 43: modifyTime.hashCode();
       String isValid = this.getIsValid();
       i1 = (i1 + i) * 59;
       i = (isValid == null)? 43: isValid.hashCode();
       return (i1 + i);
    }
    public void setCreatedTime(String createdTime){
       this.createdTime = createdTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setDescription(String description){
       this.description = description;
    }
    public void setIcon(String icon){
       this.icon = icon;
    }
    public void setIsValid(String isValid){
       this.isValid = isValid;
    }
    public void setModifier(String modifier){
       this.modifier = modifier;
    }
    public void setModifyTime(String modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setModuleCode(String moduleCode){
       this.moduleCode = moduleCode;
    }
    public void setModuleName(String moduleName){
       this.moduleName = moduleName;
    }
    public void setModuleOrder(String moduleOrder){
       this.moduleOrder = moduleOrder;
    }
    public void setModuleType(String moduleType){
       this.moduleType = moduleType;
    }
    public void setUrl(String url){
       this.url = url;
    }
    public String toString(){
       return "ModuleRespDto\(moduleCode="+this.getModuleCode()+", moduleName="+this.getModuleName()+", moduleOrder="+this.getModuleOrder()+", moduleType="+this.getModuleType()+", description="+this.getDescription()+", icon="+this.getIcon()+", url="+this.getUrl()+", creator="+this.getCreator()+", createdTime="+this.getCreatedTime()+", modifier="+this.getModifier()+", modifyTime="+this.getModifyTime()+", isValid="+this.getIsValid()+"\)";
    }
}
