package com.taikang.fly.check.dto.resource.ResourceResDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import java.lang.StringBuilder;

public class ResourceResDto implements Serializable	// class@0001b0 from classes.dex
{
    private String description;
    private String parentId;
    private String resourceId;
    private String resourceName;
    private Integer resourceOrder;
    private String resourceType;
    private String signature;
    private String url;
    private static final long serialVersionUID = 0x21e31acb44c4c6a7;

    public void ResourceResDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ResourceResDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof ResourceResDto){
          b = false;
       }else {
          ResourceResDto resourceResD = o;
          if (!resourceResD.canEqual(this)) {
             b = false;
          }else {
             String resourceId = this.getResourceId();
             String resourceId1 = resourceResD.getResourceId();
             if (resourceId == null) {
                if (resourceId1 != null) {
                   b = false;
                }
             }else if(resourceId.equals(resourceId1)){
             }
             String resourceName = this.getResourceName();
             String resourceName1 = resourceResD.getResourceName();
             if (resourceName == null) {
                if (resourceName1 != null) {
                   b = false;
                }
             }else if(resourceName.equals(resourceName1)){
             }
             String parentId = this.getParentId();
             String parentId1 = resourceResD.getParentId();
             if (parentId == null) {
                if (parentId1 != null) {
                   b = false;
                }
             }else if(parentId.equals(parentId1)){
             }
             String url = this.getUrl();
             String url1 = resourceResD.getUrl();
             if (url == null) {
                if (url1 != null) {
                   b = false;
                }
             }else if(url.equals(url1)){
             }
             Integer resourceOrde = this.getResourceOrder();
             Integer resourceOrde1 = resourceResD.getResourceOrder();
             if (resourceOrde == null) {
                if (resourceOrde1 != null) {
                   b = false;
                }
             }else if(resourceOrde.equals(resourceOrde1)){
             }
             String signature = this.getSignature();
             String signature1 = resourceResD.getSignature();
             if (signature == null) {
                if (signature1 != null) {
                label_009a :
                   b = false;
                }
             }else if(signature.equals(signature1)){
             }
             String description = this.getDescription();
             String description1 = resourceResD.getDescription();
             if (description == null) {
                if (description1 != null) {
                   b = false;
                }
             }else if(description.equals(description1)){
             }
             String resourceType = this.getResourceType();
             String resourceType1 = resourceResD.getResourceType();
             if (resourceType == null) {
                if (resourceType1 != null) {
                   b = false;
                }
             }else if(resourceType.equals(resourceType1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getDescription(){
       return this.description;
    }
    public String getParentId(){
       return this.parentId;
    }
    public String getResourceId(){
       return this.resourceId;
    }
    public String getResourceName(){
       return this.resourceName;
    }
    public Integer getResourceOrder(){
       return this.resourceOrder;
    }
    public String getResourceType(){
       return this.resourceType;
    }
    public String getSignature(){
       return this.signature;
    }
    public String getUrl(){
       return this.url;
    }
    public int hashCode(){
       String $resourceId;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($resourceId = this.getResourceId()) == null)? i: $resourceId.hashCode();
       result = i1 + 59;
       String $resourceName = this.getResourceName();
       int i2 = result * 59;
       i1 = ($resourceName == null)? i: $resourceName.hashCode();
       result = i2 + i1;
       String $parentId = this.getParentId();
       i2 = result * 59;
       i1 = ($parentId == null)? i: $parentId.hashCode();
       result = i2 + i1;
       String $url = this.getUrl();
       i2 = result * 59;
       i1 = ($url == null)? i: $url.hashCode();
       result = i2 + i1;
       Integer $resourceOrder = this.getResourceOrder();
       i2 = result * 59;
       i1 = ($resourceOrder == null)? i: $resourceOrder.hashCode();
       result = i2 + i1;
       String signature = this.getSignature();
       i2 = result * 59;
       i1 = (signature == null)? i: signature.hashCode();
       String description = this.getDescription();
       i2 = (i2 + i1) * 59;
       i1 = (description == null)? i: description.hashCode();
       String resourceType = this.getResourceType();
       i1 = (i2 + i1) * 59;
       if (resourceType != null) {
          i = resourceType.hashCode();
       }
       return (i1 + i);
    }
    public void setDescription(String description){
       this.description = description;
    }
    public void setParentId(String parentId){
       this.parentId = parentId;
    }
    public void setResourceId(String resourceId){
       this.resourceId = resourceId;
    }
    public void setResourceName(String resourceName){
       this.resourceName = resourceName;
    }
    public void setResourceOrder(Integer resourceOrder){
       this.resourceOrder = resourceOrder;
    }
    public void setResourceType(String resourceType){
       this.resourceType = resourceType;
    }
    public void setSignature(String signature){
       this.signature = signature;
    }
    public void setUrl(String url){
       this.url = url;
    }
    public String toString(){
       return "ResourceResDto\(resourceId="+this.getResourceId()+", resourceName="+this.getResourceName()+", parentId="+this.getParentId()+", url="+this.getUrl()+", resourceOrder="+this.getResourceOrder()+", signature="+this.getSignature()+", description="+this.getDescription()+", resourceType="+this.getResourceType()+"\)";
    }
}
