package org.springframework.boot.loader.MainMethodRunner;
import java.lang.String;
import java.lang.Object;
import java.lang.Thread;
import java.lang.ClassLoader;
import java.lang.Class;
import java.lang.reflect.Method;

public class MainMethodRunner	// class@000533 from classes.dex
{
    private final String[] args;
    private final String mainClassName;

    public void MainMethodRunner(String mainClass,String[] args){
       super();
       this.mainClassName = mainClass;
       String[] stringArray = (args != null)? args.clone(): null;
       this.args = stringArray;
       return;
    }
    public void run(){
       Class mainClass = Thread.currentThread().getContextClassLoader().loadClass(this.mainClassName);
       Class[] uClassArray = new Class[]{String[].class};
       Method mainMethod = mainClass.getDeclaredMethod("main", uClassArray);
       Object[] objArray = new Object[]{this.args};
       mainMethod.invoke(null, objArray);
    }
}
