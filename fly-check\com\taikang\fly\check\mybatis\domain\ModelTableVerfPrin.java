package com.taikang.fly.check.mybatis.domain.ModelTableVerfPrin;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ModelTableVerfPrin	// class@00025c from classes.dex
{
    private String id;
    private String tableDesc;
    private String tableName;

    public void ModelTableVerfPrin(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ModelTableVerfPrin;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof ModelTableVerfPrin) {
             b = false;
          }else {
             ModelTableVerfPrin modelTableVe = o;
             if (!modelTableVe.canEqual(this)) {
                b = false;
             }else {
                String id = this.getId();
                String id1 = modelTableVe.getId();
                if (id == null) {
                   if (id1 != null) {
                      b = false;
                   }
                }else if(id.equals(id1)){
                }
                String tableName = this.getTableName();
                String tableName1 = modelTableVe.getTableName();
                if (tableName == null) {
                   if (tableName1 != null) {
                      b = false;
                   }
                }else if(tableName.equals(tableName1)){
                }
                String tableDesc = this.getTableDesc();
                String tableDesc1 = modelTableVe.getTableDesc();
                if (tableDesc == null) {
                   if (tableDesc1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!tableDesc.equals(tableDesc1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getId(){
       return this.id;
    }
    public String getTableDesc(){
       return this.tableDesc;
    }
    public String getTableName(){
       return this.tableName;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $tableName = this.getTableName();
       int i2 = result * 59;
       i1 = ($tableName == null)? i: $tableName.hashCode();
       result = i2 + i1;
       String $tableDesc = this.getTableDesc();
       i1 = result * 59;
       if ($tableDesc != null) {
          i = $tableDesc.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setTableDesc(String tableDesc){
       this.tableDesc = tableDesc;
    }
    public void setTableName(String tableName){
       this.tableName = tableName;
    }
    public String toString(){
       return "ModelTableVerfPrin\(id="+this.getId()+", tableName="+this.getTableName()+", tableDesc="+this.getTableDesc()+"\)";
    }
}
