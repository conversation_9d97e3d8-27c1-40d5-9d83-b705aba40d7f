package org.springframework.boot.loader.jar.JarURLConnection$JarEntryName;
import org.springframework.boot.loader.jar.StringSequence;
import java.lang.Object;
import java.io.ByteArrayOutputStream;
import java.lang.String;
import org.springframework.boot.loader.jar.AsciiBytes;
import java.lang.Character;
import java.lang.IllegalArgumentException;
import java.lang.StringBuilder;
import java.net.URLConnection;
import org.springframework.boot.loader.jar.JarURLConnection;
import java.net.URLEncoder;
import java.lang.IllegalStateException;
import java.lang.Throwable;
import java.lang.CharSequence;

class JarURLConnection$JarEntryName	// class@00055b from classes.dex
{
    private String contentType;
    private final StringSequence name;

    void JarURLConnection$JarEntryName(StringSequence spec){
       super();
       this.name = this.decode(spec);
    }
    private StringSequence decode(StringSequence source){
       if (!source.isEmpty() && source.indexOf('%') >= 0) {
          ByteArrayOutputStream uByteArrayOu = new ByteArrayOutputStream(source.length());
          this.write(source.toString(), uByteArrayOu);
          source = new StringSequence(AsciiBytes.toString(uByteArrayOu.toByteArray()));
       }
       return source;
    }
    private char decodeEscapeSequence(String source,int i){
       int ix = 16;
       int ix1 = -1;
       int hi = Character.digit(source.charAt((i + 1)), ix);
       int lo = Character.digit(source.charAt((i + 2)), ix);
       if (hi != ix1 && lo != ix1) {
          return (char)((hi << 4) + lo);
       }
       throw new IllegalArgumentException("Invalid encoded sequence \""+source.substring(i)+"\"");
    }
    private String deduceContentType(){
       String type = (this.isEmpty())? "x-java/jar": null;
       if (type == null) {
          type = URLConnection.guessContentTypeFromName(this.toString());
       }
       if (type == null) {
          type = "content/unknown";
       }
       return type;
    }
    public static JarURLConnection$JarEntryName get(StringSequence spec){
       return JarURLConnection$JarEntryName.get(spec, 0);
    }
    public static JarURLConnection$JarEntryName get(StringSequence spec,int beginIndex){
       JarURLConnection$JarEntryName jarEntryName = (spec.length() <= beginIndex)? JarURLConnection.access$000(): new JarURLConnection$JarEntryName(spec.subSequence(beginIndex));
       return jarEntryName;
    }
    private void write(String source,ByteArrayOutputStream outputStream){
       char c;
       int ix;
       int length = source.length();
       int i = 0;
       while (true) {
          if (i >= length) {
             return;
          }
          try{
             if ((c = source.charAt(i)) > 127) {
                char c1 = (char)c;
                this.write(URLEncoder.encode(String.valueOf(c1), "UTF-8"), outputStream);
             }else if(c == '%'){
                if ((ix = i + 2) >= length) {
                   break ;
                }else {
                   c = this.decodeEscapeSequence(source, i);
                   i = i + 2;
                }
             }
             outputStream.write(c);
          }catch(java.io.UnsupportedEncodingException e2){
             throw new IllegalStateException(e2);
          }
             i++;
       }
       throw new IllegalArgumentException("Invalid encoded sequence \""+source.substring(i)+"\"");
    }
    public String getContentType(){
       if (this.contentType == null) {
          this.contentType = this.deduceContentType();
       }
       return this.contentType;
    }
    public boolean isEmpty(){
       return this.name.isEmpty();
    }
    public CharSequence toCharSequence(){
       return this.name;
    }
    public String toString(){
       return this.name.toString();
    }
}
