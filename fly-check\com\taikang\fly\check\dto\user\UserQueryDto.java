package com.taikang.fly.check.dto.user.UserQueryDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class UserQueryDto implements Serializable	// class@0001c3 from classes.dex
{
    private String name;
    private String region;
    private String status;

    public void UserQueryDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof UserQueryDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof UserQueryDto) {
             b = false;
          }else {
             UserQueryDto userQueryDto = o;
             if (!userQueryDto.canEqual(this)) {
                b = false;
             }else {
                String name = this.getName();
                String name1 = userQueryDto.getName();
                if (name == null) {
                   if (name1 != null) {
                      b = false;
                   }
                }else if(name.equals(name1)){
                }
                String status = this.getStatus();
                String status1 = userQueryDto.getStatus();
                if (status == null) {
                   if (status1 != null) {
                      b = false;
                   }
                }else if(status.equals(status1)){
                }
                String region = this.getRegion();
                String region1 = userQueryDto.getRegion();
                if (region == null) {
                   if (region1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!region.equals(region1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getName(){
       return this.name;
    }
    public String getRegion(){
       return this.region;
    }
    public String getStatus(){
       return this.status;
    }
    public int hashCode(){
       String $name;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($name = this.getName()) == null)? i: $name.hashCode();
       result = i1 + 59;
       String $status = this.getStatus();
       int i2 = result * 59;
       i1 = ($status == null)? i: $status.hashCode();
       result = i2 + i1;
       String $region = this.getRegion();
       i1 = result * 59;
       if ($region != null) {
          i = $region.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setName(String name){
       this.name = name;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setStatus(String status){
       this.status = status;
    }
    public String toString(){
       return "UserQueryDto\(name="+this.getName()+", status="+this.getStatus()+", region="+this.getRegion()+"\)";
    }
}
