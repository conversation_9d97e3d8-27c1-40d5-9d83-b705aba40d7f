package com.taikang.fly.check.dto.SegmentationDto;
import java.lang.Object;
import java.lang.String;

public class SegmentationDto	// class@0000ad from classes.dex
{
    private String associatedField;
    private String sourceField;
    private String sourceTableName;
    private String targetFiled;
    private String targetTableName;

    public void SegmentationDto(){
       super();
    }
    public String getAssociatedField(){
       return this.associatedField;
    }
    public String getSourceField(){
       return this.sourceField;
    }
    public String getSourceTableName(){
       return this.sourceTableName;
    }
    public String getTargetFiled(){
       return this.targetFiled;
    }
    public String getTargetTableName(){
       return this.targetTableName;
    }
    public void setAssociatedField(String associatedField){
       this.associatedField = associatedField;
    }
    public void setSourceField(String sourceField){
       this.sourceField = sourceField;
    }
    public void setSourceTableName(String sourceTableName){
       this.sourceTableName = sourceTableName;
    }
    public void setTargetFiled(String targetFiled){
       this.targetFiled = targetFiled;
    }
    public void setTargetTableName(String targetTableName){
       this.targetTableName = targetTableName;
    }
}
