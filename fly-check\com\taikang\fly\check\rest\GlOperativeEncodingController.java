package com.taikang.fly.check.rest.GlOperativeEncodingController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import java.lang.Integer;
import com.taikang.fly.check.dto.operativeencoding.OperativeSearchDto;
import com.taikang.fly.check.dto.RmpResponse;
import com.taikang.fly.check.comm.NativePage;
import com.taikang.fly.check.service.GlOperativeEncodingService;

public class GlOperativeEncodingController	// class@000297 from classes.dex
{
    private GlOperativeEncodingService glOperativeEncodingService;
    private static final Logger log;

    static {
       GlOperativeEncodingController.log = LoggerFactory.getLogger(GlOperativeEncodingController.class);
    }
    public void GlOperativeEncodingController(){
       super();
    }
    public RmpResponse queryByPid(Integer page,Integer size,OperativeSearchDto operativeSearchDto){
       return RmpResponse.success(this.glOperativeEncodingService.queryList(page, size, operativeSearchDto));
    }
}
