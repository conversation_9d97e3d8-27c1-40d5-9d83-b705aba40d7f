package com.taikang.fly.check.vo.merge.MergeVO;
import java.lang.Object;
import java.lang.String;
import java.lang.Double;
import java.lang.StringBuilder;

public class MergeVO	// class@000381 from classes.dex
{
    private String datasource;
    private String policyBasis;
    private String ruleConnotation;
    private String ruleId;
    private String ruleName;
    private String ruleSql;
    private String ruleType;
    private Double totalAmount;
    private Double totalCount;

    public void MergeVO(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MergeVO;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof MergeVO){
          b = false;
       }else {
          MergeVO mergeVO = o;
          if (!mergeVO.canEqual(this)) {
             b = false;
          }else {
             String ruleType = this.getRuleType();
             String ruleType1 = mergeVO.getRuleType();
             if (ruleType == null) {
                if (ruleType1 != null) {
                   b = false;
                }
             }else if(ruleType.equals(ruleType1)){
             }
             String ruleName = this.getRuleName();
             String ruleName1 = mergeVO.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String ruleId = this.getRuleId();
             String ruleId1 = mergeVO.getRuleId();
             if (ruleId == null) {
                if (ruleId1 != null) {
                   b = false;
                }
             }else if(ruleId.equals(ruleId1)){
             }
             String ruleConnotat = this.getRuleConnotation();
             String ruleConnotat1 = mergeVO.getRuleConnotation();
             if (ruleConnotat == null) {
                if (ruleConnotat1 != null) {
                   b = false;
                }
             }else if(ruleConnotat.equals(ruleConnotat1)){
             }
             String ruleSql = this.getRuleSql();
             String ruleSql1 = mergeVO.getRuleSql();
             if (ruleSql == null) {
                if (ruleSql1 != null) {
                   b = false;
                }
             }else if(ruleSql.equals(ruleSql1)){
             }
             String datasource = this.getDatasource();
             String datasource1 = mergeVO.getDatasource();
             if (datasource == null) {
                if (datasource1 != null) {
                label_009f :
                   b = false;
                }
             }else if(datasource.equals(datasource1)){
             }
             Double totalCount = this.getTotalCount();
             Double totalCount1 = mergeVO.getTotalCount();
             if (totalCount == null) {
                if (totalCount1 != null) {
                   b = false;
                }
             }else if(totalCount.equals(totalCount1)){
             }
             Double totalAmount = this.getTotalAmount();
             Double totalAmount1 = mergeVO.getTotalAmount();
             if (totalAmount == null) {
                if (totalAmount1 != null) {
                label_00cd :
                   b = false;
                }
             }else if(totalAmount.equals(totalAmount1)){
             }
             String policyBasis = this.getPolicyBasis();
             String policyBasis1 = mergeVO.getPolicyBasis();
             if (policyBasis == null) {
                if (policyBasis1 != null) {
                   b = false;
                }
             }else if(policyBasis.equals(policyBasis1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getDatasource(){
       return this.datasource;
    }
    public String getPolicyBasis(){
       return this.policyBasis;
    }
    public String getRuleConnotation(){
       return this.ruleConnotation;
    }
    public String getRuleId(){
       return this.ruleId;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleSql(){
       return this.ruleSql;
    }
    public String getRuleType(){
       return this.ruleType;
    }
    public Double getTotalAmount(){
       return this.totalAmount;
    }
    public Double getTotalCount(){
       return this.totalCount;
    }
    public int hashCode(){
       String $ruleType;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($ruleType = this.getRuleType()) == null)? i: $ruleType.hashCode();
       result = i1 + 59;
       String $ruleName = this.getRuleName();
       int i2 = result * 59;
       i1 = ($ruleName == null)? i: $ruleName.hashCode();
       result = i2 + i1;
       String $ruleId = this.getRuleId();
       i2 = result * 59;
       i1 = ($ruleId == null)? i: $ruleId.hashCode();
       result = i2 + i1;
       String $ruleConnotation = this.getRuleConnotation();
       i2 = result * 59;
       i1 = ($ruleConnotation == null)? i: $ruleConnotation.hashCode();
       result = i2 + i1;
       String $ruleSql = this.getRuleSql();
       i2 = result * 59;
       i1 = ($ruleSql == null)? i: $ruleSql.hashCode();
       result = i2 + i1;
       String datasource = this.getDatasource();
       i2 = result * 59;
       i1 = (datasource == null)? i: datasource.hashCode();
       Double totalCount = this.getTotalCount();
       i2 = (i2 + i1) * 59;
       i1 = (totalCount == null)? i: totalCount.hashCode();
       Double totalAmount = this.getTotalAmount();
       i2 = (i2 + i1) * 59;
       i1 = (totalAmount == null)? i: totalAmount.hashCode();
       String policyBasis = this.getPolicyBasis();
       i1 = (i2 + i1) * 59;
       if (policyBasis != null) {
          i = policyBasis.hashCode();
       }
       return (i1 + i);
    }
    public void setDatasource(String datasource){
       this.datasource = datasource;
    }
    public void setPolicyBasis(String policyBasis){
       this.policyBasis = policyBasis;
    }
    public void setRuleConnotation(String ruleConnotation){
       this.ruleConnotation = ruleConnotation;
    }
    public void setRuleId(String ruleId){
       this.ruleId = ruleId;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleSql(String ruleSql){
       this.ruleSql = ruleSql;
    }
    public void setRuleType(String ruleType){
       this.ruleType = ruleType;
    }
    public void setTotalAmount(Double totalAmount){
       this.totalAmount = totalAmount;
    }
    public void setTotalCount(Double totalCount){
       this.totalCount = totalCount;
    }
    public String toString(){
       return "MergeVO\(ruleType="+this.getRuleType()+", ruleName="+this.getRuleName()+", ruleId="+this.getRuleId()+", ruleConnotation="+this.getRuleConnotation()+", ruleSql="+this.getRuleSql()+", datasource="+this.getDatasource()+", totalCount="+this.getTotalCount()+", totalAmount="+this.getTotalAmount()+", policyBasis="+this.getPolicyBasis()+"\)";
    }
}
