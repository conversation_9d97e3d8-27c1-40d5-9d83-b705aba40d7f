package com.taikang.fly.check.dto.mapstruct.UserModuleMappingImpl;
import com.taikang.fly.check.dto.mapstruct.UserModuleMapping;
import java.lang.Object;
import com.taikang.fly.check.mybatis.domain.Module;
import com.taikang.fly.check.dto.module.ModuleIndexResDto;
import java.lang.Integer;
import java.lang.String;
import com.taikang.fly.check.dto.mapper.common.TypeConversionMapper;
import java.time.LocalDateTime;
import com.taikang.fly.check.utils.DateUtils;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;
import com.taikang.fly.check.dto.usermodule.UserModuleRespAddDto;
import com.taikang.fly.check.mybatis.domain.UserModule;
import com.taikang.fly.check.security.core.DomainContext;
import com.taikang.fly.check.security.core.ThreadLocalContextHolder;
import com.taikang.fly.check.dto.user.UserDto;
import com.taikang.fly.check.utils.SequenceGenerator;
import com.taikang.fly.check.dto.usermodule.UserModuleRespEditDto;

public class UserModuleMappingImpl implements UserModuleMapping	// class@000180 from classes.dex
{
    private TypeConversionMapper typeConversionMapper;

    public void UserModuleMappingImpl(){
       super();
    }
    public ModuleIndexResDto moduleToModuleIndexResDto(Module module){
       ModuleIndexResDto moduleIndexR;
       if (module == null) {
          moduleIndexR = null;
       }else {
          moduleIndexR = new ModuleIndexResDto();
          moduleIndexR.setModuleOrder(this.typeConversionMapper.Integer2String(module.getModuleOrder()));
          moduleIndexR.setModuleCode(module.getModuleCode());
          moduleIndexR.setModuleName(module.getModuleName());
          moduleIndexR.setDescription(module.getDescription());
          moduleIndexR.setIcon(module.getIcon());
          moduleIndexR.setUrl(module.getUrl());
          moduleIndexR.setCreator(module.getCreator());
          moduleIndexR.setCreatedTime(DateUtils.formatLocalDateTime(module.getCreatedTime()));
          moduleIndexR.setModifyTime(DateUtils.formatLocalDateTime(module.getModifyTime()));
       }
       return moduleIndexR;
    }
    public List moduleToModuleIndexResDto(List modules){
       List list;
       if (modules == null) {
          list = null;
       }else {
          list = new ArrayList(modules.size());
          Iterator iterator = modules.iterator();
          while (iterator.hasNext()) {
             list.add(this.moduleToModuleIndexResDto(iterator.next()));
          }
       }
       return list;
    }
    public UserModule userModuleAddDtoToUserModule(UserModuleRespAddDto userModuleRespAddDto){
       UserModule userModule;
       if (userModuleRespAddDto == null) {
          userModule = null;
       }else {
          userModule = new UserModule();
          userModule.setUserCode(userModuleRespAddDto.getUserCode());
          userModule.setModuleCode(userModuleRespAddDto.getModuleCode());
          userModule.setSignature(userModuleRespAddDto.getSignature());
          userModule.setCreator(ThreadLocalContextHolder.getContext().getUserInfo().getUserCode());
          userModule.setModifyTime(LocalDateTime.now());
          userModule.setCreateTime(LocalDateTime.now());
          userModule.setIsValid("Y");
          userModule.setModby(ThreadLocalContextHolder.getContext().getUserInfo().getUserCode());
          userModule.setId(SequenceGenerator.getId());
       }
       return userModule;
    }
    public UserModule userModuleEditDtoToUserModule(UserModuleRespEditDto userModuleRespEditDto){
       UserModule userModule;
       if (userModuleRespEditDto == null) {
          userModule = null;
       }else {
          userModule = new UserModule();
          userModule.setId(userModuleRespEditDto.getId());
          userModule.setUserCode(userModuleRespEditDto.getUserCode());
          userModule.setModuleCode(userModuleRespEditDto.getModuleCode());
          userModule.setSignature(userModuleRespEditDto.getSignature());
          userModule.setModby(ThreadLocalContextHolder.getContext().getUserInfo().getUserCode());
          userModule.setModifyTime(LocalDateTime.now());
       }
       return userModule;
    }
}
