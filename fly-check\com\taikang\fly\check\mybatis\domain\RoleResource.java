package com.taikang.fly.check.mybatis.domain.RoleResource;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.util.Date;
import java.lang.StringBuilder;

public class RoleResource implements Serializable	// class@00026a from classes.dex
{
    private Date createTime;
    private String creator;
    private String id;
    private String modby;
    private Date modifyTime;
    private String resourceCode;
    private String roleCode;
    private String signature;
    private static final long serialVersionUID = 0x1;

    public void RoleResource(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof RoleResource;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof RoleResource){
          b = false;
       }else {
          RoleResource roleResource = o;
          if (!roleResource.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = roleResource.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String roleCode = this.getRoleCode();
             String roleCode1 = roleResource.getRoleCode();
             if (roleCode == null) {
                if (roleCode1 != null) {
                   b = false;
                }
             }else if(roleCode.equals(roleCode1)){
             }
             String resourceCode = this.getResourceCode();
             String resourceCode1 = roleResource.getResourceCode();
             if (resourceCode == null) {
                if (resourceCode1 != null) {
                   b = false;
                }
             }else if(resourceCode.equals(resourceCode1)){
             }
             String creator = this.getCreator();
             String creator1 = roleResource.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             Date createTime = this.getCreateTime();
             Date createTime1 = roleResource.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modby = this.getModby();
             String modby1 = roleResource.getModby();
             if (modby == null) {
                if (modby1 != null) {
                label_009d :
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             Date modifyTime = this.getModifyTime();
             Date modifyTime1 = roleResource.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String signature = this.getSignature();
             String signature1 = roleResource.getSignature();
             if (signature == null) {
                if (signature1 != null) {
                label_00c9 :
                   b = false;
                }
             }else if(signature.equals(signature1)){
             }
             b = true;
          }
       }
       return b;
    }
    public Date getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getId(){
       return this.id;
    }
    public String getModby(){
       return this.modby;
    }
    public Date getModifyTime(){
       return this.modifyTime;
    }
    public String getResourceCode(){
       return this.resourceCode;
    }
    public String getRoleCode(){
       return this.roleCode;
    }
    public String getSignature(){
       return this.signature;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $roleCode = this.getRoleCode();
       int i2 = result * 59;
       i1 = ($roleCode == null)? i: $roleCode.hashCode();
       result = i2 + i1;
       String $resourceCode = this.getResourceCode();
       i2 = result * 59;
       i1 = ($resourceCode == null)? i: $resourceCode.hashCode();
       result = i2 + i1;
       String $creator = this.getCreator();
       i2 = result * 59;
       i1 = ($creator == null)? i: $creator.hashCode();
       result = i2 + i1;
       Date $createTime = this.getCreateTime();
       i2 = result * 59;
       i1 = ($createTime == null)? i: $createTime.hashCode();
       result = i2 + i1;
       String modby = this.getModby();
       i2 = result * 59;
       i1 = (modby == null)? i: modby.hashCode();
       Date modifyTime = this.getModifyTime();
       i2 = (i2 + i1) * 59;
       i1 = (modifyTime == null)? i: modifyTime.hashCode();
       String signature = this.getSignature();
       i1 = (i2 + i1) * 59;
       if (signature != null) {
          i = signature.hashCode();
       }
       return (i1 + i);
    }
    public RoleResource setCreateTime(Date createTime){
       this.createTime = createTime;
       return this;
    }
    public RoleResource setCreator(String creator){
       this.creator = creator;
       return this;
    }
    public RoleResource setId(String id){
       this.id = id;
       return this;
    }
    public RoleResource setModby(String modby){
       this.modby = modby;
       return this;
    }
    public RoleResource setModifyTime(Date modifyTime){
       this.modifyTime = modifyTime;
       return this;
    }
    public RoleResource setResourceCode(String resourceCode){
       this.resourceCode = resourceCode;
       return this;
    }
    public RoleResource setRoleCode(String roleCode){
       this.roleCode = roleCode;
       return this;
    }
    public RoleResource setSignature(String signature){
       this.signature = signature;
       return this;
    }
    public String toString(){
       return "RoleResource\(id="+this.getId()+", roleCode="+this.getRoleCode()+", resourceCode="+this.getResourceCode()+", creator="+this.getCreator()+", createTime="+this.getCreateTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+", signature="+this.getSignature()+"\)";
    }
}
