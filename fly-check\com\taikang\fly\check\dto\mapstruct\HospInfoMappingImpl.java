package com.taikang.fly.check.dto.mapstruct.HospInfoMappingImpl;
import com.taikang.fly.check.dto.mapstruct.HospInfoMapping;
import java.lang.Object;
import com.taikang.fly.check.mybatis.domain.HospInfo;
import com.taikang.fly.check.dto.hospinfo.HospInfoDto;
import java.lang.String;
import com.taikang.fly.check.dto.hospinfo.HospInfoRespDto;
import java.util.Date;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.ArrayList;
import java.util.Iterator;

public class HospInfoMappingImpl implements HospInfoMapping	// class@00015d from classes.dex
{

    public void HospInfoMappingImpl(){
       super();
    }
    public HospInfoDto hospInfo2HospInfoDto(HospInfo hospInfo){
       HospInfoDto hospInfoDto;
       if (hospInfo == null) {
          hospInfoDto = null;
       }else {
          hospInfoDto = new HospInfoDto();
          hospInfoDto.setId(hospInfo.getId());
          hospInfoDto.setRegion(hospInfo.getRegion());
          hospInfoDto.setHospName(hospInfo.getHospName());
          hospInfoDto.setOraUserName(hospInfo.getOraUserName());
       }
       return hospInfoDto;
    }
    public HospInfoRespDto hospInfo2RespDto(HospInfo hospInfo){
       HospInfoRespDto hospInfoResp;
       if (hospInfo == null) {
          hospInfoResp = null;
       }else {
          hospInfoResp = new HospInfoRespDto();
          hospInfoResp.setId(hospInfo.getId());
          hospInfoResp.setRegion(hospInfo.getRegion());
          hospInfoResp.setHospName(hospInfo.getHospName());
          hospInfoResp.setOraUserName(hospInfo.getOraUserName());
          hospInfoResp.setCreator(hospInfo.getCreator());
          if (hospInfo.getCreatedTime() != null) {
             hospInfoResp.setCreatedTime(new SimpleDateFormat().format(hospInfo.getCreatedTime()));
          }
       }
       return hospInfoResp;
    }
    public List hospInfoList2RespDtoList(List hospInfoList){
       List list;
       if (hospInfoList == null) {
          list = null;
       }else {
          list = new ArrayList(hospInfoList.size());
          Iterator iterator = hospInfoList.iterator();
          while (iterator.hasNext()) {
             list.add(this.hospInfo2RespDto(iterator.next()));
          }
       }
       return list;
    }
}
