package com.taikang.fly.check.mybatis.dao.UserMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import java.lang.Integer;
import java.util.List;
import com.taikang.fly.check.mybatis.domain.User;
import java.util.Map;

public interface abstract UserMapper implements BaseMapper	// class@000225 from classes.dex
{

    Integer countByRegion(String p0);
    List findAll();
    User findByName(String p0);
    User findByNameAndRegion(String p0,String p1);
    User findByPrimaryKey(String p0);
    List queryListPageByParams(Map p0);
    Integer queryListPageCountByParams(Map p0);
    Integer saveUser(User p0);
    Integer updateUserById(User p0);
}
