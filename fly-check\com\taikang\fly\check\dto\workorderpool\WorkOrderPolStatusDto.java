package com.taikang.fly.check.dto.workorderpool.WorkOrderPolStatusDto;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class WorkOrderPolStatusDto	// class@0001c8 from classes.dex
{
    private String id;
    private String receiver;
    private String schedule;

    public void WorkOrderPolStatusDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof WorkOrderPolStatusDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof WorkOrderPolStatusDto) {
             b = false;
          }else {
             WorkOrderPolStatusDto workOrderPol = o;
             if (!workOrderPol.canEqual(this)) {
                b = false;
             }else {
                String id = this.getId();
                String id1 = workOrderPol.getId();
                if (id == null) {
                   if (id1 != null) {
                      b = false;
                   }
                }else if(id.equals(id1)){
                }
                String receiver = this.getReceiver();
                String receiver1 = workOrderPol.getReceiver();
                if (receiver == null) {
                   if (receiver1 != null) {
                      b = false;
                   }
                }else if(receiver.equals(receiver1)){
                }
                String schedule = this.getSchedule();
                String schedule1 = workOrderPol.getSchedule();
                if (schedule == null) {
                   if (schedule1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!schedule.equals(schedule1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getId(){
       return this.id;
    }
    public String getReceiver(){
       return this.receiver;
    }
    public String getSchedule(){
       return this.schedule;
    }
    public int hashCode(){
       String $id;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($id = this.getId()) == null)? i: $id.hashCode();
       result = i1 + 59;
       String $receiver = this.getReceiver();
       int i2 = result * 59;
       i1 = ($receiver == null)? i: $receiver.hashCode();
       result = i2 + i1;
       String $schedule = this.getSchedule();
       i1 = result * 59;
       if ($schedule != null) {
          i = $schedule.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setReceiver(String receiver){
       this.receiver = receiver;
    }
    public void setSchedule(String schedule){
       this.schedule = schedule;
    }
    public String toString(){
       return "WorkOrderPolStatusDto\(id="+this.getId()+", receiver="+this.getReceiver()+", schedule="+this.getSchedule()+"\)";
    }
}
