package com.taikang.fly.check.dto.mapstruct.YbDiagnosisTreatmentListMapping;
import com.taikang.fly.check.mybatis.domain.YbDiagnosisTreatmentList;
import com.taikang.fly.check.dto.ybDiagnosisTreatmentList.YbDiagnosisTreatmentListRespDto;
import com.taikang.fly.check.mybatis.domain.DiagnosisTreatment;
import java.util.List;

public interface abstract YbDiagnosisTreatmentListMapping	// class@000185 from classes.dex
{

    YbDiagnosisTreatmentListRespDto domainToRespDto(YbDiagnosisTreatmentList p0);
    YbDiagnosisTreatmentListRespDto domainsToRespDtoList(DiagnosisTreatment p0);
    List domainsToRespDtoList(List p0);
}
