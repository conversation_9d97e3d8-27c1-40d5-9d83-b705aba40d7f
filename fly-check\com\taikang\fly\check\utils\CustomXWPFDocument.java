package com.taikang.fly.check.utils.CustomXWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import java.io.InputStream;
import org.apache.poi.openxml4j.opc.OPCPackage;

public class CustomXWPFDocument extends XWPFDocument	// class@00032f from classes.dex
{

    public void CustomXWPFDocument(){
       super();
    }
    public void CustomXWPFDocument(InputStream in){
       super(in);
    }
    public void CustomXWPFDocument(OPCPackage pkg){
       super(pkg);
    }
}
