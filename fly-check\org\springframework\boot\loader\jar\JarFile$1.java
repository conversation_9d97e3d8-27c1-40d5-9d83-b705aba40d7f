package org.springframework.boot.loader.jar.JarFile$1;
import org.springframework.boot.loader.jar.CentralDirectoryVisitor;
import org.springframework.boot.loader.jar.JarFile;
import java.lang.Object;
import org.springframework.boot.loader.jar.CentralDirectoryFileHeader;
import org.springframework.boot.loader.jar.AsciiBytes;
import org.springframework.boot.loader.jar.CentralDirectoryEndRecord;
import org.springframework.boot.loader.data.RandomAccessData;

class JarFile$1 implements CentralDirectoryVisitor	// class@000553 from classes.dex
{
    final JarFile this$0;

    void JarFile$1(JarFile this$0){
       this.this$0 = this$0;
       super();
    }
    public void visitEnd(){
    }
    public void visitFileHeader(CentralDirectoryFileHeader fileHeader,int dataOffset){
       AsciiBytes name = fileHeader.getName();
       if (name.startsWith(JarFile.access$000()) && name.endsWith(JarFile.access$100())) {
          JarFile.access$202(this.this$0, true);
       }
       return;
    }
    public void visitStart(CentralDirectoryEndRecord endRecord,RandomAccessData centralDirectoryData){
    }
}
