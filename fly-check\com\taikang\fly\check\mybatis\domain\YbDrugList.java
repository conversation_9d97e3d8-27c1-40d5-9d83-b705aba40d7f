package com.taikang.fly.check.mybatis.domain.YbDrugList;
import java.lang.Object;
import java.lang.String;
import java.util.Date;
import java.lang.StringBuilder;

public class YbDrugList	// class@00027a from classes.dex
{
    private String birtPaymentRatio;
    private String busiPaymentRatio;
    private Date createTime;
    private String creator;
    private String depaPaymentRatio;
    private String dosageForm;
    private String drugCode;
    private String drugName;
    private String highestPrice;
    private String hospPaymentRatio;
    private String id;
    private String level1HighestPrice;
    private String level2HighestPrice;
    private String level3HighestPrice;
    private String modby;
    private Date modifyTime;
    private String paymentCategory;
    private String paymentUnit;
    private String remark;
    private String retirementPrice;

    public void YbDrugList(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof YbDrugList;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof YbDrugList){
          b = false;
       }else {
          YbDrugList ybDrugList = o;
          if (!ybDrugList.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = ybDrugList.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String drugCode = this.getDrugCode();
             String drugCode1 = ybDrugList.getDrugCode();
             if (drugCode == null) {
                if (drugCode1 != null) {
                   b = false;
                }
             }else if(drugCode.equals(drugCode1)){
             }
             String drugName = this.getDrugName();
             String drugName1 = ybDrugList.getDrugName();
             if (drugName == null) {
                if (drugName1 != null) {
                   b = false;
                }
             }else if(drugName.equals(drugName1)){
             }
             String dosageForm = this.getDosageForm();
             String dosageForm1 = ybDrugList.getDosageForm();
             if (dosageForm == null) {
                if (dosageForm1 != null) {
                   b = false;
                }
             }else if(dosageForm.equals(dosageForm1)){
             }
             String paymentUnit = this.getPaymentUnit();
             String paymentUnit1 = ybDrugList.getPaymentUnit();
             if (paymentUnit == null) {
                if (paymentUnit1 != null) {
                   b = false;
                }
             }else if(paymentUnit.equals(paymentUnit1)){
             }
             String paymentCateg = this.getPaymentCategory();
             String paymentCateg1 = ybDrugList.getPaymentCategory();
             if (paymentCateg == null) {
                if (paymentCateg1 != null) {
                label_00a5 :
                   b = false;
                }
             }else if(paymentCateg.equals(paymentCateg1)){
             }
             String highestPrice = this.getHighestPrice();
             String highestPrice1 = ybDrugList.getHighestPrice();
             if (highestPrice == null) {
                if (highestPrice1 != null) {
                   b = false;
                }
             }else if(highestPrice.equals(highestPrice1)){
             }
             String level3Highes = this.getLevel3HighestPrice();
             String level3Highes1 = ybDrugList.getLevel3HighestPrice();
             if (level3Highes == null) {
                if (level3Highes1 != null) {
                label_00d7 :
                   b = false;
                }
             }else if(level3Highes.equals(level3Highes1)){
             }
             String level2Highes = this.getLevel2HighestPrice();
             String level2Highes1 = ybDrugList.getLevel2HighestPrice();
             if (level2Highes == null) {
                if (level2Highes1 != null) {
                   b = false;
                }
             }else if(level2Highes.equals(level2Highes1)){
             }
             String level1Highes = this.getLevel1HighestPrice();
             String level1Highes1 = ybDrugList.getLevel1HighestPrice();
             if (level1Highes == null) {
                if (level1Highes1 != null) {
                label_0109 :
                   b = false;
                }
             }else if(level1Highes.equals(level1Highes1)){
             }
             String retirementPr = this.getRetirementPrice();
             String retirementPr1 = ybDrugList.getRetirementPrice();
             if (retirementPr == null) {
                if (retirementPr1 != null) {
                   b = false;
                }
             }else if(retirementPr.equals(retirementPr1)){
             }
             String depaPaymentR = this.getDepaPaymentRatio();
             String depaPaymentR1 = ybDrugList.getDepaPaymentRatio();
             if (depaPaymentR == null) {
                if (depaPaymentR1 != null) {
                label_013b :
                   b = false;
                }
             }else if(depaPaymentR.equals(depaPaymentR1)){
             }
             String hospPaymentR = this.getHospPaymentRatio();
             String hospPaymentR1 = ybDrugList.getHospPaymentRatio();
             if (hospPaymentR == null) {
                if (hospPaymentR1 != null) {
                   b = false;
                }
             }else if(hospPaymentR.equals(hospPaymentR1)){
             }
             String busiPaymentR = this.getBusiPaymentRatio();
             String busiPaymentR1 = ybDrugList.getBusiPaymentRatio();
             if (busiPaymentR == null) {
                if (busiPaymentR1 != null) {
                   b = false;
                }
             }else if(busiPaymentR.equals(busiPaymentR1)){
             }
             String birtPaymentR = this.getBirtPaymentRatio();
             String birtPaymentR1 = ybDrugList.getBirtPaymentRatio();
             if (birtPaymentR == null) {
                if (birtPaymentR1 != null) {
                label_0183 :
                   b = false;
                }
             }else if(birtPaymentR.equals(birtPaymentR1)){
             }
             String remark = this.getRemark();
             String remark1 = ybDrugList.getRemark();
             if (remark == null) {
                if (remark1 != null) {
                   b = false;
                }
             }else if(remark.equals(remark1)){
             }
             String creator = this.getCreator();
             String creator1 = ybDrugList.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                label_01b5 :
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             Date createTime = this.getCreateTime();
             Date createTime1 = ybDrugList.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modby = this.getModby();
             String modby1 = ybDrugList.getModby();
             if (modby == null) {
                if (modby1 != null) {
                label_01e5 :
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             Date modifyTime = this.getModifyTime();
             Date modifyTime1 = ybDrugList.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                label_01ff :
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getBirtPaymentRatio(){
       return this.birtPaymentRatio;
    }
    public String getBusiPaymentRatio(){
       return this.busiPaymentRatio;
    }
    public Date getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getDepaPaymentRatio(){
       return this.depaPaymentRatio;
    }
    public String getDosageForm(){
       return this.dosageForm;
    }
    public String getDrugCode(){
       return this.drugCode;
    }
    public String getDrugName(){
       return this.drugName;
    }
    public String getHighestPrice(){
       return this.highestPrice;
    }
    public String getHospPaymentRatio(){
       return this.hospPaymentRatio;
    }
    public String getId(){
       return this.id;
    }
    public String getLevel1HighestPrice(){
       return this.level1HighestPrice;
    }
    public String getLevel2HighestPrice(){
       return this.level2HighestPrice;
    }
    public String getLevel3HighestPrice(){
       return this.level3HighestPrice;
    }
    public String getModby(){
       return this.modby;
    }
    public Date getModifyTime(){
       return this.modifyTime;
    }
    public String getPaymentCategory(){
       return this.paymentCategory;
    }
    public String getPaymentUnit(){
       return this.paymentUnit;
    }
    public String getRemark(){
       return this.remark;
    }
    public String getRetirementPrice(){
       return this.retirementPrice;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $drugCode = this.getDrugCode();
       int i1 = result * 59;
       i = ($drugCode == null)? 43: $drugCode.hashCode();
       result = i1 + i;
       String $drugName = this.getDrugName();
       i1 = result * 59;
       i = ($drugName == null)? 43: $drugName.hashCode();
       result = i1 + i;
       String $dosageForm = this.getDosageForm();
       i1 = result * 59;
       i = ($dosageForm == null)? 43: $dosageForm.hashCode();
       result = i1 + i;
       String $paymentUnit = this.getPaymentUnit();
       i1 = result * 59;
       i = ($paymentUnit == null)? 43: $paymentUnit.hashCode();
       result = i1 + i;
       String paymentCateg = this.getPaymentCategory();
       i1 = result * 59;
       i = (paymentCateg == null)? 43: paymentCateg.hashCode();
       String highestPrice = this.getHighestPrice();
       i1 = (i1 + i) * 59;
       i = (highestPrice == null)? 43: highestPrice.hashCode();
       String level3Highes = this.getLevel3HighestPrice();
       i1 = (i1 + i) * 59;
       i = (level3Highes == null)? 43: level3Highes.hashCode();
       String level2Highes = this.getLevel2HighestPrice();
       i1 = (i1 + i) * 59;
       i = (level2Highes == null)? 43: level2Highes.hashCode();
       String level1Highes = this.getLevel1HighestPrice();
       i1 = (i1 + i) * 59;
       i = (level1Highes == null)? 43: level1Highes.hashCode();
       String retirementPr = this.getRetirementPrice();
       i1 = (i1 + i) * 59;
       i = (retirementPr == null)? 43: retirementPr.hashCode();
       String depaPaymentR = this.getDepaPaymentRatio();
       i1 = (i1 + i) * 59;
       i = (depaPaymentR == null)? 43: depaPaymentR.hashCode();
       String hospPaymentR = this.getHospPaymentRatio();
       i1 = (i1 + i) * 59;
       i = (hospPaymentR == null)? 43: hospPaymentR.hashCode();
       String busiPaymentR = this.getBusiPaymentRatio();
       i1 = (i1 + i) * 59;
       i = (busiPaymentR == null)? 43: busiPaymentR.hashCode();
       String birtPaymentR = this.getBirtPaymentRatio();
       i1 = (i1 + i) * 59;
       i = (birtPaymentR == null)? 43: birtPaymentR.hashCode();
       String remark = this.getRemark();
       i1 = (i1 + i) * 59;
       i = (remark == null)? 43: remark.hashCode();
       String creator = this.getCreator();
       i1 = (i1 + i) * 59;
       i = (creator == null)? 43: creator.hashCode();
       Date createTime = this.getCreateTime();
       i1 = (i1 + i) * 59;
       i = (createTime == null)? 43: createTime.hashCode();
       String modby = this.getModby();
       i1 = (i1 + i) * 59;
       i = (modby == null)? 43: modby.hashCode();
       Date modifyTime = this.getModifyTime();
       i1 = (i1 + i) * 59;
       i = (modifyTime == null)? 43: modifyTime.hashCode();
       return (i1 + i);
    }
    public void setBirtPaymentRatio(String birtPaymentRatio){
       this.birtPaymentRatio = birtPaymentRatio;
    }
    public void setBusiPaymentRatio(String busiPaymentRatio){
       this.busiPaymentRatio = busiPaymentRatio;
    }
    public void setCreateTime(Date createTime){
       this.createTime = createTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setDepaPaymentRatio(String depaPaymentRatio){
       this.depaPaymentRatio = depaPaymentRatio;
    }
    public void setDosageForm(String dosageForm){
       this.dosageForm = dosageForm;
    }
    public void setDrugCode(String drugCode){
       this.drugCode = drugCode;
    }
    public void setDrugName(String drugName){
       this.drugName = drugName;
    }
    public void setHighestPrice(String highestPrice){
       this.highestPrice = highestPrice;
    }
    public void setHospPaymentRatio(String hospPaymentRatio){
       this.hospPaymentRatio = hospPaymentRatio;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setLevel1HighestPrice(String level1HighestPrice){
       this.level1HighestPrice = level1HighestPrice;
    }
    public void setLevel2HighestPrice(String level2HighestPrice){
       this.level2HighestPrice = level2HighestPrice;
    }
    public void setLevel3HighestPrice(String level3HighestPrice){
       this.level3HighestPrice = level3HighestPrice;
    }
    public void setModby(String modby){
       this.modby = modby;
    }
    public void setModifyTime(Date modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setPaymentCategory(String paymentCategory){
       this.paymentCategory = paymentCategory;
    }
    public void setPaymentUnit(String paymentUnit){
       this.paymentUnit = paymentUnit;
    }
    public void setRemark(String remark){
       this.remark = remark;
    }
    public void setRetirementPrice(String retirementPrice){
       this.retirementPrice = retirementPrice;
    }
    public String toString(){
       return "YbDrugList\(id="+this.getId()+", drugCode="+this.getDrugCode()+", drugName="+this.getDrugName()+", dosageForm="+this.getDosageForm()+", paymentUnit="+this.getPaymentUnit()+", paymentCategory="+this.getPaymentCategory()+", highestPrice="+this.getHighestPrice()+", level3HighestPrice="+this.getLevel3HighestPrice()+", level2HighestPrice="+this.getLevel2HighestPrice()+", level1HighestPrice="+this.getLevel1HighestPrice()+", retirementPrice="+this.getRetirementPrice()+", depaPaymentRatio="+this.getDepaPaymentRatio()+", hospPaymentRatio="+this.getHospPaymentRatio()+", busiPaymentRatio="+this.getBusiPaymentRatio()+", birtPaymentRatio="+this.getBirtPaymentRatio()+", remark="+this.getRemark()+", creator="+this.getCreator()+", createTime="+this.getCreateTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+"\)";
    }
}
