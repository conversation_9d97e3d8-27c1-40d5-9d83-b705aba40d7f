package com.taikang.fly.check.dto.plan.PlanLogRespDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.util.Date;
import java.lang.StringBuilder;

public class PlanLogRespDto implements Serializable	// class@00019f from classes.dex
{
    private Date createTime;
    private String creator;
    private Date dataEnd;
    private String dataSources;
    private Date dataStart;
    private String downloadStatus;
    private String errorRules;
    private String id;
    private Date missionEnd;
    private String missionMessage;
    private String missionName;
    private Date missionStart;
    private String missionStatus;
    private String modby;
    private Date modifyTime;
    private String redFiled1;
    private String redFiled2;
    private String redFiled3;
    private String redFiled4;
    private String redFiled5;
    private String rules;
    private String status;
    private static final long serialVersionUID = 0x1;

    public void PlanLogRespDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof PlanLogRespDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof PlanLogRespDto){
          b = false;
       }else {
          PlanLogRespDto planLogRespD = o;
          if (!planLogRespD.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = planLogRespD.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String missionName = this.getMissionName();
             String missionName1 = planLogRespD.getMissionName();
             if (missionName == null) {
                if (missionName1 != null) {
                   b = false;
                }
             }else if(missionName.equals(missionName1)){
             }
             String creator = this.getCreator();
             String creator1 = planLogRespD.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             Date createTime = this.getCreateTime();
             Date createTime1 = planLogRespD.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modby = this.getModby();
             String modby1 = planLogRespD.getModby();
             if (modby == null) {
                if (modby1 != null) {
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             Date modifyTime = this.getModifyTime();
             Date modifyTime1 = planLogRespD.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                label_00a5 :
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             Date dataStart = this.getDataStart();
             Date dataStart1 = planLogRespD.getDataStart();
             if (dataStart == null) {
                if (dataStart1 != null) {
                label_00bf :
                   b = false;
                }
             }else if(dataStart.equals(dataStart1)){
             }
             Date dataEnd = this.getDataEnd();
             Date dataEnd1 = planLogRespD.getDataEnd();
             if (dataEnd == null) {
                if (dataEnd1 != null) {
                   b = false;
                }
             }else if(dataEnd.equals(dataEnd1)){
             }
             String rules = this.getRules();
             String rules1 = planLogRespD.getRules();
             if (rules == null) {
                if (rules1 != null) {
                label_00ef :
                   b = false;
                }
             }else if(rules.equals(rules1)){
             }
             Date missionStart = this.getMissionStart();
             Date missionStart1 = planLogRespD.getMissionStart();
             if (missionStart == null) {
                if (missionStart1 != null) {
                   b = false;
                }
             }else if(missionStart.equals(missionStart1)){
             }
             String missionStatu = this.getMissionStatus();
             String missionStatu1 = planLogRespD.getMissionStatus();
             if (missionStatu == null) {
                if (missionStatu1 != null) {
                label_0121 :
                   b = false;
                }
             }else if(missionStatu.equals(missionStatu1)){
             }
             Date missionEnd = this.getMissionEnd();
             Date missionEnd1 = planLogRespD.getMissionEnd();
             if (missionEnd == null) {
                if (missionEnd1 != null) {
                   b = false;
                }
             }else if(missionEnd.equals(missionEnd1)){
             }
             String status = this.getStatus();
             String status1 = planLogRespD.getStatus();
             if (status == null) {
                if (status1 != null) {
                label_0151 :
                   b = false;
                }
             }else if(status.equals(status1)){
             }
             String redFiled1 = this.getRedFiled1();
             String redFiled11 = planLogRespD.getRedFiled1();
             if (redFiled1 == null) {
                if (redFiled11 != null) {
                   b = false;
                }
             }else if(redFiled1.equals(redFiled11)){
             }
             String redFiled2 = this.getRedFiled2();
             String redFiled21 = planLogRespD.getRedFiled2();
             if (redFiled2 == null) {
                if (redFiled21 != null) {
                label_0185 :
                   b = false;
                }
             }else if(redFiled2.equals(redFiled21)){
             }
             String redFiled3 = this.getRedFiled3();
             String redFiled31 = planLogRespD.getRedFiled3();
             if (redFiled3 == null) {
                if (redFiled31 != null) {
                   b = false;
                }
             }else if(redFiled3.equals(redFiled31)){
             }
             String redFiled4 = this.getRedFiled4();
             String redFiled41 = planLogRespD.getRedFiled4();
             if (redFiled4 == null) {
                if (redFiled41 != null) {
                label_01b9 :
                   b = false;
                }
             }else if(redFiled4.equals(redFiled41)){
             }
             String redFiled5 = this.getRedFiled5();
             String redFiled51 = planLogRespD.getRedFiled5();
             if (redFiled5 == null) {
                if (redFiled51 != null) {
                   b = false;
                }
             }else if(redFiled5.equals(redFiled51)){
             }
             String missionMessa = this.getMissionMessage();
             String missionMessa1 = planLogRespD.getMissionMessage();
             if (missionMessa == null) {
                if (missionMessa1 != null) {
                label_01ed :
                   b = false;
                }
             }else if(missionMessa.equals(missionMessa1)){
             }
             String downloadStat = this.getDownloadStatus();
             String downloadStat1 = planLogRespD.getDownloadStatus();
             if (downloadStat == null) {
                if (downloadStat1 != null) {
                   b = false;
                }
             }else if(downloadStat.equals(downloadStat1)){
             }
             String errorRules = this.getErrorRules();
             String errorRules1 = planLogRespD.getErrorRules();
             if (errorRules == null) {
                if (errorRules1 != null) {
                   b = false;
                }
             }else if(errorRules.equals(errorRules1)){
             }
             String dataSources = this.getDataSources();
             String dataSources1 = planLogRespD.getDataSources();
             if (dataSources == null) {
                if (dataSources1 != null) {
                label_0235 :
                   b = false;
                }
             }else if(dataSources.equals(dataSources1)){
             }
             b = true;
          }
       }
       return b;
    }
    public Date getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public Date getDataEnd(){
       return this.dataEnd;
    }
    public String getDataSources(){
       return this.dataSources;
    }
    public Date getDataStart(){
       return this.dataStart;
    }
    public String getDownloadStatus(){
       return this.downloadStatus;
    }
    public String getErrorRules(){
       return this.errorRules;
    }
    public String getId(){
       return this.id;
    }
    public Date getMissionEnd(){
       return this.missionEnd;
    }
    public String getMissionMessage(){
       return this.missionMessage;
    }
    public String getMissionName(){
       return this.missionName;
    }
    public Date getMissionStart(){
       return this.missionStart;
    }
    public String getMissionStatus(){
       return this.missionStatus;
    }
    public String getModby(){
       return this.modby;
    }
    public Date getModifyTime(){
       return this.modifyTime;
    }
    public String getRedFiled1(){
       return this.redFiled1;
    }
    public String getRedFiled2(){
       return this.redFiled2;
    }
    public String getRedFiled3(){
       return this.redFiled3;
    }
    public String getRedFiled4(){
       return this.redFiled4;
    }
    public String getRedFiled5(){
       return this.redFiled5;
    }
    public String getRules(){
       return this.rules;
    }
    public String getStatus(){
       return this.status;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $missionName = this.getMissionName();
       int i1 = result * 59;
       i = ($missionName == null)? 43: $missionName.hashCode();
       result = i1 + i;
       String $creator = this.getCreator();
       i1 = result * 59;
       i = ($creator == null)? 43: $creator.hashCode();
       result = i1 + i;
       Date $createTime = this.getCreateTime();
       i1 = result * 59;
       i = ($createTime == null)? 43: $createTime.hashCode();
       result = i1 + i;
       String $modby = this.getModby();
       i1 = result * 59;
       i = ($modby == null)? 43: $modby.hashCode();
       result = i1 + i;
       Date modifyTime = this.getModifyTime();
       i1 = result * 59;
       i = (modifyTime == null)? 43: modifyTime.hashCode();
       Date dataStart = this.getDataStart();
       i1 = (i1 + i) * 59;
       i = (dataStart == null)? 43: dataStart.hashCode();
       Date dataEnd = this.getDataEnd();
       i1 = (i1 + i) * 59;
       i = (dataEnd == null)? 43: dataEnd.hashCode();
       String rules = this.getRules();
       i1 = (i1 + i) * 59;
       i = (rules == null)? 43: rules.hashCode();
       Date missionStart = this.getMissionStart();
       i1 = (i1 + i) * 59;
       i = (missionStart == null)? 43: missionStart.hashCode();
       String missionStatu = this.getMissionStatus();
       i1 = (i1 + i) * 59;
       i = (missionStatu == null)? 43: missionStatu.hashCode();
       Date missionEnd = this.getMissionEnd();
       i1 = (i1 + i) * 59;
       i = (missionEnd == null)? 43: missionEnd.hashCode();
       String status = this.getStatus();
       i1 = (i1 + i) * 59;
       i = (status == null)? 43: status.hashCode();
       String redFiled1 = this.getRedFiled1();
       i1 = (i1 + i) * 59;
       i = (redFiled1 == null)? 43: redFiled1.hashCode();
       String redFiled2 = this.getRedFiled2();
       i1 = (i1 + i) * 59;
       i = (redFiled2 == null)? 43: redFiled2.hashCode();
       String redFiled3 = this.getRedFiled3();
       i1 = (i1 + i) * 59;
       i = (redFiled3 == null)? 43: redFiled3.hashCode();
       String redFiled4 = this.getRedFiled4();
       i1 = (i1 + i) * 59;
       i = (redFiled4 == null)? 43: redFiled4.hashCode();
       String redFiled5 = this.getRedFiled5();
       i1 = (i1 + i) * 59;
       i = (redFiled5 == null)? 43: redFiled5.hashCode();
       String missionMessa = this.getMissionMessage();
       i1 = (i1 + i) * 59;
       i = (missionMessa == null)? 43: missionMessa.hashCode();
       String downloadStat = this.getDownloadStatus();
       i1 = (i1 + i) * 59;
       i = (downloadStat == null)? 43: downloadStat.hashCode();
       String errorRules = this.getErrorRules();
       i1 = (i1 + i) * 59;
       i = (errorRules == null)? 43: errorRules.hashCode();
       String dataSources = this.getDataSources();
       i1 = (i1 + i) * 59;
       i = (dataSources == null)? 43: dataSources.hashCode();
       return (i1 + i);
    }
    public void setCreateTime(Date createTime){
       this.createTime = createTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setDataEnd(Date dataEnd){
       this.dataEnd = dataEnd;
    }
    public void setDataSources(String dataSources){
       this.dataSources = dataSources;
    }
    public void setDataStart(Date dataStart){
       this.dataStart = dataStart;
    }
    public void setDownloadStatus(String downloadStatus){
       this.downloadStatus = downloadStatus;
    }
    public void setErrorRules(String errorRules){
       this.errorRules = errorRules;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setMissionEnd(Date missionEnd){
       this.missionEnd = missionEnd;
    }
    public void setMissionMessage(String missionMessage){
       this.missionMessage = missionMessage;
    }
    public void setMissionName(String missionName){
       this.missionName = missionName;
    }
    public void setMissionStart(Date missionStart){
       this.missionStart = missionStart;
    }
    public void setMissionStatus(String missionStatus){
       this.missionStatus = missionStatus;
    }
    public void setModby(String modby){
       this.modby = modby;
    }
    public void setModifyTime(Date modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setRedFiled1(String redFiled1){
       this.redFiled1 = redFiled1;
    }
    public void setRedFiled2(String redFiled2){
       this.redFiled2 = redFiled2;
    }
    public void setRedFiled3(String redFiled3){
       this.redFiled3 = redFiled3;
    }
    public void setRedFiled4(String redFiled4){
       this.redFiled4 = redFiled4;
    }
    public void setRedFiled5(String redFiled5){
       this.redFiled5 = redFiled5;
    }
    public void setRules(String rules){
       this.rules = rules;
    }
    public void setStatus(String status){
       this.status = status;
    }
    public String toString(){
       return "PlanLogRespDto\(id="+this.getId()+", missionName="+this.getMissionName()+", creator="+this.getCreator()+", createTime="+this.getCreateTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+", dataStart="+this.getDataStart()+", dataEnd="+this.getDataEnd()+", rules="+this.getRules()+", missionStart="+this.getMissionStart()+", missionStatus="+this.getMissionStatus()+", missionEnd="+this.getMissionEnd()+", status="+this.getStatus()+", redFiled1="+this.getRedFiled1()+", redFiled2="+this.getRedFiled2()+", redFiled3="+this.getRedFiled3()+", redFiled4="+this.getRedFiled4()+", redFiled5="+this.getRedFiled5()+", missionMessage="+this.getMissionMessage()+", downloadStatus="+this.getDownloadStatus()+", errorRules="+this.getErrorRules()+", dataSources="+this.getDataSources()+"\)";
    }
}
