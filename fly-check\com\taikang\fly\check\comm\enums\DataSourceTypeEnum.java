package com.taikang.fly.check.comm.enums.DataSourceTypeEnum;
import java.lang.Enum;
import java.lang.Boolean;
import java.lang.String;
import com.taikang.fly.check.comm.BizException;
import com.taikang.fly.check.comm.enums.ResponseCodeEnum;
import java.lang.Class;
import java.lang.Object;

public final class DataSourceTypeEnum extends Enum	// class@000082 from classes.dex
{
    private final String driverClassName;
    private final String name;
    private final Boolean needDynamic;
    private final String type;
    private final String urlPrefix;
    private static final DataSourceTypeEnum[] $VALUES;
    public static final DataSourceTypeEnum CLICKHOUSE;
    public static final DataSourceTypeEnum ORACLE;

    static {
       DataSourceTypeEnum.ORACLE = new DataSourceTypeEnum("ORACLE", 0, "Oracle", "oracle", "oracle.jdbc.driver.OracleDriver", "jdbc:oracle:thin:@", Boolean.valueOf(true));
       DataSourceTypeEnum.CLICKHOUSE = new DataSourceTypeEnum("CLICKHOUSE", true, "ClickHouse", "clickhouse", "ru.yandex.clickhouse.ClickHouseDriver", "jdbc:clickhouse://", Boolean.valueOf(true));
       DataSourceTypeEnum[] uDataSourceT = new DataSourceTypeEnum[]{DataSourceTypeEnum.ORACLE,DataSourceTypeEnum.CLICKHOUSE};
       DataSourceTypeEnum.$VALUES = uDataSourceT;
    }
    private void DataSourceTypeEnum(String p0,int p1,String name,String type,String driverClassName,String urlPrefix,Boolean needDynamic){
       super(p0, p1);
       this.name = name;
       this.type = type;
       this.driverClassName = driverClassName;
       this.urlPrefix = urlPrefix;
       this.needDynamic = needDynamic;
    }
    public static DataSourceTypeEnum getEnumByType(String type){
       object oobject;
       DataSourceTypeEnum[] uDataSourceT = DataSourceTypeEnum.values();
       int len = uDataSourceT.length;
       int i = 0;
       while (true) {
          if (i >= len) {
             throw new BizException(ResponseCodeEnum.DATABASE_TYPE_ERROR);
          }
          oobject = uDataSourceT[i];
          if (oobject.getType().equalsIgnoreCase(type)) {
             break ;
          }else {
             i = i + 1;
          }
       }
       return oobject;
    }
    public static DataSourceTypeEnum valueOf(String name){
       return Enum.valueOf(DataSourceTypeEnum.class, name);
    }
    public static DataSourceTypeEnum[] values(){
       return DataSourceTypeEnum.$VALUES.clone();
    }
    public String getDriverClassName(){
       return this.driverClassName;
    }
    public String getName(){
       return this.name;
    }
    public Boolean getNeedDynamic(){
       return this.needDynamic;
    }
    public String getType(){
       return this.type;
    }
    public String getUrlPrefix(){
       return this.urlPrefix;
    }
}
