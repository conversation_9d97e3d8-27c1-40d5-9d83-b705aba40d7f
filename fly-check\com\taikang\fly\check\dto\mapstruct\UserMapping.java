package com.taikang.fly.check.dto.mapstruct.UserMapping;
import com.taikang.fly.check.dto.user.UserAddDto;
import com.taikang.fly.check.mybatis.domain.User;
import com.taikang.fly.check.dto.user.UserEditDto;
import com.taikang.fly.check.dto.user.UserDto;
import java.util.List;

public interface abstract UserMapping	// class@00017d from classes.dex
{

    User userAddDtoToUser(UserAddDto p0);
    User userEditDtoToUser(UserEditDto p0,User p1);
    UserDto userToUserDto(User p0);
    List usersToUserDtos(List p0);
}
