package com.taikang.fly.check.rest.RoleController;
import java.lang.Object;
import com.taikang.fly.check.dto.system.role.RoleAddDto;
import com.taikang.fly.check.dto.RmpResponse;
import com.taikang.fly.check.service.RoleService;
import java.lang.String;
import com.taikang.fly.check.dto.system.role.RoleEditDto;
import java.lang.Integer;
import com.taikang.fly.check.dto.system.role.RoleSearchDto;
import com.taikang.fly.check.comm.Page;
import java.util.List;

public class RoleController	// class@0002ab from classes.dex
{
    private RoleService roleService;

    public void RoleController(){
       super();
    }
    public RmpResponse addRole(RoleAddDto roleAddDto){
       this.roleService.addRole(roleAddDto);
       return RmpResponse.success();
    }
    public RmpResponse deleteRole(String roleId){
       this.roleService.deleteByRoleId(roleId);
       return RmpResponse.success();
    }
    public RmpResponse editRole(RoleEditDto roleEditDto){
       this.roleService.editRole(roleEditDto);
       return RmpResponse.success();
    }
    public RmpResponse queryPage(Integer pageNum,Integer pageSize,RoleSearchDto roleSearchDto){
       Page roleIndexResDtoPage = this.roleService.queryPage(pageNum, pageSize, roleSearchDto);
       return RmpResponse.success(roleIndexResDtoPage);
    }
    public RmpResponse registerRoleList(){
       return RmpResponse.success(this.roleService.registerRoleList());
    }
}
