package com.taikang.fly.check.security.core.ThreadLocalContextHolder;
import java.lang.ThreadLocal;
import java.lang.InheritableThreadLocal;
import java.lang.Object;
import com.taikang.fly.check.security.core.DomainContext;
import java.lang.String;
import org.springframework.util.Assert;

public final class ThreadLocalContextHolder	// class@0002c4 from classes.dex
{
    private static final ThreadLocal CONTEXT_HOLDER;
    private static final InheritableThreadLocal INHERITABLE_THREAD_LOCAL;

    static {
       ThreadLocalContextHolder.CONTEXT_HOLDER = new ThreadLocal();
       ThreadLocalContextHolder.INHERITABLE_THREAD_LOCAL = new InheritableThreadLocal();
    }
    private void ThreadLocalContextHolder(){
       super();
    }
    public static void clearContext(){
       ThreadLocalContextHolder.CONTEXT_HOLDER.remove();
    }
    public static DomainContext createEmptyContext(){
       return new DomainContext();
    }
    public static DomainContext getContext(){
       DomainContext uDomainConte;
       if ((uDomainConte = ThreadLocalContextHolder.CONTEXT_HOLDER.get()) == null) {
          uDomainConte = ThreadLocalContextHolder.createEmptyContext();
          ThreadLocalContextHolder.CONTEXT_HOLDER.set(ctx);
       }
       return ctx;
    }
    public static DomainContext getContextThread(){
       DomainContext uDomainConte;
       if ((uDomainConte = ThreadLocalContextHolder.INHERITABLE_THREAD_LOCAL.get()) == null) {
          uDomainConte = ThreadLocalContextHolder.createEmptyContext();
          ThreadLocalContextHolder.INHERITABLE_THREAD_LOCAL.set(ctx);
       }
       return ctx;
    }
    public static void setContext(DomainContext context){
       Assert.notNull(context, "Only non-null SecurityContext instances are permitted");
       ThreadLocalContextHolder.CONTEXT_HOLDER.set(context);
       ThreadLocalContextHolder.INHERITABLE_THREAD_LOCAL.set(context);
    }
}
