package com.taikang.fly.check.dto.plan.PlanRespDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class PlanRespDto implements Serializable	// class@0001a2 from classes.dex
{
    private String createTime;
    private String creator;
    private String id;
    private String missionStatus;
    private String modby;
    private String modifyTime;
    private String planName;
    private String redFiled1;
    private String redFiled2;
    private String redFiled3;
    private String redFiled4;
    private String redFiled5;
    private String rules;
    private String status;
    private static final long serialVersionUID = 0x1;

    public void PlanRespDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof PlanRespDto;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof PlanRespDto){
          b = false;
       }else {
          PlanRespDto planRespDto = o;
          if (!planRespDto.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = planRespDto.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String planName = this.getPlanName();
             String planName1 = planRespDto.getPlanName();
             if (planName == null) {
                if (planName1 != null) {
                   b = false;
                }
             }else if(planName.equals(planName1)){
             }
             String status = this.getStatus();
             String status1 = planRespDto.getStatus();
             if (status == null) {
                if (status1 != null) {
                   b = false;
                }
             }else if(status.equals(status1)){
             }
             String creator = this.getCreator();
             String creator1 = planRespDto.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             String rules = this.getRules();
             String rules1 = planRespDto.getRules();
             if (rules == null) {
                if (rules1 != null) {
                   b = false;
                }
             }else if(rules.equals(rules1)){
             }
             String createTime = this.getCreateTime();
             String createTime1 = planRespDto.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                label_00a5 :
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modby = this.getModby();
             String modby1 = planRespDto.getModby();
             if (modby == null) {
                if (modby1 != null) {
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             String modifyTime = this.getModifyTime();
             String modifyTime1 = planRespDto.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                label_00d5 :
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String redFiled1 = this.getRedFiled1();
             String redFiled11 = planRespDto.getRedFiled1();
             if (redFiled1 == null) {
                if (redFiled11 != null) {
                   b = false;
                }
             }else if(redFiled1.equals(redFiled11)){
             }
             String redFiled2 = this.getRedFiled2();
             String redFiled21 = planRespDto.getRedFiled2();
             if (redFiled2 == null) {
                if (redFiled21 != null) {
                label_0105 :
                   b = false;
                }
             }else if(redFiled2.equals(redFiled21)){
             }
             String redFiled3 = this.getRedFiled3();
             String redFiled31 = planRespDto.getRedFiled3();
             if (redFiled3 == null) {
                if (redFiled31 != null) {
                   b = false;
                }
             }else if(redFiled3.equals(redFiled31)){
             }
             String redFiled4 = this.getRedFiled4();
             String redFiled41 = planRespDto.getRedFiled4();
             if (redFiled4 == null) {
                if (redFiled41 != null) {
                label_0135 :
                   b = false;
                }
             }else if(redFiled4.equals(redFiled41)){
             }
             String redFiled5 = this.getRedFiled5();
             String redFiled51 = planRespDto.getRedFiled5();
             if (redFiled5 == null) {
                if (redFiled51 != null) {
                   b = false;
                }
             }else if(redFiled5.equals(redFiled51)){
             }
             String missionStatu = this.getMissionStatus();
             String missionStatu1 = planRespDto.getMissionStatus();
             if (missionStatu == null) {
                if (missionStatu1 != null) {
                   b = false;
                }
             }else if(missionStatu.equals(missionStatu1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getId(){
       return this.id;
    }
    public String getMissionStatus(){
       return this.missionStatus;
    }
    public String getModby(){
       return this.modby;
    }
    public String getModifyTime(){
       return this.modifyTime;
    }
    public String getPlanName(){
       return this.planName;
    }
    public String getRedFiled1(){
       return this.redFiled1;
    }
    public String getRedFiled2(){
       return this.redFiled2;
    }
    public String getRedFiled3(){
       return this.redFiled3;
    }
    public String getRedFiled4(){
       return this.redFiled4;
    }
    public String getRedFiled5(){
       return this.redFiled5;
    }
    public String getRules(){
       return this.rules;
    }
    public String getStatus(){
       return this.status;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $planName = this.getPlanName();
       int i1 = result * 59;
       i = ($planName == null)? 43: $planName.hashCode();
       result = i1 + i;
       String $status = this.getStatus();
       i1 = result * 59;
       i = ($status == null)? 43: $status.hashCode();
       result = i1 + i;
       String $creator = this.getCreator();
       i1 = result * 59;
       i = ($creator == null)? 43: $creator.hashCode();
       result = i1 + i;
       String $rules = this.getRules();
       i1 = result * 59;
       i = ($rules == null)? 43: $rules.hashCode();
       result = i1 + i;
       String createTime = this.getCreateTime();
       i1 = result * 59;
       i = (createTime == null)? 43: createTime.hashCode();
       String modby = this.getModby();
       i1 = (i1 + i) * 59;
       i = (modby == null)? 43: modby.hashCode();
       String modifyTime = this.getModifyTime();
       i1 = (i1 + i) * 59;
       i = (modifyTime == null)? 43: modifyTime.hashCode();
       String redFiled1 = this.getRedFiled1();
       i1 = (i1 + i) * 59;
       i = (redFiled1 == null)? 43: redFiled1.hashCode();
       String redFiled2 = this.getRedFiled2();
       i1 = (i1 + i) * 59;
       i = (redFiled2 == null)? 43: redFiled2.hashCode();
       String redFiled3 = this.getRedFiled3();
       i1 = (i1 + i) * 59;
       i = (redFiled3 == null)? 43: redFiled3.hashCode();
       String redFiled4 = this.getRedFiled4();
       i1 = (i1 + i) * 59;
       i = (redFiled4 == null)? 43: redFiled4.hashCode();
       String redFiled5 = this.getRedFiled5();
       i1 = (i1 + i) * 59;
       i = (redFiled5 == null)? 43: redFiled5.hashCode();
       String missionStatu = this.getMissionStatus();
       i1 = (i1 + i) * 59;
       i = (missionStatu == null)? 43: missionStatu.hashCode();
       return (i1 + i);
    }
    public void setCreateTime(String createTime){
       this.createTime = createTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setMissionStatus(String missionStatus){
       this.missionStatus = missionStatus;
    }
    public void setModby(String modby){
       this.modby = modby;
    }
    public void setModifyTime(String modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setPlanName(String planName){
       this.planName = planName;
    }
    public void setRedFiled1(String redFiled1){
       this.redFiled1 = redFiled1;
    }
    public void setRedFiled2(String redFiled2){
       this.redFiled2 = redFiled2;
    }
    public void setRedFiled3(String redFiled3){
       this.redFiled3 = redFiled3;
    }
    public void setRedFiled4(String redFiled4){
       this.redFiled4 = redFiled4;
    }
    public void setRedFiled5(String redFiled5){
       this.redFiled5 = redFiled5;
    }
    public void setRules(String rules){
       this.rules = rules;
    }
    public void setStatus(String status){
       this.status = status;
    }
    public String toString(){
       return "PlanRespDto\(id="+this.getId()+", planName="+this.getPlanName()+", status="+this.getStatus()+", creator="+this.getCreator()+", rules="+this.getRules()+", createTime="+this.getCreateTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+", redFiled1="+this.getRedFiled1()+", redFiled2="+this.getRedFiled2()+", redFiled3="+this.getRedFiled3()+", redFiled4="+this.getRedFiled4()+", redFiled5="+this.getRedFiled5()+", missionStatus="+this.getMissionStatus()+"\)";
    }
}
