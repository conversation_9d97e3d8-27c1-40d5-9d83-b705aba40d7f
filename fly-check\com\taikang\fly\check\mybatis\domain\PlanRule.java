package com.taikang.fly.check.mybatis.domain.PlanRule;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.time.LocalDateTime;
import java.lang.StringBuilder;

public class PlanRule implements Serializable	// class@000265 from classes.dex
{
    private LocalDateTime createTime;
    private String id;
    private String medicalCategory;
    private String planId;
    private String ps;
    private String ruleId;
    private String ruleIntension;
    private String ruleName;
    private String ruleScopeApply;
    private String ruleType;
    private String sourceOfRule;
    private String status;
    private static final long serialVersionUID = 0x1;

    public void PlanRule(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof PlanRule;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof PlanRule){
          b = false;
       }else {
          PlanRule planRule = o;
          if (!planRule.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = planRule.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String ruleId = this.getRuleId();
             String ruleId1 = planRule.getRuleId();
             if (ruleId == null) {
                if (ruleId1 != null) {
                   b = false;
                }
             }else if(ruleId.equals(ruleId1)){
             }
             String planId = this.getPlanId();
             String planId1 = planRule.getPlanId();
             if (planId == null) {
                if (planId1 != null) {
                   b = false;
                }
             }else if(planId.equals(planId1)){
             }
             String ruleName = this.getRuleName();
             String ruleName1 = planRule.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String sourceOfRule = this.getSourceOfRule();
             String sourceOfRule1 = planRule.getSourceOfRule();
             if (sourceOfRule == null) {
                if (sourceOfRule1 != null) {
                   b = false;
                }
             }else if(sourceOfRule.equals(sourceOfRule1)){
             }
             String ruleIntensio = this.getRuleIntension();
             String ruleIntensio1 = planRule.getRuleIntension();
             if (ruleIntensio == null) {
                if (ruleIntensio1 != null) {
                label_00a3 :
                   b = false;
                }
             }else if(ruleIntensio.equals(ruleIntensio1)){
             }
             String ps = this.getPs();
             String ps1 = planRule.getPs();
             if (ps == null) {
                if (ps1 != null) {
                   b = false;
                }
             }else if(ps.equals(ps1)){
             }
             String ruleType = this.getRuleType();
             String ruleType1 = planRule.getRuleType();
             if (ruleType == null) {
                if (ruleType1 != null) {
                label_00d3 :
                   b = false;
                }
             }else if(ruleType.equals(ruleType1)){
             }
             String medicalCateg = this.getMedicalCategory();
             String medicalCateg1 = planRule.getMedicalCategory();
             if (medicalCateg == null) {
                if (medicalCateg1 != null) {
                   b = false;
                }
             }else if(medicalCateg.equals(medicalCateg1)){
             }
             LocalDateTime createTime = this.getCreateTime();
             LocalDateTime createTime1 = planRule.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String status = this.getStatus();
             String status1 = planRule.getStatus();
             if (status == null) {
                if (status1 != null) {
                label_0119 :
                   b = false;
                }
             }else if(status.equals(status1)){
             }
             String ruleScopeApp = this.getRuleScopeApply();
             String ruleScopeApp1 = planRule.getRuleScopeApply();
             if (ruleScopeApp == null) {
                if (ruleScopeApp1 != null) {
                   b = false;
                }
             }else if(ruleScopeApp.equals(ruleScopeApp1)){
             }
             b = true;
          }
       }
       return b;
    }
    public LocalDateTime getCreateTime(){
       return this.createTime;
    }
    public String getId(){
       return this.id;
    }
    public String getMedicalCategory(){
       return this.medicalCategory;
    }
    public String getPlanId(){
       return this.planId;
    }
    public String getPs(){
       return this.ps;
    }
    public String getRuleId(){
       return this.ruleId;
    }
    public String getRuleIntension(){
       return this.ruleIntension;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleScopeApply(){
       return this.ruleScopeApply;
    }
    public String getRuleType(){
       return this.ruleType;
    }
    public String getSourceOfRule(){
       return this.sourceOfRule;
    }
    public String getStatus(){
       return this.status;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $ruleId = this.getRuleId();
       int i1 = result * 59;
       i = ($ruleId == null)? 43: $ruleId.hashCode();
       result = i1 + i;
       String $planId = this.getPlanId();
       i1 = result * 59;
       i = ($planId == null)? 43: $planId.hashCode();
       result = i1 + i;
       String $ruleName = this.getRuleName();
       i1 = result * 59;
       i = ($ruleName == null)? 43: $ruleName.hashCode();
       result = i1 + i;
       String $sourceOfRule = this.getSourceOfRule();
       i1 = result * 59;
       i = ($sourceOfRule == null)? 43: $sourceOfRule.hashCode();
       result = i1 + i;
       String ruleIntensio = this.getRuleIntension();
       i1 = result * 59;
       i = (ruleIntensio == null)? 43: ruleIntensio.hashCode();
       String ps = this.getPs();
       i1 = (i1 + i) * 59;
       i = (ps == null)? 43: ps.hashCode();
       String ruleType = this.getRuleType();
       i1 = (i1 + i) * 59;
       i = (ruleType == null)? 43: ruleType.hashCode();
       String medicalCateg = this.getMedicalCategory();
       i1 = (i1 + i) * 59;
       i = (medicalCateg == null)? 43: medicalCateg.hashCode();
       LocalDateTime createTime = this.getCreateTime();
       i1 = (i1 + i) * 59;
       i = (createTime == null)? 43: createTime.hashCode();
       String status = this.getStatus();
       i1 = (i1 + i) * 59;
       i = (status == null)? 43: status.hashCode();
       String ruleScopeApp = this.getRuleScopeApply();
       i1 = (i1 + i) * 59;
       i = (ruleScopeApp == null)? 43: ruleScopeApp.hashCode();
       return (i1 + i);
    }
    public void setCreateTime(LocalDateTime createTime){
       this.createTime = createTime;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setMedicalCategory(String medicalCategory){
       this.medicalCategory = medicalCategory;
    }
    public void setPlanId(String planId){
       this.planId = planId;
    }
    public void setPs(String ps){
       this.ps = ps;
    }
    public void setRuleId(String ruleId){
       this.ruleId = ruleId;
    }
    public void setRuleIntension(String ruleIntension){
       this.ruleIntension = ruleIntension;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleScopeApply(String ruleScopeApply){
       this.ruleScopeApply = ruleScopeApply;
    }
    public void setRuleType(String ruleType){
       this.ruleType = ruleType;
    }
    public void setSourceOfRule(String sourceOfRule){
       this.sourceOfRule = sourceOfRule;
    }
    public void setStatus(String status){
       this.status = status;
    }
    public String toString(){
       return "PlanRule\(id="+this.getId()+", ruleId="+this.getRuleId()+", planId="+this.getPlanId()+", ruleName="+this.getRuleName()+", sourceOfRule="+this.getSourceOfRule()+", ruleIntension="+this.getRuleIntension()+", ps="+this.getPs()+", ruleType="+this.getRuleType()+", medicalCategory="+this.getMedicalCategory()+", createTime="+this.getCreateTime()+", status="+this.getStatus()+", ruleScopeApply="+this.getRuleScopeApply()+"\)";
    }
}
