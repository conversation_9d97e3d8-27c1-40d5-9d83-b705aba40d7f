package org.springframework.boot.loader.data.RandomAccessDataFile;
import org.springframework.boot.loader.data.RandomAccessData;
import java.io.File;
import java.lang.Object;
import java.lang.IllegalArgumentException;
import java.lang.String;
import org.springframework.boot.loader.data.RandomAccessDataFile$FileAccess;
import org.springframework.boot.loader.data.RandomAccessDataFile$1;
import java.io.InputStream;
import org.springframework.boot.loader.data.RandomAccessDataFile$DataInputStream;
import java.lang.IndexOutOfBoundsException;
import java.io.EOFException;

public class RandomAccessDataFile implements RandomAccessData	// class@000548 from classes.dex
{
    private final RandomAccessDataFile$FileAccess fileAccess;
    private final long length;
    private final long offset;

    public void RandomAccessDataFile(File file){
       super();
       if (file == null) {
          throw new IllegalArgumentException("File must not be null");
       }
       this.fileAccess = new RandomAccessDataFile$FileAccess(file, null);
       this.offset = 0;
       this.length = file.length();
       return;
    }
    private void RandomAccessDataFile(RandomAccessDataFile$FileAccess fileAccess,long offset,long length){
       super();
       this.fileAccess = fileAccess;
       this.offset = offset;
       this.length = length;
    }
    static int access$600(RandomAccessDataFile x0,long x1){
       return x0.readByte(x1);
    }
    static int access$700(RandomAccessDataFile x0,byte[] x1,long x2,int x3,int x4){
       return x0.read(x1, x2, x3, x4);
    }
    static long access$800(RandomAccessDataFile x0){
       return x0.length;
    }
    private int read(byte[] bytes,long position,int offset,int length){
       int i = ((position - this.length) > 0)? -1: RandomAccessDataFile$FileAccess.access$400(this.fileAccess, bytes, (this.offset + position), offset, length);
       return i;
    }
    private int readByte(long position){
       int i = ((position - this.length) >= 0)? -1: RandomAccessDataFile$FileAccess.access$300(this.fileAccess, (this.offset + position));
       return i;
    }
    public void close(){
       RandomAccessDataFile$FileAccess.access$500(this.fileAccess);
    }
    public File getFile(){
       return RandomAccessDataFile$FileAccess.access$100(this.fileAccess);
    }
    public InputStream getInputStream(){
       return new RandomAccessDataFile$DataInputStream(this, null);
    }
    public long getSize(){
       return this.length;
    }
    public RandomAccessData getSubsection(long offset,long length){
       int i = 0;
       if ((offset - i) >= 0 && ((length - i) >= 0 && ((offset + length) - this.length) <= 0)) {
          return new RandomAccessDataFile(this.fileAccess, (this.offset + offset), length);
       }
       throw new IndexOutOfBoundsException();
    }
    public byte[] read(){
       return this.read(0, this.length);
    }
    public byte[] read(long offset,long length){
       if ((offset - this.length) > 0) {
          throw new IndexOutOfBoundsException();
       }
       if (((offset + length) - this.length) > 0) {
          throw new EOFException();
       }
       byte[] uobyteArray = new byte[(int)length];
       this.read(uobyteArray, offset, 0, uobyteArray.length);
       return uobyteArray;
    }
}
