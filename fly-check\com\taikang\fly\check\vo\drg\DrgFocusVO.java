package com.taikang.fly.check.vo.drg.DrgFocusVO;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import java.lang.StringBuilder;

public class DrgFocusVO	// class@000368 from classes.dex
{
    private String focus;
    private Integer setlIdCount;
    private Integer suspiciousRuleCount;
    private Integer violationRuleCount;

    public void DrgFocusVO(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgFocusVO;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof DrgFocusVO) {
             b = false;
          }else {
             DrgFocusVO uDrgFocusVO = o;
             if (!uDrgFocusVO.canEqual(this)) {
                b = false;
             }else {
                String focus = this.getFocus();
                String focus1 = uDrgFocusVO.getFocus();
                if (focus == null) {
                   if (focus1 != null) {
                      b = false;
                   }
                }else if(focus.equals(focus1)){
                }
                Integer violationRul = this.getViolationRuleCount();
                Integer violationRul1 = uDrgFocusVO.getViolationRuleCount();
                if (violationRul == null) {
                   if (violationRul1 != null) {
                      b = false;
                   }
                }else if(violationRul.equals(violationRul1)){
                }
                Integer suspiciousRu = this.getSuspiciousRuleCount();
                Integer suspiciousRu1 = uDrgFocusVO.getSuspiciousRuleCount();
                if (suspiciousRu == null) {
                   if (suspiciousRu1 != null) {
                      b = false;
                   }
                }else if(suspiciousRu.equals(suspiciousRu1)){
                }
                Integer setlIdCount = this.getSetlIdCount();
                Integer setlIdCount1 = uDrgFocusVO.getSetlIdCount();
                if (setlIdCount == null) {
                   if (setlIdCount1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!setlIdCount.equals(setlIdCount1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getFocus(){
       return this.focus;
    }
    public Integer getSetlIdCount(){
       return this.setlIdCount;
    }
    public Integer getSuspiciousRuleCount(){
       return this.suspiciousRuleCount;
    }
    public Integer getViolationRuleCount(){
       return this.violationRuleCount;
    }
    public int hashCode(){
       String $focus;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($focus = this.getFocus()) == null)? i: $focus.hashCode();
       result = i1 + 59;
       Integer $violationRuleCount = this.getViolationRuleCount();
       int i2 = result * 59;
       i1 = ($violationRuleCount == null)? i: $violationRuleCount.hashCode();
       result = i2 + i1;
       Integer $suspiciousRuleCount = this.getSuspiciousRuleCount();
       i2 = result * 59;
       i1 = ($suspiciousRuleCount == null)? i: $suspiciousRuleCount.hashCode();
       result = i2 + i1;
       Integer $setlIdCount = this.getSetlIdCount();
       i1 = result * 59;
       if ($setlIdCount != null) {
          i = $setlIdCount.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setFocus(String focus){
       this.focus = focus;
    }
    public void setSetlIdCount(Integer setlIdCount){
       this.setlIdCount = setlIdCount;
    }
    public void setSuspiciousRuleCount(Integer suspiciousRuleCount){
       this.suspiciousRuleCount = suspiciousRuleCount;
    }
    public void setViolationRuleCount(Integer violationRuleCount){
       this.violationRuleCount = violationRuleCount;
    }
    public String toString(){
       return "DrgFocusVO\(focus="+this.getFocus()+", violationRuleCount="+this.getViolationRuleCount()+", suspiciousRuleCount="+this.getSuspiciousRuleCount()+", setlIdCount="+this.getSetlIdCount()+"\)";
    }
}
