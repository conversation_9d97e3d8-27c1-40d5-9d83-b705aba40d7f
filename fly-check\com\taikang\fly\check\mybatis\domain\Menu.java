package com.taikang.fly.check.mybatis.domain.Menu;
import java.lang.Object;
import java.lang.String;
import java.util.Date;
import java.lang.StringBuilder;

public class Menu	// class@00024f from classes.dex
{
    private String aclass;
    private Date createTime;
    private String creator;
    private String iclass;
    private String icon;
    private String isLeaf;
    private String isMenu;
    private String isValid;
    private String menuDesc;
    private String menuId;
    private String menuName;
    private String menuOrder;
    private String modby;
    private Date modifyTime;
    private String moduleCode;
    private String parentId;
    private String signature;
    private String url;

    public void Menu(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof Menu;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof Menu){
          b = false;
       }else {
          Menu menu = o;
          if (!menu.canEqual(this)) {
             b = false;
          }else {
             String menuId = this.getMenuId();
             String menuId1 = menu.getMenuId();
             if (menuId == null) {
                if (menuId1 != null) {
                   b = false;
                }
             }else if(menuId.equals(menuId1)){
             }
             String moduleCode = this.getModuleCode();
             String moduleCode1 = menu.getModuleCode();
             if (moduleCode == null) {
                if (moduleCode1 != null) {
                   b = false;
                }
             }else if(moduleCode.equals(moduleCode1)){
             }
             String parentId = this.getParentId();
             String parentId1 = menu.getParentId();
             if (parentId == null) {
                if (parentId1 != null) {
                   b = false;
                }
             }else if(parentId.equals(parentId1)){
             }
             String menuName = this.getMenuName();
             String menuName1 = menu.getMenuName();
             if (menuName == null) {
                if (menuName1 != null) {
                   b = false;
                }
             }else if(menuName.equals(menuName1)){
             }
             String url = this.getUrl();
             String url1 = menu.getUrl();
             if (url == null) {
                if (url1 != null) {
                label_008f :
                   b = false;
                }
             }else if(url.equals(url1)){
             }
             String menuOrder = this.getMenuOrder();
             String menuOrder1 = menu.getMenuOrder();
             if (menuOrder == null) {
                if (menuOrder1 != null) {
                   b = false;
                }
             }else if(menuOrder.equals(menuOrder1)){
             }
             String icon = this.getIcon();
             String icon1 = menu.getIcon();
             if (icon == null) {
                if (icon1 != null) {
                   b = false;
                }
             }else if(icon.equals(icon1)){
             }
             String creator = this.getCreator();
             String creator1 = menu.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                label_00d9 :
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             Date createTime = this.getCreateTime();
             Date createTime1 = menu.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                label_00f1 :
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modby = this.getModby();
             String modby1 = menu.getModby();
             if (modby == null) {
                if (modby1 != null) {
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             Date modifyTime = this.getModifyTime();
             Date modifyTime1 = menu.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                label_0121 :
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String signature = this.getSignature();
             String signature1 = menu.getSignature();
             if (signature == null) {
                if (signature1 != null) {
                   b = false;
                }
             }else if(signature.equals(signature1)){
             }
             String menuDesc = this.getMenuDesc();
             String menuDesc1 = menu.getMenuDesc();
             if (menuDesc == null) {
                if (menuDesc1 != null) {
                label_0155 :
                   b = false;
                }
             }else if(menuDesc.equals(menuDesc1)){
             }
             String iclass = this.getIclass();
             String iclass1 = menu.getIclass();
             if (iclass == null) {
                if (iclass1 != null) {
                   b = false;
                }
             }else if(iclass.equals(iclass1)){
             }
             String isMenu = this.getIsMenu();
             String isMenu1 = menu.getIsMenu();
             if (isMenu == null) {
                if (isMenu1 != null) {
                   b = false;
                }
             }else if(isMenu.equals(isMenu1)){
             }
             String isLeaf = this.getIsLeaf();
             String isLeaf1 = menu.getIsLeaf();
             if (isLeaf == null) {
                if (isLeaf1 != null) {
                label_019d :
                   b = false;
                }
             }else if(isLeaf.equals(isLeaf1)){
             }
             String aclass = this.getAclass();
             String aclass1 = menu.getAclass();
             if (aclass == null) {
                if (aclass1 != null) {
                   b = false;
                }
             }else if(aclass.equals(aclass1)){
             }
             String isValid = this.getIsValid();
             String isValid1 = menu.getIsValid();
             if (isValid == null) {
                if (isValid1 != null) {
                label_01cd :
                   b = false;
                }
             }else if(isValid.equals(isValid1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getAclass(){
       return this.aclass;
    }
    public Date getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getIclass(){
       return this.iclass;
    }
    public String getIcon(){
       return this.icon;
    }
    public String getIsLeaf(){
       return this.isLeaf;
    }
    public String getIsMenu(){
       return this.isMenu;
    }
    public String getIsValid(){
       return this.isValid;
    }
    public String getMenuDesc(){
       return this.menuDesc;
    }
    public String getMenuId(){
       return this.menuId;
    }
    public String getMenuName(){
       return this.menuName;
    }
    public String getMenuOrder(){
       return this.menuOrder;
    }
    public String getModby(){
       return this.modby;
    }
    public Date getModifyTime(){
       return this.modifyTime;
    }
    public String getModuleCode(){
       return this.moduleCode;
    }
    public String getParentId(){
       return this.parentId;
    }
    public String getSignature(){
       return this.signature;
    }
    public String getUrl(){
       return this.url;
    }
    public int hashCode(){
       String $menuId;
       int PRIME = 59;
       int result = 1;
       int i = (($menuId = this.getMenuId()) == null)? 43: $menuId.hashCode();
       result = i + 59;
       String $moduleCode = this.getModuleCode();
       int i1 = result * 59;
       i = ($moduleCode == null)? 43: $moduleCode.hashCode();
       result = i1 + i;
       String $parentId = this.getParentId();
       i1 = result * 59;
       i = ($parentId == null)? 43: $parentId.hashCode();
       result = i1 + i;
       String $menuName = this.getMenuName();
       i1 = result * 59;
       i = ($menuName == null)? 43: $menuName.hashCode();
       result = i1 + i;
       String $url = this.getUrl();
       i1 = result * 59;
       i = ($url == null)? 43: $url.hashCode();
       result = i1 + i;
       String menuOrder = this.getMenuOrder();
       i1 = result * 59;
       i = (menuOrder == null)? 43: menuOrder.hashCode();
       String icon = this.getIcon();
       i1 = (i1 + i) * 59;
       i = (icon == null)? 43: icon.hashCode();
       String creator = this.getCreator();
       i1 = (i1 + i) * 59;
       i = (creator == null)? 43: creator.hashCode();
       Date createTime = this.getCreateTime();
       i1 = (i1 + i) * 59;
       i = (createTime == null)? 43: createTime.hashCode();
       String modby = this.getModby();
       i1 = (i1 + i) * 59;
       i = (modby == null)? 43: modby.hashCode();
       Date modifyTime = this.getModifyTime();
       i1 = (i1 + i) * 59;
       i = (modifyTime == null)? 43: modifyTime.hashCode();
       String signature = this.getSignature();
       i1 = (i1 + i) * 59;
       i = (signature == null)? 43: signature.hashCode();
       String menuDesc = this.getMenuDesc();
       i1 = (i1 + i) * 59;
       i = (menuDesc == null)? 43: menuDesc.hashCode();
       String iclass = this.getIclass();
       i1 = (i1 + i) * 59;
       i = (iclass == null)? 43: iclass.hashCode();
       String isMenu = this.getIsMenu();
       i1 = (i1 + i) * 59;
       i = (isMenu == null)? 43: isMenu.hashCode();
       String isLeaf = this.getIsLeaf();
       i1 = (i1 + i) * 59;
       i = (isLeaf == null)? 43: isLeaf.hashCode();
       String aclass = this.getAclass();
       i1 = (i1 + i) * 59;
       i = (aclass == null)? 43: aclass.hashCode();
       String isValid = this.getIsValid();
       i1 = (i1 + i) * 59;
       i = (isValid == null)? 43: isValid.hashCode();
       return (i1 + i);
    }
    public void setAclass(String aclass){
       this.aclass = aclass;
    }
    public void setCreateTime(Date createTime){
       this.createTime = createTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setIclass(String iclass){
       this.iclass = iclass;
    }
    public void setIcon(String icon){
       this.icon = icon;
    }
    public void setIsLeaf(String isLeaf){
       this.isLeaf = isLeaf;
    }
    public void setIsMenu(String isMenu){
       this.isMenu = isMenu;
    }
    public void setIsValid(String isValid){
       this.isValid = isValid;
    }
    public void setMenuDesc(String menuDesc){
       this.menuDesc = menuDesc;
    }
    public void setMenuId(String menuId){
       this.menuId = menuId;
    }
    public void setMenuName(String menuName){
       this.menuName = menuName;
    }
    public void setMenuOrder(String menuOrder){
       this.menuOrder = menuOrder;
    }
    public void setModby(String modby){
       this.modby = modby;
    }
    public void setModifyTime(Date modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setModuleCode(String moduleCode){
       this.moduleCode = moduleCode;
    }
    public void setParentId(String parentId){
       this.parentId = parentId;
    }
    public void setSignature(String signature){
       this.signature = signature;
    }
    public void setUrl(String url){
       this.url = url;
    }
    public String toString(){
       return "Menu\(menuId="+this.getMenuId()+", moduleCode="+this.getModuleCode()+", parentId="+this.getParentId()+", menuName="+this.getMenuName()+", url="+this.getUrl()+", menuOrder="+this.getMenuOrder()+", icon="+this.getIcon()+", creator="+this.getCreator()+", createTime="+this.getCreateTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+", signature="+this.getSignature()+", menuDesc="+this.getMenuDesc()+", iclass="+this.getIclass()+", isMenu="+this.getIsMenu()+", isLeaf="+this.getIsLeaf()+", aclass="+this.getAclass()+", isValid="+this.getIsValid()+"\)";
    }
}
