package com.taikang.fly.check.utils.CharsetUtils;
import java.lang.Object;
import java.io.File;
import java.lang.String;
import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.InputStream;
import java.lang.Throwable;

public class CharsetUtils	// class@00032e from classes.dex
{

    public void CharsetUtils(){
       super();
    }
    public String getCharset(File file){
       int read;
       String str;
       String charset = "GBK";
       byte[] first3Bytes = new byte[3];
       boolean checked = false;
       BufferedInputStream bis = new BufferedInputStream(new FileInputStream(file));
       try{
          int i = 0;
          bis.mark(i);
          if ((read = bis.read(first3Bytes, 0, 3)) == -1) {
             try{
                if (bis != null) {
                   if (0) {
                      bis.close();
                   }else {
                      bis.close();
                   }
                }
             }catch(java.lang.Throwable e8){
                0.addSuppressed(e8);
             }
             str = charset;
          }else if(first3Bytes[0] == -1 && first3Bytes[1] == -2){
             charset = "UTF-16LE";
             checked = 1;
          }else {
             i = 0;
             if (first3Bytes[i] == -2 && first3Bytes[1] == -1) {
                charset = "UTF-16BE";
                checked = 1;
             }else if(first3Bytes[0] == -17 && (first3Bytes[1] == -69 && first3Bytes[2] == -65)){
                charset = "UTF-8";
                checked = 1;
             }
          }
          bis.reset();
          if (!checked) {
             int i1 = 0;
             while ((read = bis.read()) != -1) {
                i1 = i1 + 1;
                if (read < 240 && (128 > read || read > 191)) {
                   if (192 <= read && read <= 223) {
                      read = bis.read();
                      if (128 <= read && read <= 191) {
                      }
                   }else if(224 <= read && read <= 239){
                      read = bis.read();
                      if (128 <= read && read <= 191) {
                         read = bis.read();
                         if (128 <= read && read <= 191) {
                            charset = "UTF-8";
                            break ;
                         }
                      }
                   }
                }
             }
          }
          try{
             if (bis != null) {
                if (0) {
                   bis.close();
                }else {
                   bis.close();
                }
             }
          }catch(java.lang.Throwable e8){
             0.addSuppressed(e8);
          }
          str = charset;
       }catch(java.lang.Throwable e8){
          throw e8;
       }
          return str;
    }
}
