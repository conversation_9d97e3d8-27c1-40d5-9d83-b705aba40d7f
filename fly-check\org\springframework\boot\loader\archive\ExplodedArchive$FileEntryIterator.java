package org.springframework.boot.loader.archive.ExplodedArchive$FileEntryIterator;
import java.util.Iterator;
import java.io.File;
import java.lang.Object;
import org.springframework.boot.loader.archive.ExplodedArchive$FileEntryIterator$EntryComparator;
import org.springframework.boot.loader.archive.ExplodedArchive$1;
import java.util.LinkedList;
import java.util.Deque;
import java.util.List;
import java.util.Collections;
import java.util.Comparator;
import java.util.Arrays;
import java.util.Set;
import org.springframework.boot.loader.archive.ExplodedArchive;
import java.lang.String;
import org.springframework.boot.loader.archive.Archive$Entry;
import java.util.NoSuchElementException;
import java.net.URI;
import org.springframework.boot.loader.archive.ExplodedArchive$FileEntry;
import java.lang.UnsupportedOperationException;

class ExplodedArchive$FileEntryIterator implements Iterator	// class@00053f from classes.dex
{
    private File current;
    private final Comparator entryComparator;
    private final boolean recursive;
    private final File root;
    private final Deque stack;

    void ExplodedArchive$FileEntryIterator(File root,boolean recursive){
       super();
       this.entryComparator = new ExplodedArchive$FileEntryIterator$EntryComparator(null);
       this.stack = new LinkedList();
       this.root = root;
       this.recursive = recursive;
       this.stack.add(this.listFiles(root));
       this.current = this.poll();
    }
    private Iterator listFiles(File file){
       File[] files;
       Iterator iterator;
       if ((files = file.listFiles()) == null) {
          iterator = Collections.emptyList().iterator();
       }else {
          Arrays.sort(files, this.entryComparator);
          iterator = Arrays.asList(files).iterator();
       }
       return iterator;
    }
    private File poll(){
       File uFile;
       while (true) {
          if (!this.stack.isEmpty()) {
             do {
                if (this.stack.peek().hasNext()) {
                }else {
                   this.stack.poll();
                }
                uFile = this.stack.peek().next();
             } while (!ExplodedArchive.access$100().contains(uFile.getName()));
             return uFile;
          }else {
             uFile = null;
          }
       }
    }
    public boolean hasNext(){
       boolean b = (this.current != null)? true: false;
       return b;
    }
    public Object next(){
       return this.next();
    }
    public Archive$Entry next(){
       if (this.current == null) {
          throw new NoSuchElementException();
       }
       ExplodedArchive$FileEntryIterator tcurrent = this.current;
       if (tcurrent.isDirectory() && (this.recursive != null && !tcurrent.getParentFile().equals(this.root))) {
          this.stack.addFirst(this.listFiles(tcurrent));
       }
       this.current = this.poll();
       return new ExplodedArchive$FileEntry(tcurrent.toURI().getPath().substring(this.root.toURI().getPath().length()), tcurrent);
    }
    public void remove(){
       throw new UnsupportedOperationException("remove");
    }
}
