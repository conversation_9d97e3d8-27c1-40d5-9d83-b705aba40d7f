package com.taikang.fly.check.dto.SplitTableDto;
import java.io.Serializable;
import java.lang.String;
import java.util.List;
import java.lang.Object;
import java.lang.StringBuilder;

public class SplitTableDto implements Serializable	// class@0000ae from classes.dex
{
    private List columnNameList;
    private String tableNames;

    public void SplitTableDto(String tableNames,List columnNameList){
       super();
       this.tableNames = tableNames;
       this.columnNameList = columnNameList;
    }
    protected boolean canEqual(Object other){
       return other instanceof SplitTableDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof SplitTableDto) {
             b = false;
          }else {
             SplitTableDto splitTableDt = o;
             if (!splitTableDt.canEqual(this)) {
                b = false;
             }else {
                String tableNames = this.getTableNames();
                String tableNames1 = splitTableDt.getTableNames();
                if (tableNames == null) {
                   if (tableNames1 != null) {
                      b = false;
                   }
                }else if(tableNames.equals(tableNames1)){
                }
                List columnNameLi = this.getColumnNameList();
                List columnNameLi1 = splitTableDt.getColumnNameList();
                if (columnNameLi == null) {
                   if (columnNameLi1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!columnNameLi.equals(columnNameLi1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public List getColumnNameList(){
       return this.columnNameList;
    }
    public String getTableNames(){
       return this.tableNames;
    }
    public int hashCode(){
       String $tableNames;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($tableNames = this.getTableNames()) == null)? i: $tableNames.hashCode();
       result = i1 + 59;
       List $columnNameList = this.getColumnNameList();
       i1 = result * 59;
       if ($columnNameList != null) {
          i = $columnNameList.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setColumnNameList(List columnNameList){
       this.columnNameList = columnNameList;
    }
    public void setTableNames(String tableNames){
       this.tableNames = tableNames;
    }
    public String toString(){
       return "SplitTableDto\(tableNames="+this.getTableNames()+", columnNameList="+this.getColumnNameList()+"\)";
    }
}
