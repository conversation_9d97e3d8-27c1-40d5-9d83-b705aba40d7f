package com.taikang.fly.check.service.SystemConfigService;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import java.lang.String;
import java.lang.Integer;
import com.taikang.fly.check.mybatis.dao.SystemConfigMapper;
import com.taikang.fly.check.mybatis.domain.SystemConfig;

public class SystemConfigService	// class@000300 from classes.dex
{
    private SystemConfigMapper systemConfigMapper;
    private static final Logger log;

    static {
       SystemConfigService.log = LoggerFactory.getLogger(SystemConfigService.class);
    }
    public void SystemConfigService(){
       super();
    }
    public Integer editApplicationName(String applicationName){
       return this.systemConfigMapper.editApplicationName(applicationName);
    }
    public SystemConfig getApplicationName(){
       return this.systemConfigMapper.getApplicationName();
    }
}
