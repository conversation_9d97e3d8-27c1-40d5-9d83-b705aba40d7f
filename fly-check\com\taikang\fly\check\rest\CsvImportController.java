package com.taikang.fly.check.rest.CsvImportController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import java.util.List;
import java.lang.String;
import com.taikang.fly.check.comm.CommResponse;
import com.taikang.fly.check.service.CsvImportService;
import java.lang.Integer;
import com.taikang.fly.check.dto.csventry.CsvConfigSearchDto;
import com.taikang.fly.check.comm.NativePage;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import com.taikang.fly.check.service.FieldTypeRemarksService;
import com.taikang.fly.check.dto.csventry.CsvConfigEntry;
import com.taikang.fly.check.dto.csventry.CsvImport;
import org.springframework.web.multipart.MultipartFile;
import com.taikang.fly.check.dto.csventry.CsvImportDTO;

public class CsvImportController	// class@000286 from classes.dex
{
    private CsvImportService csvImportService;
    private FieldTypeRemarksService fieldTypeRemarksService;
    private static final Logger log;

    static {
       CsvImportController.log = LoggerFactory.getLogger(CsvImportController.class);
    }
    public void CsvImportController(){
       super();
    }
    public CommResponse addConfigInfo(List list,String configName){
       this.csvImportService.addConfigInfo(list, configName);
       return CommResponse.success();
    }
    public CommResponse configInfoList(Integer page,Integer size,CsvConfigSearchDto configSearchDto){
       return CommResponse.success(this.csvImportService.getColumnTypeList(page, size, configSearchDto));
    }
    public CommResponse configNameList(){
       return CommResponse.success(this.csvImportService.findConfigNames());
    }
    public CommResponse delConfigByName(String configName){
       return CommResponse.success(this.csvImportService.delConfigByName(configName));
    }
    public CommResponse delConfigInfo(String id){
       return CommResponse.success(this.csvImportService.delConfigInfo(id));
    }
    public CommResponse delCsvData(String id){
       this.csvImportService.delCsvData(id);
       return CommResponse.success();
    }
    public void exportCSVConfigFile(HttpServletResponse response){
       try{
          this.csvImportService.exportCSVConfigFile(response);
       }catch(java.io.IOException e0){
          CsvImportController.log.info(e0.getMessage());
       }
       return;
    }
    public CommResponse fieldTypeDict(){
       return CommResponse.success(this.fieldTypeRemarksService.queryTypeInfo());
    }
    public CommResponse fieldTypeDictInfo(String dataType){
       return CommResponse.success(this.fieldTypeRemarksService.queryInfo(dataType));
    }
    public CommResponse getConfigInfo(String id){
       return CommResponse.success(this.csvImportService.getConfigInfo(id));
    }
    public CommResponse getConfigNameList(Integer page,Integer size,CsvConfigSearchDto configSearchDto){
       return CommResponse.success(this.csvImportService.getConfigNameList(page, size, configSearchDto));
    }
    public CommResponse importBefore(CsvImport csvImport){
       return CommResponse.success(this.csvImportService.csvHeaderList(csvImport));
    }
    public CommResponse importCSVConfigFile(MultipartFile file){
       return this.csvImportService.importCSVConfigFile(file);
    }
    public CommResponse importCsvData(CsvImportDTO csvImportDTO,List list){
       CommResponse commResponse = new CommResponse();
       this.csvImportService.importCsvData(csvImportDTO, list, commResponse);
       return commResponse;
    }
    public CommResponse updateConfigInfo(CsvConfigEntry csvConfigEntry){
       return CommResponse.success(this.csvImportService.updateConfigInfo(csvConfigEntry));
    }
}
