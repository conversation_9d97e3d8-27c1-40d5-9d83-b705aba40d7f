package com.taikang.fly.check.rest.FlyDataViewController;
import java.lang.Object;
import java.lang.String;
import com.taikang.fly.check.comm.CommResponse;
import java.lang.Integer;
import com.taikang.fly.check.service.TemplateService;
import javax.servlet.http.HttpServletResponse;
import java.lang.Boolean;
import com.taikang.fly.check.dto.templateInfo.TemplateInfoEditDto;
import com.taikang.fly.check.comm.NativePage;
import com.taikang.fly.check.mybatis.domain.TemplateInfo;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.util.StringUtils;

public class FlyDataViewController	// class@00028f from classes.dex
{
    private TemplateService templateService;

    public void FlyDataViewController(){
       super();
    }
    public CommResponse del(String id){
       return CommResponse.success(this.templateService.delete(id));
    }
    public CommResponse downloadTemplate(HttpServletResponse httpServletResponse){
       this.templateService.downloadTemplate(httpServletResponse);
       return CommResponse.success(Boolean.valueOf(true));
    }
    public CommResponse edit(TemplateInfoEditDto editDto){
       return CommResponse.success(this.templateService.edit(editDto));
    }
    public CommResponse execByIds(String ids){
       return this.templateService.execByTemplateIds(ids);
    }
    public CommResponse find(Integer pageNum,Integer pageSize){
       return CommResponse.success(this.templateService.queryAll(pageNum, pageSize));
    }
    public CommResponse findById(String id){
       return CommResponse.success(this.templateService.queryById(id));
    }
    public CommResponse importTemplate(MultipartFile file,String manufacturer){
       String errotMsg = this.templateService.parse(file, manufacturer);
       CommResponse uCommRespons = (StringUtils.isEmpty(errotMsg))? CommResponse.success(): CommResponse.error("-16", errotMsg, null);
       return uCommRespons;
    }
}
