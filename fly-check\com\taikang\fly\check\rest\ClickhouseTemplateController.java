package com.taikang.fly.check.rest.ClickhouseTemplateController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import java.lang.String;
import com.taikang.fly.check.comm.CommResponse;
import com.taikang.fly.check.dto.clickhouseFlyRule.ClickhouseFlyRuleTemplateRespDto;
import com.taikang.fly.check.service.ClickhouseFlyRuleTemplateService;
import java.lang.Integer;
import com.taikang.fly.check.dto.clickhouseFlyRule.ClickhouseFlyRuleAddTemplateSqlDto;
import javax.servlet.http.HttpServletResponse;
import com.taikang.fly.check.comm.Page;
import java.util.List;

public class ClickhouseTemplateController	// class@000285 from classes.dex
{
    private ClickhouseFlyRuleTemplateService clickhouseTemplateService;
    private static final Logger log;

    static {
       ClickhouseTemplateController.log = LoggerFactory.getLogger(ClickhouseTemplateController.class);
    }
    public void ClickhouseTemplateController(){
       super();
    }
    public CommResponse getFlyRuleTemplateByRuleName(String ruleName){
       return CommResponse.success(this.clickhouseTemplateService.getFlyRuleTemplateByRuleName(ruleName));
    }
    public CommResponse querySqlOne(Integer page,Integer size,ClickhouseFlyRuleAddTemplateSqlDto clickhouseFlyRuleAddTemplateSqlDto,HttpServletResponse response){
       return CommResponse.success(this.clickhouseTemplateService.querySqlOne(page, size, clickhouseFlyRuleAddTemplateSqlDto, response));
    }
    public CommResponse ruleNameList(){
       List list = this.clickhouseTemplateService.getRuleNameList();
       return CommResponse.success(list);
    }
}
