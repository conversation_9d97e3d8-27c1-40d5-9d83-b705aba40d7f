package com.taikang.fly.check.mybatis.dao.WorkOrderPoolMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import com.taikang.fly.check.mybatis.domain.WorkOrderPool;
import java.lang.Object;
import java.lang.String;

public interface abstract WorkOrderPoolMapper implements BaseMapper	// class@000228 from classes.dex
{

    List getSpecialType();
    int insert(WorkOrderPool p0);
    default int insert(Object p0){
       return this.insert(p0);
    }
    List selectAll();
    String selectCountByName(String p0,String p1);
}
