package org.springframework.boot.loader.archive.ExplodedArchive;
import org.springframework.boot.loader.archive.Archive;
import java.util.HashSet;
import java.lang.String;
import java.lang.Object;
import java.util.List;
import java.util.Arrays;
import java.util.Collection;
import java.io.File;
import java.lang.IllegalArgumentException;
import java.lang.StringBuilder;
import java.util.Set;
import java.util.jar.Manifest;
import java.io.FileInputStream;
import java.io.InputStream;
import java.lang.Throwable;
import org.springframework.boot.loader.archive.Archive$Entry;
import org.springframework.boot.loader.archive.ExplodedArchive$FileEntry;
import org.springframework.boot.loader.archive.JarFileArchive;
import org.springframework.boot.loader.archive.Archive$EntryFilter;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Collections;
import java.net.URL;
import java.net.URI;
import org.springframework.boot.loader.archive.ExplodedArchive$FileEntryIterator;

public class ExplodedArchive implements Archive	// class@000540 from classes.dex
{
    private Manifest manifest;
    private File manifestFile;
    private final boolean recursive;
    private final File root;
    private static final Set SKIPPED_NAMES;

    static {
       String[] stringArray = new String[]{".",".."};
       ExplodedArchive.SKIPPED_NAMES = new HashSet(Arrays.asList(stringArray));
    }
    public void ExplodedArchive(File root){
       super(root, true);
    }
    public void ExplodedArchive(File root,boolean recursive){
       super();
       if (!root.exists() || !root.isDirectory()) {
          throw new IllegalArgumentException("Invalid source folder "+root);
       }
       this.root = root;
       this.recursive = recursive;
       this.manifestFile = this.getManifestFile(root);
       return;
    }
    static Set access$100(){
       return ExplodedArchive.SKIPPED_NAMES;
    }
    private File getManifestFile(File root){
       File metaInf = new File(root, "META-INF");
       return new File(metaInf, "MANIFEST.MF");
    }
    public Manifest getManifest(){
       int i;
       try{
          if (this.manifest == null && this.manifestFile.exists()) {
             FileInputStream inputStream = new FileInputStream(this.manifestFile);
             try{
                i = 0;
                this.manifest = new Manifest(inputStream);
                if (inputStream != null) {
                   if (i) {
                      inputStream.close();
                   }else {
                      inputStream.close();
                   }
                }
             }catch(java.lang.Throwable e1){
                throw e1;
             }
          }
       }catch(java.lang.Throwable e1){
          i.addSuppressed(e1);
       }
       return this.manifest;
    }
    protected Archive getNestedArchive(Archive$Entry entry){
       File file = entry.getFile();
       ExplodedArchive uExplodedArc = (file.isDirectory())? new ExplodedArchive(file): new JarFileArchive(file);
       return uExplodedArc;
    }
    public List getNestedArchives(Archive$EntryFilter filter){
       List nestedArchives = new ArrayList();
       Iterator iterator = this.iterator();
       while (iterator.hasNext()) {
          Archive$Entry uEntry = iterator.next();
          if (filter.matches(uEntry)) {
             nestedArchives.add(this.getNestedArchive(uEntry));
          }
       }
       return Collections.unmodifiableList(nestedArchives);
    }
    public URL getUrl(){
       return this.root.toURI().toURL();
    }
    public Iterator iterator(){
       return new ExplodedArchive$FileEntryIterator(this.root, this.recursive);
    }
    public String toString(){
       String str;
       try{
          str = this.getUrl().toString();
       }catch(java.lang.Exception e0){
          str = "exploded archive";
       }
       return str;
    }
}
