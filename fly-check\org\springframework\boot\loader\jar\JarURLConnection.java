package org.springframework.boot.loader.jar.JarURLConnection;
import java.net.JarURLConnection;
import java.lang.ThreadLocal;
import java.io.FileNotFoundException;
import java.lang.String;
import java.lang.IllegalStateException;
import java.lang.Throwable;
import java.net.URL;
import org.springframework.boot.loader.jar.JarURLConnection$1;
import java.net.URLStreamHandler;
import org.springframework.boot.loader.jar.JarURLConnection$JarEntryName;
import org.springframework.boot.loader.jar.StringSequence;
import org.springframework.boot.loader.jar.JarFile;
import java.lang.StringBuilder;
import java.lang.Boolean;
import java.lang.Object;
import org.springframework.boot.loader.jar.JarEntry;
import java.lang.CharSequence;
import java.io.InputStream;
import org.springframework.boot.loader.jar.JarFile$JarFileType;
import java.io.IOException;
import org.springframework.boot.loader.data.RandomAccessData;
import java.util.zip.ZipEntry;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;
import java.security.Permission;
import java.io.FilePermission;
import org.springframework.boot.loader.data.RandomAccessDataFile;
import java.io.File;

final class JarURLConnection extends JarURLConnection	// class@00055c from classes.dex
{
    private JarEntry jarEntry;
    private final JarURLConnection$JarEntryName jarEntryName;
    private final JarFile jarFile;
    private URL jarFileUrl;
    private Permission permission;
    private static final JarURLConnection$JarEntryName EMPTY_JAR_ENTRY_NAME;
    private static final URL EMPTY_JAR_URL;
    private static final FileNotFoundException FILE_NOT_FOUND_EXCEPTION;
    private static final JarURLConnection NOT_FOUND_CONNECTION;
    private static final IllegalStateException NOT_FOUND_CONNECTION_EXCEPTION;
    private static final String READ_ACTION;
    private static final String SEPARATOR;
    private static ThreadLocal useFastExceptions;

    static {
       JarURLConnection.useFastExceptions = new ThreadLocal();
       JarURLConnection.FILE_NOT_FOUND_EXCEPTION = new FileNotFoundException("Jar file or entry not found");
       IllegalStateException illegalState = new IllegalStateException(JarURLConnection.FILE_NOT_FOUND_EXCEPTION);
       try{
          JarURLConnection.NOT_FOUND_CONNECTION_EXCEPTION = illegalState;
          JarURLConnection.EMPTY_JAR_URL = new URL("jar:", null, 0, "file:!/", new JarURLConnection$1());
          JarURLConnection.EMPTY_JAR_ENTRY_NAME = new JarURLConnection$JarEntryName(new StringSequence(""));
          JarURLConnection.NOT_FOUND_CONNECTION = JarURLConnection.notFound();
       }catch(java.net.MalformedURLException e6){
          throw new IllegalStateException(e6);
       }
    }
    private void JarURLConnection(URL url,JarFile jarFile,JarURLConnection$JarEntryName jarEntryName){
       super(JarURLConnection.EMPTY_JAR_URL);
       this.url = url;
       this.jarFile = jarFile;
       this.jarEntryName = jarEntryName;
    }
    static JarURLConnection$JarEntryName access$000(){
       return JarURLConnection.EMPTY_JAR_ENTRY_NAME;
    }
    private URL buildJarFileUrl(){
       try{
          String spec = this.jarFile.getUrl().getFile();
          if (spec.endsWith("!/")) {
             spec = spec.substring(0, (spec.length() - "!/".length()));
          }
          URL uRL = (spec.indexOf("!/") == -1)? new URL(spec): new URL("jar:"+spec);
          return uRL;
       }catch(java.net.MalformedURLException e0){
          throw new IllegalStateException(e0);
       }
    }
    static JarURLConnection get(URL url,JarFile jarFile){
       int index;
       JarURLConnection nOT_FOUND_CO;
       int i;
       JarEntry jarEntry;
       StringSequence spec = new StringSequence(url.getFile());
       if ((index = JarURLConnection.indexOfRootSpec(spec, jarFile.getPathFromRoot())) == -1) {
          nOT_FOUND_CO = (Boolean.TRUE.equals(JarURLConnection.useFastExceptions.get()))? JarURLConnection.NOT_FOUND_CONNECTION: new JarURLConnection(url, null, JarURLConnection.EMPTY_JAR_ENTRY_NAME);
       }else {
          while (true) {
             if ((i = spec.indexOf("!/", index)) > 0) {
                JarURLConnection$JarEntryName jarEntryName = JarURLConnection$JarEntryName.get(spec.subSequence(index, i));
                if ((jarEntry = jarFile.getJarEntry(jarEntryName.toCharSequence())) == null) {
                   nOT_FOUND_CO = JarURLConnection.notFound(jarFile, jarEntryName);
                   break ;
                }else {
                   jarFile = jarFile.getNestedJarFile(jarEntry);
                   index = i + "!/".length();
                }
             }else {
                JarURLConnection$JarEntryName jarEntryName1 = JarURLConnection$JarEntryName.get(spec, index);
                nOT_FOUND_CO = (Boolean.TRUE.equals(JarURLConnection.useFastExceptions.get()) && (!jarEntryName1.isEmpty() && !jarFile.containsEntry(jarEntryName1.toString())))? JarURLConnection.NOT_FOUND_CONNECTION: new JarURLConnection(url, jarFile, jarEntryName1);
             }
          }
       }
       return nOT_FOUND_CO;
    }
    private static int indexOfRootSpec(StringSequence file,String pathFromRoot){
       int separatorIndex;
       int i = ((separatorIndex = file.indexOf("!/")) >= 0 && file.startsWith(pathFromRoot, separatorIndex))? ("!/".length() + separatorIndex) + pathFromRoot.length(): -1;
       return i;
    }
    private static JarURLConnection notFound(){
       JarFile jarFile = null;
       JarURLConnection$JarEntryName jarEntryName = null;
       try{
          return JarURLConnection.notFound(jarFile, jarEntryName);
       }catch(java.io.IOException e0){
          throw new IllegalStateException(e0);
       }
    }
    private static JarURLConnection notFound(JarFile jarFile,JarURLConnection$JarEntryName jarEntryName){
       JarURLConnection nOT_FOUND_CO = (Boolean.TRUE.equals(JarURLConnection.useFastExceptions.get()))? JarURLConnection.NOT_FOUND_CONNECTION: new JarURLConnection(null, jarFile, jarEntryName);
       return nOT_FOUND_CO;
    }
    static void setUseFastExceptions(boolean useFastExceptions){
       JarURLConnection.useFastExceptions.set(Boolean.valueOf(useFastExceptions));
    }
    private void throwFileNotFound(Object entry,JarFile jarFile){
       if (Boolean.TRUE.equals(JarURLConnection.useFastExceptions.get())) {
          throw JarURLConnection.FILE_NOT_FOUND_EXCEPTION;
       }
       throw new FileNotFoundException("JAR entry "+entry+" not found in "+jarFile.getName());
    }
    public void connect(){
       if (this.jarFile == null) {
          throw JarURLConnection.FILE_NOT_FOUND_EXCEPTION;
       }
       if (!this.jarEntryName.isEmpty() && this.jarEntry == null) {
          this.jarEntry = this.jarFile.getJarEntry(this.getEntryName());
          if (this.jarEntry == null) {
             this.throwFileNotFound(this.jarEntryName, this.jarFile);
          }
       }
       this.connected = true;
       return;
    }
    public Object getContent(){
       this.connect();
       JarURLConnection tjarFile = (this.jarEntryName.isEmpty())? this.jarFile: super.getContent();
       return tjarFile;
    }
    public int getContentLength(){
       long length = this.getContentLengthLong();
       int i = ((length - 0x7fffffff) > 0)? -1: (int)length;
       return i;
    }
    public long getContentLengthLong(){
       JarEntry jarEntry;
       long l = -1;
       if (this.jarFile != null) {
          try{
             if (this.jarEntryName.isEmpty()) {
                l = (long)this.jarFile.size();
             }else if((jarEntry = this.getJarEntry()) != null){
                l = (long)(int)jarEntry.getSize();
             }
          }catch(java.io.IOException e1){
          }
       }
       return l;
    }
    public String getContentType(){
       String contentType = (this.jarEntryName != null)? this.jarEntryName.getContentType(): null;
       return contentType;
    }
    public String getEntryName(){
       if (this.jarFile == null) {
          throw JarURLConnection.NOT_FOUND_CONNECTION_EXCEPTION;
       }
       return this.jarEntryName.toString();
    }
    public InputStream getInputStream(){
       if (this.jarFile == null) {
          throw JarURLConnection.FILE_NOT_FOUND_EXCEPTION;
       }
       if (this.jarEntryName.isEmpty() && this.jarFile.getType() == JarFile$JarFileType.DIRECT) {
          throw new IOException("no entry name specified");
       }
       this.connect();
       InputStream inputStream = (this.jarEntryName.isEmpty())? this.jarFile.getData().getInputStream(): this.jarFile.getInputStream(this.jarEntry);
       if (inputStream == null) {
          this.throwFileNotFound(this.jarEntryName, this.jarFile);
       }
       return inputStream;
    }
    public JarEntry getJarEntry(){
       return this.getJarEntry();
    }
    public JarEntry getJarEntry(){
       JarEntry jarEntry;
       if (this.jarEntryName != null && !this.jarEntryName.isEmpty()) {
          this.connect();
          jarEntry = this.jarEntry;
       }else {
          jarEntry = null;
       }
       return jarEntry;
    }
    public JarFile getJarFile(){
       return this.getJarFile();
    }
    public JarFile getJarFile(){
       this.connect();
       return this.jarFile;
    }
    public URL getJarFileURL(){
       if (this.jarFile == null) {
          throw JarURLConnection.NOT_FOUND_CONNECTION_EXCEPTION;
       }
       if (this.jarFileUrl == null) {
          this.jarFileUrl = this.buildJarFileUrl();
       }
       return this.jarFileUrl;
    }
    public long getLastModified(){
       long l;
       JarEntry jarEntry;
       try{
          l = 0;
          if (this.jarFile != null && (!this.jarEntryName.isEmpty() && (jarEntry = this.getJarEntry()) != null)) {
             l = jarEntry.getTime();
          }
       }catch(java.io.IOException e1){
       }
       return l;
    }
    public Permission getPermission(){
       if (this.jarFile == null) {
          throw JarURLConnection.FILE_NOT_FOUND_EXCEPTION;
       }
       if (this.permission == null) {
          this.permission = new FilePermission(this.jarFile.getRootJarFile().getFile().getPath(), "read");
       }
       return this.permission;
    }
}
