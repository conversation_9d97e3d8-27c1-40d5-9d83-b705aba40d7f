package com.taikang.fly.check.mybatis.dao.FlyRuleMultiMapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.lang.String;
import com.taikang.fly.check.mybatis.domain.FlyRuleMulti;
import java.lang.Object;

public interface abstract FlyRuleMultiMapper implements BaseMapper	// class@0001f4 from classes.dex
{

    void deleteAll();
    int deleteByPrimaryKey(String p0);
    int insert(FlyRuleMulti p0);
    default int insert(Object p0){
       return this.insert(p0);
    }
    FlyRuleMulti selectByPrimaryKey(String p0);
    int updateByPrimaryKey(FlyRuleMulti p0);
}
