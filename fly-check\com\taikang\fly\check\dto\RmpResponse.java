package com.taikang.fly.check.dto.RmpResponse;
import java.lang.Object;
import java.lang.String;
import com.taikang.fly.check.comm.enums.ResponseCodeEnum;
import java.lang.StringBuilder;

public class RmpResponse	// class@0000aa from classes.dex
{
    private String code;
    private Object data;
    private String message;

    public void RmpResponse(){
       super();
    }
    public void RmpResponse(String code,String message,Object data){
       super();
       this.code = code;
       this.message = message;
       this.data = data;
    }
    public static RmpResponse error(String code,String message,Object t){
       return new RmpResponse(code, message, t);
    }
    public static RmpResponse success(){
       return new RmpResponse(ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getMsg(), null);
    }
    public static RmpResponse success(Object t){
       return new RmpResponse(ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getMsg(), t);
    }
    protected boolean canEqual(Object other){
       return other instanceof RmpResponse;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof RmpResponse) {
             b = false;
          }else {
             RmpResponse rmpResponse = o;
             if (!rmpResponse.canEqual(this)) {
                b = false;
             }else {
                String code = this.getCode();
                String code1 = rmpResponse.getCode();
                if (code == null) {
                   if (code1 != null) {
                      b = false;
                   }
                }else if(code.equals(code1)){
                }
                String message = this.getMessage();
                String message1 = rmpResponse.getMessage();
                if (message == null) {
                   if (message1 != null) {
                      b = false;
                   }
                }else if(message.equals(message1)){
                }
                Object data = this.getData();
                Object data1 = rmpResponse.getData();
                if (data == null) {
                   if (data1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!data.equals(data1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getCode(){
       return this.code;
    }
    public Object getData(){
       return this.data;
    }
    public String getMessage(){
       return this.message;
    }
    public int hashCode(){
       String $code;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($code = this.getCode()) == null)? i: $code.hashCode();
       result = i1 + 59;
       String $message = this.getMessage();
       int i2 = result * 59;
       i1 = ($message == null)? i: $message.hashCode();
       result = i2 + i1;
       Object $data = this.getData();
       i1 = result * 59;
       if ($data != null) {
          i = $data.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setCode(String code){
       this.code = code;
    }
    public void setData(Object data){
       this.data = data;
    }
    public void setMessage(String message){
       this.message = message;
    }
    public String toString(){
       return "RmpResponse\(code="+this.getCode()+", message="+this.getMessage()+", data="+this.getData()+"\)";
    }
}
