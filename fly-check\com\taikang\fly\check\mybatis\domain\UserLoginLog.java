package com.taikang.fly.check.mybatis.domain.UserLoginLog;
import java.lang.Object;
import java.lang.String;
import java.time.LocalDateTime;
import java.lang.Integer;
import java.lang.StringBuilder;

public class UserLoginLog	// class@000272 from classes.dex
{
    private LocalDateTime createTime;
    private String creator;
    private Integer failureTimes;
    private String id;
    private LocalDateTime lockTime;
    private LocalDateTime loginTime;
    private String modby;
    private LocalDateTime modifyTime;
    private String name;
    private String region;
    private String status;

    public void UserLoginLog(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof UserLoginLog;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof UserLoginLog){
          b = false;
       }else {
          UserLoginLog userLoginLog = o;
          if (!userLoginLog.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = userLoginLog.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             String name = this.getName();
             String name1 = userLoginLog.getName();
             if (name == null) {
                if (name1 != null) {
                   b = false;
                }
             }else if(name.equals(name1)){
             }
             String status = this.getStatus();
             String status1 = userLoginLog.getStatus();
             if (status == null) {
                if (status1 != null) {
                   b = false;
                }
             }else if(status.equals(status1)){
             }
             String region = this.getRegion();
             String region1 = userLoginLog.getRegion();
             if (region == null) {
                if (region1 != null) {
                   b = false;
                }
             }else if(region.equals(region1)){
             }
             LocalDateTime loginTime = this.getLoginTime();
             LocalDateTime loginTime1 = userLoginLog.getLoginTime();
             if (loginTime == null) {
                if (loginTime1 != null) {
                   b = false;
                }
             }else if(loginTime.equals(loginTime1)){
             }
             LocalDateTime lockTime = this.getLockTime();
             LocalDateTime lockTime1 = userLoginLog.getLockTime();
             if (lockTime == null) {
                if (lockTime1 != null) {
                   b = false;
                }
             }else if(lockTime.equals(lockTime1)){
             }
             Integer failureTimes = this.getFailureTimes();
             Integer failureTimes1 = userLoginLog.getFailureTimes();
             if (failureTimes == null) {
                if (failureTimes1 != null) {
                   b = false;
                }
             }else if(failureTimes.equals(failureTimes1)){
             }
             String creator = this.getCreator();
             String creator1 = userLoginLog.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             LocalDateTime createTime = this.getCreateTime();
             LocalDateTime createTime1 = userLoginLog.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modby = this.getModby();
             String modby1 = userLoginLog.getModby();
             if (modby == null) {
                if (modby1 != null) {
                label_00ff :
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             LocalDateTime modifyTime = this.getModifyTime();
             LocalDateTime modifyTime1 = userLoginLog.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             b = true;
          }
       }
       return b;
    }
    public LocalDateTime getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public Integer getFailureTimes(){
       return this.failureTimes;
    }
    public String getId(){
       return this.id;
    }
    public LocalDateTime getLockTime(){
       return this.lockTime;
    }
    public LocalDateTime getLoginTime(){
       return this.loginTime;
    }
    public String getModby(){
       return this.modby;
    }
    public LocalDateTime getModifyTime(){
       return this.modifyTime;
    }
    public String getName(){
       return this.name;
    }
    public String getRegion(){
       return this.region;
    }
    public String getStatus(){
       return this.status;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       String $name = this.getName();
       int i1 = result * 59;
       i = ($name == null)? 43: $name.hashCode();
       result = i1 + i;
       String $status = this.getStatus();
       i1 = result * 59;
       i = ($status == null)? 43: $status.hashCode();
       result = i1 + i;
       String $region = this.getRegion();
       i1 = result * 59;
       i = ($region == null)? 43: $region.hashCode();
       result = i1 + i;
       LocalDateTime $loginTime = this.getLoginTime();
       i1 = result * 59;
       i = ($loginTime == null)? 43: $loginTime.hashCode();
       result = i1 + i;
       LocalDateTime lockTime = this.getLockTime();
       i1 = result * 59;
       i = (lockTime == null)? 43: lockTime.hashCode();
       Integer failureTimes = this.getFailureTimes();
       i1 = (i1 + i) * 59;
       i = (failureTimes == null)? 43: failureTimes.hashCode();
       String creator = this.getCreator();
       i1 = (i1 + i) * 59;
       i = (creator == null)? 43: creator.hashCode();
       LocalDateTime createTime = this.getCreateTime();
       i1 = (i1 + i) * 59;
       i = (createTime == null)? 43: createTime.hashCode();
       String modby = this.getModby();
       i1 = (i1 + i) * 59;
       i = (modby == null)? 43: modby.hashCode();
       LocalDateTime modifyTime = this.getModifyTime();
       i1 = (i1 + i) * 59;
       i = (modifyTime == null)? 43: modifyTime.hashCode();
       return (i1 + i);
    }
    public void setCreateTime(LocalDateTime createTime){
       this.createTime = createTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setFailureTimes(Integer failureTimes){
       this.failureTimes = failureTimes;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setLockTime(LocalDateTime lockTime){
       this.lockTime = lockTime;
    }
    public void setLoginTime(LocalDateTime loginTime){
       this.loginTime = loginTime;
    }
    public void setModby(String modby){
       this.modby = modby;
    }
    public void setModifyTime(LocalDateTime modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setName(String name){
       this.name = name;
    }
    public void setRegion(String region){
       this.region = region;
    }
    public void setStatus(String status){
       this.status = status;
    }
    public String toString(){
       return "UserLoginLog\(id="+this.getId()+", name="+this.getName()+", status="+this.getStatus()+", region="+this.getRegion()+", loginTime="+this.getLoginTime()+", lockTime="+this.getLockTime()+", failureTimes="+this.getFailureTimes()+", creator="+this.getCreator()+", createTime="+this.getCreateTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+"\)";
    }
}
