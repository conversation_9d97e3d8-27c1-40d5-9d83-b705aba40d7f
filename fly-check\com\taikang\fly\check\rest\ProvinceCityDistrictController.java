package com.taikang.fly.check.rest.ProvinceCityDistrictController;
import java.lang.Class;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.Object;
import java.lang.String;
import com.taikang.fly.check.dto.RmpResponse;
import java.util.List;
import com.taikang.fly.check.service.ProvinceCityDistrictService;

public class ProvinceCityDistrictController	// class@0002a8 from classes.dex
{
    private ProvinceCityDistrictService provinceCityDistrictService;
    private static final Logger log;

    static {
       ProvinceCityDistrictController.log = LoggerFactory.getLogger(ProvinceCityDistrictController.class);
    }
    public void ProvinceCityDistrictController(){
       super();
    }
    public RmpResponse queryByPid(String pid){
       return RmpResponse.success(this.provinceCityDistrictService.queryByPid(pid));
    }
    public RmpResponse regionInfo(){
       return RmpResponse.success(this.provinceCityDistrictService.regionInfo());
    }
    public RmpResponse treeMap(){
       return RmpResponse.success(this.provinceCityDistrictService.treeMap());
    }
}
