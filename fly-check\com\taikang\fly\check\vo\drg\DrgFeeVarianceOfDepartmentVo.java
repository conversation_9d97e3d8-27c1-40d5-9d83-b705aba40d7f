package com.taikang.fly.check.vo.drg.DrgFeeVarianceOfDepartmentVo;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class DrgFeeVarianceOfDepartmentVo	// class@000367 from classes.dex
{
    private String averageCostPercentage;
    private String disease;
    private String drgAverageCost;
    private String drgCode;
    private String drgName;
    private String highAverageCost;
    private String highDepartmentName;
    private String highDepartmentPercentage;
    private String highNumber;
    private String lowAverageCost;
    private String lowDepartmentName;
    private String lowDepartmentPercentage;
    private String lowNumber;
    private String year;

    public void DrgFeeVarianceOfDepartmentVo(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof DrgFeeVarianceOfDepartmentVo;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof DrgFeeVarianceOfDepartmentVo){
          b = false;
       }else {
          DrgFeeVarianceOfDepartmentVo uDrgFeeVaria = o;
          if (!uDrgFeeVaria.canEqual(this)) {
             b = false;
          }else {
             String year = this.getYear();
             String year1 = uDrgFeeVaria.getYear();
             if (year == null) {
                if (year1 != null) {
                   b = false;
                }
             }else if(year.equals(year1)){
             }
             String disease = this.getDisease();
             String disease1 = uDrgFeeVaria.getDisease();
             if (disease == null) {
                if (disease1 != null) {
                   b = false;
                }
             }else if(disease.equals(disease1)){
             }
             String drgCode = this.getDrgCode();
             String drgCode1 = uDrgFeeVaria.getDrgCode();
             if (drgCode == null) {
                if (drgCode1 != null) {
                   b = false;
                }
             }else if(drgCode.equals(drgCode1)){
             }
             String drgName = this.getDrgName();
             String drgName1 = uDrgFeeVaria.getDrgName();
             if (drgName == null) {
                if (drgName1 != null) {
                   b = false;
                }
             }else if(drgName.equals(drgName1)){
             }
             String highDepartme = this.getHighDepartmentName();
             String highDepartme1 = uDrgFeeVaria.getHighDepartmentName();
             if (highDepartme == null) {
                if (highDepartme1 != null) {
                   b = false;
                }
             }else if(highDepartme.equals(highDepartme1)){
             }
             String highNumber = this.getHighNumber();
             String highNumber1 = uDrgFeeVaria.getHighNumber();
             if (highNumber == null) {
                if (highNumber1 != null) {
                   b = false;
                }
             }else if(highNumber.equals(highNumber1)){
             }
             String highAverageC = this.getHighAverageCost();
             String highAverageC1 = uDrgFeeVaria.getHighAverageCost();
             if (highAverageC == null) {
                if (highAverageC1 != null) {
                label_00bd :
                   b = false;
                }
             }else if(highAverageC.equals(highAverageC1)){
             }
             String lowDepartmen = this.getLowDepartmentName();
             String lowDepartmen1 = uDrgFeeVaria.getLowDepartmentName();
             if (lowDepartmen == null) {
                if (lowDepartmen1 != null) {
                   b = false;
                }
             }else if(lowDepartmen.equals(lowDepartmen1)){
             }
             String lowNumber = this.getLowNumber();
             String lowNumber1 = uDrgFeeVaria.getLowNumber();
             if (lowNumber == null) {
                if (lowNumber1 != null) {
                   b = false;
                }
             }else if(lowNumber.equals(lowNumber1)){
             }
             String lowAverageCo = this.getLowAverageCost();
             String lowAverageCo1 = uDrgFeeVaria.getLowAverageCost();
             if (lowAverageCo == null) {
                if (lowAverageCo1 != null) {
                label_0105 :
                   b = false;
                }
             }else if(lowAverageCo.equals(lowAverageCo1)){
             }
             String averageCostP = this.getAverageCostPercentage();
             String averageCostP1 = uDrgFeeVaria.getAverageCostPercentage();
             if (averageCostP == null) {
                if (averageCostP1 != null) {
                   b = false;
                }
             }else if(averageCostP.equals(averageCostP1)){
             }
             String drgAverageCo = this.getDrgAverageCost();
             String drgAverageCo1 = uDrgFeeVaria.getDrgAverageCost();
             if (drgAverageCo == null) {
                if (drgAverageCo1 != null) {
                label_0135 :
                   b = false;
                }
             }else if(drgAverageCo.equals(drgAverageCo1)){
             }
             String lowDepartmen2 = this.getLowDepartmentPercentage();
             String lowDepartmen3 = uDrgFeeVaria.getLowDepartmentPercentage();
             if (lowDepartmen2 == null) {
                if (lowDepartmen3 != null) {
                   b = false;
                }
             }else if(lowDepartmen2.equals(lowDepartmen3)){
             }
             String highDepartme2 = this.getHighDepartmentPercentage();
             String highDepartme3 = uDrgFeeVaria.getHighDepartmentPercentage();
             if (highDepartme2 == null) {
                if (highDepartme3 != null) {
                label_0165 :
                   b = false;
                }
             }else if(highDepartme2.equals(highDepartme3)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getAverageCostPercentage(){
       return this.averageCostPercentage;
    }
    public String getDisease(){
       return this.disease;
    }
    public String getDrgAverageCost(){
       return this.drgAverageCost;
    }
    public String getDrgCode(){
       return this.drgCode;
    }
    public String getDrgName(){
       return this.drgName;
    }
    public String getHighAverageCost(){
       return this.highAverageCost;
    }
    public String getHighDepartmentName(){
       return this.highDepartmentName;
    }
    public String getHighDepartmentPercentage(){
       return this.highDepartmentPercentage;
    }
    public String getHighNumber(){
       return this.highNumber;
    }
    public String getLowAverageCost(){
       return this.lowAverageCost;
    }
    public String getLowDepartmentName(){
       return this.lowDepartmentName;
    }
    public String getLowDepartmentPercentage(){
       return this.lowDepartmentPercentage;
    }
    public String getLowNumber(){
       return this.lowNumber;
    }
    public String getYear(){
       return this.year;
    }
    public int hashCode(){
       String $year;
       int PRIME = 59;
       int result = 1;
       int i = (($year = this.getYear()) == null)? 43: $year.hashCode();
       result = i + 59;
       String $disease = this.getDisease();
       int i1 = result * 59;
       i = ($disease == null)? 43: $disease.hashCode();
       result = i1 + i;
       String $drgCode = this.getDrgCode();
       i1 = result * 59;
       i = ($drgCode == null)? 43: $drgCode.hashCode();
       result = i1 + i;
       String $drgName = this.getDrgName();
       i1 = result * 59;
       i = ($drgName == null)? 43: $drgName.hashCode();
       result = i1 + i;
       String $highDepartmentName = this.getHighDepartmentName();
       i1 = result * 59;
       i = ($highDepartmentName == null)? 43: $highDepartmentName.hashCode();
       result = i1 + i;
       String highNumber = this.getHighNumber();
       i1 = result * 59;
       i = (highNumber == null)? 43: highNumber.hashCode();
       String highAverageC = this.getHighAverageCost();
       i1 = (i1 + i) * 59;
       i = (highAverageC == null)? 43: highAverageC.hashCode();
       String lowDepartmen = this.getLowDepartmentName();
       i1 = (i1 + i) * 59;
       i = (lowDepartmen == null)? 43: lowDepartmen.hashCode();
       String lowNumber = this.getLowNumber();
       i1 = (i1 + i) * 59;
       i = (lowNumber == null)? 43: lowNumber.hashCode();
       String lowAverageCo = this.getLowAverageCost();
       i1 = (i1 + i) * 59;
       i = (lowAverageCo == null)? 43: lowAverageCo.hashCode();
       String averageCostP = this.getAverageCostPercentage();
       i1 = (i1 + i) * 59;
       i = (averageCostP == null)? 43: averageCostP.hashCode();
       String drgAverageCo = this.getDrgAverageCost();
       i1 = (i1 + i) * 59;
       i = (drgAverageCo == null)? 43: drgAverageCo.hashCode();
       String lowDepartmen1 = this.getLowDepartmentPercentage();
       i1 = (i1 + i) * 59;
       i = (lowDepartmen1 == null)? 43: lowDepartmen1.hashCode();
       String highDepartme = this.getHighDepartmentPercentage();
       i1 = (i1 + i) * 59;
       i = (highDepartme == null)? 43: highDepartme.hashCode();
       return (i1 + i);
    }
    public void setAverageCostPercentage(String averageCostPercentage){
       this.averageCostPercentage = averageCostPercentage;
    }
    public void setDisease(String disease){
       this.disease = disease;
    }
    public void setDrgAverageCost(String drgAverageCost){
       this.drgAverageCost = drgAverageCost;
    }
    public void setDrgCode(String drgCode){
       this.drgCode = drgCode;
    }
    public void setDrgName(String drgName){
       this.drgName = drgName;
    }
    public void setHighAverageCost(String highAverageCost){
       this.highAverageCost = highAverageCost;
    }
    public void setHighDepartmentName(String highDepartmentName){
       this.highDepartmentName = highDepartmentName;
    }
    public void setHighDepartmentPercentage(String highDepartmentPercentage){
       this.highDepartmentPercentage = highDepartmentPercentage;
    }
    public void setHighNumber(String highNumber){
       this.highNumber = highNumber;
    }
    public void setLowAverageCost(String lowAverageCost){
       this.lowAverageCost = lowAverageCost;
    }
    public void setLowDepartmentName(String lowDepartmentName){
       this.lowDepartmentName = lowDepartmentName;
    }
    public void setLowDepartmentPercentage(String lowDepartmentPercentage){
       this.lowDepartmentPercentage = lowDepartmentPercentage;
    }
    public void setLowNumber(String lowNumber){
       this.lowNumber = lowNumber;
    }
    public void setYear(String year){
       this.year = year;
    }
    public String toString(){
       return "DrgFeeVarianceOfDepartmentVo\(year="+this.getYear()+", disease="+this.getDisease()+", drgCode="+this.getDrgCode()+", drgName="+this.getDrgName()+", highDepartmentName="+this.getHighDepartmentName()+", highNumber="+this.getHighNumber()+", highAverageCost="+this.getHighAverageCost()+", lowDepartmentName="+this.getLowDepartmentName()+", lowNumber="+this.getLowNumber()+", lowAverageCost="+this.getLowAverageCost()+", averageCostPercentage="+this.getAverageCostPercentage()+", drgAverageCost="+this.getDrgAverageCost()+", lowDepartmentPercentage="+this.getLowDepartmentPercentage()+", highDepartmentPercentage="+this.getHighDepartmentPercentage()+"\)";
    }
}
