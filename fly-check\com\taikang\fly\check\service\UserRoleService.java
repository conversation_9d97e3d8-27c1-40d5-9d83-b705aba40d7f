package com.taikang.fly.check.service.UserRoleService;
import java.lang.Object;
import java.lang.String;
import java.util.List;
import com.taikang.fly.check.mybatis.dao.UserRoleMapper;
import com.taikang.fly.check.dto.mapstruct.RoleMapping;
import java.util.Iterator;
import com.taikang.fly.check.dto.system.role.RoleIndexResDto;
import com.taikang.fly.check.dto.system.user.UserRoleManageDto;
import java.lang.CharSequence;
import org.apache.commons.lang3.StringUtils;
import java.util.HashMap;
import java.util.Map;
import java.util.Date;
import com.taikang.fly.check.utils.SequenceGenerator;

public class UserRoleService	// class@000307 from classes.dex
{
    private RoleMapping theRoleMapper;
    private UserRoleMapper userRoleMapper;

    public void UserRoleService(){
       super();
    }
    public List findAll(String userId){
       List list = this.userRoleMapper.findRoleList();
       List checkedRoleIds = this.roleIdsByUserCode(userId);
       List roleList = null;
       roleList = this.theRoleMapper.listRoleMapToListDto(list);
       Iterator iterator = roleList.iterator();
       while (iterator.hasNext()) {
          RoleIndexResDto roleIndexRes = iterator.next();
          if (checkedRoleIds.contains(roleIndexRes.getRoleCode())) {
             roleIndexRes.setCheck(true);
          }else {
             roleIndexRes.setCheck(false);
          }
       }
       return roleList;
    }
    public List roleIdsByUserCode(String userCode){
       List roleIds = this.userRoleMapper.roleIdsByUserCode(userCode);
       return roleIds;
    }
    public void userRoleManage(UserRoleManageDto userRoleManageDto){
       this.userRoleMapper.deleteByUserCode(userRoleManageDto.getUserCode());
       String roleCodes = (userRoleManageDto.getRoleCodes() == null)? "": userRoleManageDto.getRoleCodes();
       if (!StringUtils.isBlank(roleCodes)) {
          String[] stringArray = roleCodes.split(",");
          HashMap hashMap = new HashMap(7);
          hashMap.clear();
          hashMap.put("creator", "system");
          hashMap.put("createTime", new Date());
          int len = stringArray.length;
          for (int i = 0; i < len; i = i + 1) {
             hashMap.put("userCode", userRoleManageDto.getUserCode());
             hashMap.put("roleCode", stringArray[i]);
             hashMap.put("modby", "system");
             hashMap.put("signature", "0");
             hashMap.put("id", SequenceGenerator.getId());
             this.userRoleMapper.addUserRole(hashMap);
          }
       }
       return;
    }
}
