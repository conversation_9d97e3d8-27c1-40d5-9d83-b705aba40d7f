package com.taikang.fly.check.FlyCheckApplication;
import java.lang.Object;
import java.lang.String;
import org.springframework.core.env.StandardEnvironment;
import org.springframework.boot.builder.SpringApplicationBuilder;
import java.lang.Class;
import org.springframework.core.env.ConfigurableEnvironment;
import com.github.ulisesbocchio.jar.resources.JarResourceLoader;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ResourceLoader;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.ApplicationContext;
import com.taikang.fly.check.utils.SpringUtil;

public class FlyCheckApplication	// class@00006b from classes.dex
{

    public void FlyCheckApplication(){
       super();
    }
    public static void main(String[] args){
       StandardEnvironment environment = new StandardEnvironment();
       Class[] uClassArray = new Class[0];
       uClassArray = new Class[]{FlyCheckApplication.class};
       ApplicationContext app = new SpringApplicationBuilder(uClassArray).sources(uClassArray).environment(environment).resourceLoader(new JarResourceLoader(environment, "resources.extract.dir")).build().run(args);
       SpringUtil.setApplicationContext(app);
    }
}
