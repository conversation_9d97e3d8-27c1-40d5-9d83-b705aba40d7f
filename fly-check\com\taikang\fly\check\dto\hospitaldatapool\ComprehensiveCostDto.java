package com.taikang.fly.check.dto.hospitaldatapool.ComprehensiveCostDto;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;

public class ComprehensiveCostDto implements Serializable	// class@00012b from classes.dex
{
    private String dischargeDepartment;
    private String patientName;
    private String totalCost;
    private String year;
    private static final long serialVersionUID = 0x1;

    public void ComprehensiveCostDto(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof ComprehensiveCostDto;
    }
    public boolean equals(Object o){
       boolean b = true;
       if (o != this) {
          if (!o instanceof ComprehensiveCostDto) {
             b = false;
          }else {
             ComprehensiveCostDto uComprehensi = o;
             if (!uComprehensi.canEqual(this)) {
                b = false;
             }else {
                String year = this.getYear();
                String year1 = uComprehensi.getYear();
                if (year == null) {
                   if (year1 != null) {
                      b = false;
                   }
                }else if(year.equals(year1)){
                }
                String patientName = this.getPatientName();
                String patientName1 = uComprehensi.getPatientName();
                if (patientName == null) {
                   if (patientName1 != null) {
                      b = false;
                   }
                }else if(patientName.equals(patientName1)){
                }
                String dischargeDep = this.getDischargeDepartment();
                String dischargeDep1 = uComprehensi.getDischargeDepartment();
                if (dischargeDep == null) {
                   if (dischargeDep1 != null) {
                      b = false;
                   }
                }else if(dischargeDep.equals(dischargeDep1)){
                }
                String totalCost = this.getTotalCost();
                String totalCost1 = uComprehensi.getTotalCost();
                if (totalCost == null) {
                   if (totalCost1 == null) {
                   label_0004 :
                      return b;
                   }
                }else if(!totalCost.equals(totalCost1)){
                }
                b = false;
                goto label_0004 ;
             }
          }
       }
    }
    public String getDischargeDepartment(){
       return this.dischargeDepartment;
    }
    public String getPatientName(){
       return this.patientName;
    }
    public String getTotalCost(){
       return this.totalCost;
    }
    public String getYear(){
       return this.year;
    }
    public int hashCode(){
       String $year;
       int i = 43;
       int PRIME = 59;
       int result = 1;
       int i1 = (($year = this.getYear()) == null)? i: $year.hashCode();
       result = i1 + 59;
       String $patientName = this.getPatientName();
       int i2 = result * 59;
       i1 = ($patientName == null)? i: $patientName.hashCode();
       result = i2 + i1;
       String $dischargeDepartment = this.getDischargeDepartment();
       i2 = result * 59;
       i1 = ($dischargeDepartment == null)? i: $dischargeDepartment.hashCode();
       result = i2 + i1;
       String $totalCost = this.getTotalCost();
       i1 = result * 59;
       if ($totalCost != null) {
          i = $totalCost.hashCode();
       }
       result = i1 + i;
       return result;
    }
    public void setDischargeDepartment(String dischargeDepartment){
       this.dischargeDepartment = dischargeDepartment;
    }
    public void setPatientName(String patientName){
       this.patientName = patientName;
    }
    public void setTotalCost(String totalCost){
       this.totalCost = totalCost;
    }
    public void setYear(String year){
       this.year = year;
    }
    public String toString(){
       return "ComprehensiveCostDto\(year="+this.getYear()+", patientName="+this.getPatientName()+", dischargeDepartment="+this.getDischargeDepartment()+", totalCost="+this.getTotalCost()+"\)";
    }
}
