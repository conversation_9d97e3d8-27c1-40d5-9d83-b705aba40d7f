package com.taikang.fly.check.security.filter.XssRequestWrapper$1;
import javax.servlet.ServletInputStream;
import com.taikang.fly.check.security.filter.XssRequestWrapper;
import java.io.ByteArrayInputStream;
import javax.servlet.ReadListener;
import org.slf4j.Logger;
import java.lang.StringBuilder;
import java.lang.String;

class XssRequestWrapper$1 extends ServletInputStream	// class@0002c6 from classes.dex
{
    final XssRequestWrapper this$0;
    final ByteArrayInputStream val$byteArrayInputStream;

    void XssRequestWrapper$1(XssRequestWrapper this$0,ByteArrayInputStream p1){
       this.this$0 = this$0;
       this.val$byteArrayInputStream = p1;
       super();
    }
    public boolean isFinished(){
       return false;
    }
    public boolean isReady(){
       return false;
    }
    public int read(){
       return this.val$byteArrayInputStream.read();
    }
    public void setReadListener(ReadListener readListener){
       int i = 0;
       XssRequestWrapper.access$000().info("--- "+i);
    }
}
