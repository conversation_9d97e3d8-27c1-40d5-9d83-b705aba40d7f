package com.taikang.fly.check.mybatis.domain.FileImportLog;
import java.lang.Object;
import java.lang.String;
import java.time.LocalDateTime;
import java.lang.StringBuilder;

public class FileImportLog	// class@000240 from classes.dex
{
    private LocalDateTime createTime;
    private String creator;
    private String fileName;
    private String filePath;
    private String fileSize;
    private String fileType;
    private String id;
    private LocalDateTime importDate;
    private String importState;
    private String modby;
    private LocalDateTime modifyTime;
    private String operator;

    public void FileImportLog(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof FileImportLog;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof FileImportLog){
          b = false;
       }else {
          FileImportLog uFileImportL = o;
          if (!uFileImportL.canEqual(this)) {
             b = false;
          }else {
             String id = this.getId();
             String id1 = uFileImportL.getId();
             if (id == null) {
                if (id1 != null) {
                   b = false;
                }
             }else if(id.equals(id1)){
             }
             LocalDateTime importDate = this.getImportDate();
             LocalDateTime importDate1 = uFileImportL.getImportDate();
             if (importDate == null) {
                if (importDate1 != null) {
                   b = false;
                }
             }else if(importDate.equals(importDate1)){
             }
             String fileName = this.getFileName();
             String fileName1 = uFileImportL.getFileName();
             if (fileName == null) {
                if (fileName1 != null) {
                   b = false;
                }
             }else if(fileName.equals(fileName1)){
             }
             String fileSize = this.getFileSize();
             String fileSize1 = uFileImportL.getFileSize();
             if (fileSize == null) {
                if (fileSize1 != null) {
                   b = false;
                }
             }else if(fileSize.equals(fileSize1)){
             }
             String operator = this.getOperator();
             String operator1 = uFileImportL.getOperator();
             if (operator == null) {
                if (operator1 != null) {
                   b = false;
                }
             }else if(operator.equals(operator1)){
             }
             String importState = this.getImportState();
             String importState1 = uFileImportL.getImportState();
             if (importState == null) {
                if (importState1 != null) {
                   b = false;
                }
             }else if(importState.equals(importState1)){
             }
             String creator = this.getCreator();
             String creator1 = uFileImportL.getCreator();
             if (creator == null) {
                if (creator1 != null) {
                label_00bb :
                   b = false;
                }
             }else if(creator.equals(creator1)){
             }
             LocalDateTime createTime = this.getCreateTime();
             LocalDateTime createTime1 = uFileImportL.getCreateTime();
             if (createTime == null) {
                if (createTime1 != null) {
                   b = false;
                }
             }else if(createTime.equals(createTime1)){
             }
             String modby = this.getModby();
             String modby1 = uFileImportL.getModby();
             if (modby == null) {
                if (modby1 != null) {
                   b = false;
                }
             }else if(modby.equals(modby1)){
             }
             LocalDateTime modifyTime = this.getModifyTime();
             LocalDateTime modifyTime1 = uFileImportL.getModifyTime();
             if (modifyTime == null) {
                if (modifyTime1 != null) {
                   b = false;
                }
             }else if(modifyTime.equals(modifyTime1)){
             }
             String filePath = this.getFilePath();
             String filePath1 = uFileImportL.getFilePath();
             if (filePath == null) {
                if (filePath1 != null) {
                   b = false;
                }
             }else if(filePath.equals(filePath1)){
             }
             String fileType = this.getFileType();
             String fileType1 = uFileImportL.getFileType();
             if (fileType == null) {
                if (fileType1 != null) {
                label_0131 :
                   b = false;
                }
             }else if(fileType.equals(fileType1)){
             }
             b = true;
          }
       }
       return b;
    }
    public LocalDateTime getCreateTime(){
       return this.createTime;
    }
    public String getCreator(){
       return this.creator;
    }
    public String getFileName(){
       return this.fileName;
    }
    public String getFilePath(){
       return this.filePath;
    }
    public String getFileSize(){
       return this.fileSize;
    }
    public String getFileType(){
       return this.fileType;
    }
    public String getId(){
       return this.id;
    }
    public LocalDateTime getImportDate(){
       return this.importDate;
    }
    public String getImportState(){
       return this.importState;
    }
    public String getModby(){
       return this.modby;
    }
    public LocalDateTime getModifyTime(){
       return this.modifyTime;
    }
    public String getOperator(){
       return this.operator;
    }
    public int hashCode(){
       String $id;
       int PRIME = 59;
       int result = 1;
       int i = (($id = this.getId()) == null)? 43: $id.hashCode();
       result = i + 59;
       LocalDateTime $importDate = this.getImportDate();
       int i1 = result * 59;
       i = ($importDate == null)? 43: $importDate.hashCode();
       result = i1 + i;
       String $fileName = this.getFileName();
       i1 = result * 59;
       i = ($fileName == null)? 43: $fileName.hashCode();
       result = i1 + i;
       String $fileSize = this.getFileSize();
       i1 = result * 59;
       i = ($fileSize == null)? 43: $fileSize.hashCode();
       result = i1 + i;
       String $operator = this.getOperator();
       i1 = result * 59;
       i = ($operator == null)? 43: $operator.hashCode();
       result = i1 + i;
       String importState = this.getImportState();
       i1 = result * 59;
       i = (importState == null)? 43: importState.hashCode();
       String creator = this.getCreator();
       i1 = (i1 + i) * 59;
       i = (creator == null)? 43: creator.hashCode();
       LocalDateTime createTime = this.getCreateTime();
       i1 = (i1 + i) * 59;
       i = (createTime == null)? 43: createTime.hashCode();
       String modby = this.getModby();
       i1 = (i1 + i) * 59;
       i = (modby == null)? 43: modby.hashCode();
       LocalDateTime modifyTime = this.getModifyTime();
       i1 = (i1 + i) * 59;
       i = (modifyTime == null)? 43: modifyTime.hashCode();
       String filePath = this.getFilePath();
       i1 = (i1 + i) * 59;
       i = (filePath == null)? 43: filePath.hashCode();
       String fileType = this.getFileType();
       i1 = (i1 + i) * 59;
       i = (fileType == null)? 43: fileType.hashCode();
       return (i1 + i);
    }
    public void setCreateTime(LocalDateTime createTime){
       this.createTime = createTime;
    }
    public void setCreator(String creator){
       this.creator = creator;
    }
    public void setFileName(String fileName){
       this.fileName = fileName;
    }
    public void setFilePath(String filePath){
       this.filePath = filePath;
    }
    public void setFileSize(String fileSize){
       this.fileSize = fileSize;
    }
    public void setFileType(String fileType){
       this.fileType = fileType;
    }
    public void setId(String id){
       this.id = id;
    }
    public void setImportDate(LocalDateTime importDate){
       this.importDate = importDate;
    }
    public void setImportState(String importState){
       this.importState = importState;
    }
    public void setModby(String modby){
       this.modby = modby;
    }
    public void setModifyTime(LocalDateTime modifyTime){
       this.modifyTime = modifyTime;
    }
    public void setOperator(String operator){
       this.operator = operator;
    }
    public String toString(){
       return "FileImportLog\(id="+this.getId()+", importDate="+this.getImportDate()+", fileName="+this.getFileName()+", fileSize="+this.getFileSize()+", operator="+this.getOperator()+", importState="+this.getImportState()+", creator="+this.getCreator()+", createTime="+this.getCreateTime()+", modby="+this.getModby()+", modifyTime="+this.getModifyTime()+", filePath="+this.getFilePath()+", fileType="+this.getFileType()+"\)";
    }
}
