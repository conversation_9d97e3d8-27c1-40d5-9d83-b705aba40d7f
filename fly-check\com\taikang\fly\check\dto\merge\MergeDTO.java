package com.taikang.fly.check.dto.merge.MergeDTO;
import java.io.Serializable;
import java.lang.Object;
import java.lang.String;
import java.lang.Double;
import java.lang.Integer;
import java.lang.StringBuilder;

public class MergeDTO implements Serializable	// class@000193 from classes.dex
{
    private String datasources;
    private String dischargeDepartment;
    private String endTime;
    private Integer maxHospitalizationDays;
    private Double maxTotalMedicalAmount;
    private Integer minHospitalizationDays;
    private Double minTotalMedicalAmount;
    private String policyBasis;
    private String ruleName;
    private String ruleType;
    private String startTime;

    public void MergeDTO(){
       super();
    }
    protected boolean canEqual(Object other){
       return other instanceof MergeDTO;
    }
    public boolean equals(Object o){
       boolean b;
       if (o == this) {
          b = true;
       }else if(!o instanceof MergeDTO){
          b = false;
       }else {
          MergeDTO mergeDTO = o;
          if (!mergeDTO.canEqual(this)) {
             b = false;
          }else {
             String ruleName = this.getRuleName();
             String ruleName1 = mergeDTO.getRuleName();
             if (ruleName == null) {
                if (ruleName1 != null) {
                   b = false;
                }
             }else if(ruleName.equals(ruleName1)){
             }
             String ruleType = this.getRuleType();
             String ruleType1 = mergeDTO.getRuleType();
             if (ruleType == null) {
                if (ruleType1 != null) {
                   b = false;
                }
             }else if(ruleType.equals(ruleType1)){
             }
             String dischargeDep = this.getDischargeDepartment();
             String dischargeDep1 = mergeDTO.getDischargeDepartment();
             if (dischargeDep == null) {
                if (dischargeDep1 != null) {
                   b = false;
                }
             }else if(dischargeDep.equals(dischargeDep1)){
             }
             Double minTotalMedi = this.getMinTotalMedicalAmount();
             Double minTotalMedi1 = mergeDTO.getMinTotalMedicalAmount();
             if (minTotalMedi == null) {
                if (minTotalMedi1 != null) {
                   b = false;
                }
             }else if(minTotalMedi.equals(minTotalMedi1)){
             }
             Double maxTotalMedi = this.getMaxTotalMedicalAmount();
             Double maxTotalMedi1 = mergeDTO.getMaxTotalMedicalAmount();
             if (maxTotalMedi == null) {
                if (maxTotalMedi1 != null) {
                   b = false;
                }
             }else if(maxTotalMedi.equals(maxTotalMedi1)){
             }
             Integer minHospitali = this.getMinHospitalizationDays();
             Integer minHospitali1 = mergeDTO.getMinHospitalizationDays();
             if (minHospitali == null) {
                if (minHospitali1 != null) {
                label_00a1 :
                   b = false;
                }
             }else if(minHospitali.equals(minHospitali1)){
             }
             Integer maxHospitali = this.getMaxHospitalizationDays();
             Integer maxHospitali1 = mergeDTO.getMaxHospitalizationDays();
             if (maxHospitali == null) {
                if (maxHospitali1 != null) {
                   b = false;
                }
             }else if(maxHospitali.equals(maxHospitali1)){
             }
             String startTime = this.getStartTime();
             String startTime1 = mergeDTO.getStartTime();
             if (startTime == null) {
                if (startTime1 != null) {
                label_00d1 :
                   b = false;
                }
             }else if(startTime.equals(startTime1)){
             }
             String endTime = this.getEndTime();
             String endTime1 = mergeDTO.getEndTime();
             if (endTime == null) {
                if (endTime1 != null) {
                   b = false;
                }
             }else if(endTime.equals(endTime1)){
             }
             String policyBasis = this.getPolicyBasis();
             String policyBasis1 = mergeDTO.getPolicyBasis();
             if (policyBasis == null) {
                if (policyBasis1 != null) {
                label_0101 :
                   b = false;
                }
             }else if(policyBasis.equals(policyBasis1)){
             }
             String datasources = this.getDatasources();
             String datasources1 = mergeDTO.getDatasources();
             if (datasources == null) {
                if (datasources1 != null) {
                   b = false;
                }
             }else if(datasources.equals(datasources1)){
             }
             b = true;
          }
       }
       return b;
    }
    public String getDatasources(){
       return this.datasources;
    }
    public String getDischargeDepartment(){
       return this.dischargeDepartment;
    }
    public String getEndTime(){
       return this.endTime;
    }
    public Integer getMaxHospitalizationDays(){
       return this.maxHospitalizationDays;
    }
    public Double getMaxTotalMedicalAmount(){
       return this.maxTotalMedicalAmount;
    }
    public Integer getMinHospitalizationDays(){
       return this.minHospitalizationDays;
    }
    public Double getMinTotalMedicalAmount(){
       return this.minTotalMedicalAmount;
    }
    public String getPolicyBasis(){
       return this.policyBasis;
    }
    public String getRuleName(){
       return this.ruleName;
    }
    public String getRuleType(){
       return this.ruleType;
    }
    public String getStartTime(){
       return this.startTime;
    }
    public int hashCode(){
       String $ruleName;
       int PRIME = 59;
       int result = 1;
       int i = (($ruleName = this.getRuleName()) == null)? 43: $ruleName.hashCode();
       result = i + 59;
       String $ruleType = this.getRuleType();
       int i1 = result * 59;
       i = ($ruleType == null)? 43: $ruleType.hashCode();
       result = i1 + i;
       String $dischargeDepartment = this.getDischargeDepartment();
       i1 = result * 59;
       i = ($dischargeDepartment == null)? 43: $dischargeDepartment.hashCode();
       result = i1 + i;
       Double $minTotalMedicalAmount = this.getMinTotalMedicalAmount();
       i1 = result * 59;
       i = ($minTotalMedicalAmount == null)? 43: $minTotalMedicalAmount.hashCode();
       result = i1 + i;
       Double $maxTotalMedicalAmount = this.getMaxTotalMedicalAmount();
       i1 = result * 59;
       i = ($maxTotalMedicalAmount == null)? 43: $maxTotalMedicalAmount.hashCode();
       result = i1 + i;
       Integer minHospitali = this.getMinHospitalizationDays();
       i1 = result * 59;
       i = (minHospitali == null)? 43: minHospitali.hashCode();
       Integer maxHospitali = this.getMaxHospitalizationDays();
       i1 = (i1 + i) * 59;
       i = (maxHospitali == null)? 43: maxHospitali.hashCode();
       String startTime = this.getStartTime();
       i1 = (i1 + i) * 59;
       i = (startTime == null)? 43: startTime.hashCode();
       String endTime = this.getEndTime();
       i1 = (i1 + i) * 59;
       i = (endTime == null)? 43: endTime.hashCode();
       String policyBasis = this.getPolicyBasis();
       i1 = (i1 + i) * 59;
       i = (policyBasis == null)? 43: policyBasis.hashCode();
       String datasources = this.getDatasources();
       i1 = (i1 + i) * 59;
       i = (datasources == null)? 43: datasources.hashCode();
       return (i1 + i);
    }
    public void setDatasources(String datasources){
       this.datasources = datasources;
    }
    public void setDischargeDepartment(String dischargeDepartment){
       this.dischargeDepartment = dischargeDepartment;
    }
    public void setEndTime(String endTime){
       this.endTime = endTime;
    }
    public void setMaxHospitalizationDays(Integer maxHospitalizationDays){
       this.maxHospitalizationDays = maxHospitalizationDays;
    }
    public void setMaxTotalMedicalAmount(Double maxTotalMedicalAmount){
       this.maxTotalMedicalAmount = maxTotalMedicalAmount;
    }
    public void setMinHospitalizationDays(Integer minHospitalizationDays){
       this.minHospitalizationDays = minHospitalizationDays;
    }
    public void setMinTotalMedicalAmount(Double minTotalMedicalAmount){
       this.minTotalMedicalAmount = minTotalMedicalAmount;
    }
    public void setPolicyBasis(String policyBasis){
       this.policyBasis = policyBasis;
    }
    public void setRuleName(String ruleName){
       this.ruleName = ruleName;
    }
    public void setRuleType(String ruleType){
       this.ruleType = ruleType;
    }
    public void setStartTime(String startTime){
       this.startTime = startTime;
    }
    public String toString(){
       return "MergeDTO\(ruleName="+this.getRuleName()+", ruleType="+this.getRuleType()+", dischargeDepartment="+this.getDischargeDepartment()+", minTotalMedicalAmount="+this.getMinTotalMedicalAmount()+", maxTotalMedicalAmount="+this.getMaxTotalMedicalAmount()+", minHospitalizationDays="+this.getMinHospitalizationDays()+", maxHospitalizationDays="+this.getMaxHospitalizationDays()+", startTime="+this.getStartTime()+", endTime="+this.getEndTime()+", policyBasis="+this.getPolicyBasis()+", datasources="+this.getDatasources()+"\)";
    }
}
