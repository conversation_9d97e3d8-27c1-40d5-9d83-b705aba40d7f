package com.taikang.fly.check.dto.mapstruct.YbDrugInstructionsMapping;
import com.taikang.fly.check.mybatis.domain.YbDrugInstructions;
import com.taikang.fly.check.dto.ybDrugInstructions.YbDrugInstructionsRespDto;
import java.util.List;

public interface abstract YbDrugInstructionsMapping	// class@000187 from classes.dex
{

    YbDrugInstructionsRespDto domainToRespDto(YbDrugInstructions p0);
    List domainsToRespDtoList(List p0);
}
